/* [project]/src/components/products/ProductCard/productCard.module.css [app-client] (css) */
.productCard-module__UIKE7W__productCard {
  background: #fff;
  border-radius: 8px;
  flex-direction: column;
  height: 100%;
  transition: transform .3s, box-shadow .3s;
  display: flex;
  overflow: hidden;
  box-shadow: 0 2px 8px #0000001a;
}

.productCard-module__UIKE7W__productCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px #00000026;
}

.productCard-module__UIKE7W__imageContainer {
  width: 100%;
  height: 250px;
  position: relative;
  overflow: hidden;
}

.productCard-module__UIKE7W__productImage {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform .3s;
}

.productCard-module__UIKE7W__productCard:hover .productCard-module__UIKE7W__productImage {
  transform: scale(1.05);
}

.productCard-module__UIKE7W__outOfStockOverlay {
  color: #fff;
  background: #000000b3;
  justify-content: center;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  position: absolute;
  inset: 0;
}

.productCard-module__UIKE7W__productInfo {
  flex-direction: column;
  flex: 1;
  padding: 1.5rem;
  display: flex;
}

.productCard-module__UIKE7W__productName {
  color: #1f2937;
  margin: 0 0 .75rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
}

.productCard-module__UIKE7W__productDescription {
  color: #4b5563;
  flex: 1;
  margin: 0 0 1rem;
  font-size: .9rem;
  line-height: 1.5;
}

.productCard-module__UIKE7W__priceContainer {
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: .75rem;
  display: flex;
}

.productCard-module__UIKE7W__priceSection {
  flex-direction: column;
  gap: .25rem;
  display: flex;
}

.productCard-module__UIKE7W__price {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 700;
}

.productCard-module__UIKE7W__wholesaleLabel {
  color: #059669;
  text-transform: uppercase;
  letter-spacing: .5px;
  font-size: .75rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__retailPrice {
  color: #6b7280;
  font-size: .875rem;
  text-decoration: line-through;
}

.productCard-module__UIKE7W__collection {
  color: #1e40af;
  align-self: flex-end;
  font-size: .85rem;
  font-style: italic;
}

.productCard-module__UIKE7W__stockInfo {
  margin-bottom: 1rem;
}

.productCard-module__UIKE7W__inStock {
  color: #059669;
  font-size: .85rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__outOfStock {
  color: #dc2626;
  font-size: .85rem;
  font-weight: 600;
}

.productCard-module__UIKE7W__actionButtons {
  flex-direction: column;
  gap: .75rem;
  margin-top: auto;
  display: flex;
}

.productCard-module__UIKE7W__viewDetailsBtn {
  color: #1e40af;
  background: none;
  border: 2px solid #1e40af;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-flex;
}

.productCard-module__UIKE7W__viewDetailsBtn:hover {
  color: #fff;
  background: #1e40af;
}

.productCard-module__UIKE7W__addToCartSection {
  flex-direction: column;
  gap: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__quantitySelector {
  justify-content: center;
  align-items: center;
  gap: .5rem;
  margin-bottom: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__quantityBtn {
  color: #1f2937;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-weight: 600;
  transition: all .2s;
  display: flex;
}

.productCard-module__UIKE7W__quantityBtn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #1e40af;
}

.productCard-module__UIKE7W__quantityBtn:disabled {
  opacity: .5;
  cursor: not-allowed;
}

.productCard-module__UIKE7W__quantity {
  text-align: center;
  color: #1f2937;
  min-width: 40px;
  font-weight: 600;
}

.productCard-module__UIKE7W__addToCartBtn {
  color: #fff;
  cursor: pointer;
  background: #1e40af;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  gap: .5rem;
  padding: .875rem 1rem;
  font-size: .9rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.productCard-module__UIKE7W__addToCartBtn:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.productCard-module__UIKE7W__addToCartBtn:disabled {
  opacity: .7;
  cursor: not-allowed;
  transform: none;
}

.productCard-module__UIKE7W__cartIcon {
  stroke-width: 2px;
  width: 18px;
  height: 18px;
}

.productCard-module__UIKE7W__loading {
  align-items: center;
  gap: .5rem;
  display: flex;
}

.productCard-module__UIKE7W__loading:after {
  content: "";
  border: 2px solid #0000;
  border-top-color: currentColor;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: 1s linear infinite productCard-module__UIKE7W__spin;
}

@keyframes productCard-module__UIKE7W__spin {
  to {
    transform: rotate(360deg);
  }
}

@media (width <= 768px) {
  .productCard-module__UIKE7W__productCard {
    margin-bottom: 1rem;
  }

  .productCard-module__UIKE7W__imageContainer {
    height: 200px;
  }

  .productCard-module__UIKE7W__productInfo {
    padding: 1rem;
  }

  .productCard-module__UIKE7W__productName {
    font-size: 1.1rem;
  }

  .productCard-module__UIKE7W__price {
    font-size: 1.25rem;
  }

  .productCard-module__UIKE7W__actionButtons {
    gap: .5rem;
  }

  .productCard-module__UIKE7W__addToCartSection {
    flex-direction: column;
  }
}

/*# sourceMappingURL=src_components_products_ProductCard_productCard_module_css_f9ee138c._.single.css.map*/