{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/TestimonialsSection/testimonialsSection.module.css"], "sourcesContent": ["/* Testimonials Section Styles */\r\n.testimonialsSection {\r\n  padding: 8rem 0;\r\n  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);\r\n  color: #ffffff;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.testimonialsSection::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('/images/testimonials-pattern.svg') repeat;\r\n  opacity: 0.05;\r\n  z-index: 1;\r\n}\r\n\r\n.container {\r\n  position: relative;\r\n  z-index: 2;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Header Styles */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 4rem;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.15em;\r\n  color: #white;\r\n  margin-bottom: 1rem;\r\n  display: block;\r\n}\r\n\r\n.title {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3.5rem;\r\n  font-weight: 700;\r\n  color: #ffffff;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.02em;\r\n  line-height: 1.1;\r\n}\r\n\r\n.description {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Testimonials Container */\r\n.testimonialsContainer {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr;\r\n  gap: 4rem;\r\n  align-items: center;\r\n  margin-bottom: 4rem;\r\n}\r\n\r\n/* Testimonial Content */\r\n.testimonialContent {\r\n  position: relative;\r\n}\r\n\r\n.quoteIcon {\r\n  color: #white;\r\n  margin-bottom: 2rem;\r\n  opacity: 0.7;\r\n}\r\n\r\n.testimonialText {\r\n  position: relative;\r\n}\r\n\r\n.testimonialContent {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  line-height: 1.6;\r\n  color: #ffffff;\r\n  margin-bottom: 2rem;\r\n  font-style: italic;\r\n  font-weight: 400;\r\n}\r\n\r\n.rating {\r\n  display: flex;\r\n  gap: 0.25rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.projectInfo {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.projectLabel {\r\n  color: #white;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n}\r\n\r\n.projectName {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Testimonial Meta */\r\n.testimonialMeta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2rem;\r\n}\r\n\r\n.authorInfo {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.authorImage {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  border: 3px solid #white;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.authorPhoto {\r\n  object-fit: cover;\r\n}\r\n\r\n.authorDetails {\r\n  flex: 1;\r\n}\r\n\r\n.authorName {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 0.25rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.authorTitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: #white;\r\n  font-weight: 500;\r\n  margin-bottom: 0.125rem;\r\n}\r\n\r\n.authorCompany {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 400;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n  justify-content: center;\r\n}\r\n\r\n.navDot {\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  border: none;\r\n  background: rgba(255, 255, 255, 0.3);\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.navDot:hover {\r\n  background: rgba(212, 175, 140, 0.7);\r\n  transform: scale(1.2);\r\n}\r\n\r\n.navDot.active {\r\n  background: #white;\r\n  transform: scale(1.3);\r\n}\r\n\r\n/* Stats */\r\n.stats {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr);\r\n  gap: 2rem;\r\n  padding-top: 3rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.stat {\r\n  text-align: center;\r\n}\r\n\r\n.statNumber {\r\n  display: block;\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  color: #white;\r\n  line-height: 1;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.statLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .testimonialsSection {\r\n    padding: 6rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 3rem;\r\n  }\r\n  \r\n  .testimonialsContainer {\r\n    gap: 3rem;\r\n  }\r\n  \r\n  .testimonialContent {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .testimonialsSection {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 3rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .description {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .testimonialsContainer {\r\n    grid-template-columns: 1fr;\r\n    gap: 2.5rem;\r\n    text-align: center;\r\n  }\r\n  \r\n  .testimonialContent {\r\n    font-size: 1.125rem;\r\n  }\r\n  \r\n  .authorInfo {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .stats {\r\n    grid-template-columns: repeat(2, 1fr);\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 2rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .testimonialContent {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .authorInfo {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .authorImage {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .stats {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .statNumber {\r\n    font-size: 2rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;;;AAYA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;AASA;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAQA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAKA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;AAUA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA"}}]}