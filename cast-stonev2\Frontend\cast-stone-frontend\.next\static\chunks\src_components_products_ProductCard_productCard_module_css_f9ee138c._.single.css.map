{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductCard/productCard.module.css"], "sourcesContent": ["/* Product Card Styles - Magazine/Editorial Theme */\r\n.productCard {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.productCard:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* Image Container */\r\n.imageContainer {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 250px;\r\n  overflow: hidden;\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.productCard:hover .productImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.outOfStockOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n/* Product Info */\r\n.productInfo {\r\n  padding: 1.5rem;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.productName {\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.75rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.productDescription {\r\n  color: #4b5563;\r\n  font-size: 0.9rem;\r\n  line-height: 1.5;\r\n  margin: 0 0 1rem 0;\r\n  flex: 1;\r\n}\r\n\r\n/* Price Container */\r\n.priceContainer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.priceSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.price {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.wholesaleLabel {\r\n  color: #059669;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.retailPrice {\r\n  color: #6b7280;\r\n  font-size: 0.875rem;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.collection {\r\n  color: #1e40af;\r\n  font-size: 0.85rem;\r\n  font-style: italic;\r\n  align-self: flex-end;\r\n}\r\n\r\n/* Stock Info */\r\n.stockInfo {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.inStock {\r\n  color: #059669;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.outOfStock {\r\n  color: #dc2626;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Action Buttons */\r\n.actionButtons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  margin-top: auto;\r\n}\r\n\r\n.viewDetailsBtn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.75rem 1rem;\r\n  background: transparent;\r\n  color: #1e40af;\r\n  border: 2px solid #1e40af;\r\n  border-radius: 4px;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.viewDetailsBtn:hover {\r\n  background: #1e40af;\r\n  color: white;\r\n}\r\n\r\n/* Add to Cart Section */\r\n.addToCartSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.quantitySelector {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.quantityBtn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: 1px solid #d1d5db;\r\n  background: white;\r\n  color: #1f2937;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.quantityBtn:hover:not(:disabled) {\r\n  background: #f3f4f6;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.quantityBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.quantity {\r\n  min-width: 40px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n}\r\n\r\n.addToCartBtn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 0.875rem 1rem;\r\n  background: #1e40af;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.addToCartBtn:hover:not(:disabled) {\r\n  background: #1d4ed8;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.addToCartBtn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.cartIcon {\r\n  width: 18px;\r\n  height: 18px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.loading {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.loading::after {\r\n  content: '';\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 2px solid transparent;\r\n  border-top: 2px solid currentColor;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .productCard {\r\n    margin-bottom: 1rem;\r\n  }\r\n  \r\n  .imageContainer {\r\n    height: 200px;\r\n  }\r\n  \r\n  .productInfo {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .productName {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .actionButtons {\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .addToCartSection {\r\n    flex-direction: column;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAOA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}