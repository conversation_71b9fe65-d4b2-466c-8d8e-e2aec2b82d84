(()=>{var e={};e.id=541,e.ids=[541],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13974:e=>{e.exports={container:"completedProjects_container__rjA5E",header:"completedProjects_header__9vmEP",title:"completedProjects_title__yJm_9",subtitle:"completedProjects_subtitle__NXnbH",content:"completedProjects_content__6fi4L",comingSoon:"completedProjects_comingSoon__ddXBf",icon:"completedProjects_icon__4K3iS",features:"completedProjects_features__pu_bb",feature:"completedProjects_feature__SPgWI"}},17886:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>d});var r=s(65239),o=s(48088),n=s(88170),a=s.n(n),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d={children:["",{children:["completed-projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,47733)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\completed-projects\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\completed-projects\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/completed-projects/page",pathname:"/completed-projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38995:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(60687);s(43210);var o=s(13974),n=s.n(o);function a(){return(0,r.jsxs)("div",{className:n().container,children:[(0,r.jsxs)("div",{className:n().header,children:[(0,r.jsx)("h1",{className:n().title,children:"Completed Projects"}),(0,r.jsx)("p",{className:n().subtitle,children:"Explore our portfolio of stunning cast stone installations and transformations"})]}),(0,r.jsx)("div",{className:n().content,children:(0,r.jsxs)("div",{className:n().comingSoon,children:[(0,r.jsx)("div",{className:n().icon,children:(0,r.jsx)("svg",{width:"64",height:"64",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,r.jsx)("h2",{children:"Coming Soon"}),(0,r.jsx)("p",{children:"We're currently curating our most impressive completed projects to showcase here. Check back soon to see beautiful installations, before & after transformations, and customer success stories."}),(0,r.jsxs)("div",{className:n().features,children:[(0,r.jsxs)("div",{className:n().feature,children:[(0,r.jsx)("h4",{children:"Project Galleries"}),(0,r.jsx)("p",{children:"High-quality photos of completed installations"})]}),(0,r.jsxs)("div",{className:n().feature,children:[(0,r.jsx)("h4",{children:"Case Studies"}),(0,r.jsx)("p",{children:"Detailed project breakdowns and customer testimonials"})]}),(0,r.jsxs)("div",{className:n().feature,children:[(0,r.jsx)("h4",{children:"Before & After"}),(0,r.jsx)("p",{children:"Transformation stories showcasing our craftsmanship"})]})]})]})})]})}},47733:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\completed-projects\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\completed-projects\\page.tsx","default")},56073:(e,t,s)=>{Promise.resolve().then(s.bind(s,38995))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65801:(e,t,s)=>{Promise.resolve().then(s.bind(s,47733))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,72,658,913],()=>s(17886));module.exports=r})();