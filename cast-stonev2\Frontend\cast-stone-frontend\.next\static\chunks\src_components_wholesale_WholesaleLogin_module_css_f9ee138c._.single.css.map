{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/wholesale/WholesaleLogin.module.css"], "sourcesContent": [".loginContainer {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 60vh;\n  padding: 2rem;\n}\n\n.loginCard {\n  width: 100%;\n  max-width: 400px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.loginHeader {\n  padding: 2rem 2rem 1rem;\n  text-align: center;\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  color: white;\n}\n\n.loginHeader h2 {\n  margin: 0 0 0.5rem;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.loginHeader p {\n  margin: 0;\n  font-size: 0.875rem;\n  opacity: 0.9;\n}\n\n.loginForm {\n  padding: 2rem;\n}\n\n.formGroup {\n  margin-bottom: 1.5rem;\n}\n\n.formGroup label {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #374151;\n  font-weight: 500;\n  font-size: 0.875rem;\n}\n\n.formGroup input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease, box-shadow 0.2s ease;\n}\n\n.formGroup input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.formGroup input.error {\n  border-color: #ef4444;\n}\n\n.formGroup input.error:focus {\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.formGroup input:disabled {\n  background-color: #f9fafb;\n  cursor: not-allowed;\n}\n\n.errorText {\n  display: block;\n  color: #ef4444;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n\n.loginButton {\n  width: 100%;\n  background: #3b82f6;\n  color: white;\n  border: none;\n  padding: 0.75rem;\n  border-radius: 6px;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  margin-top: 0.5rem;\n}\n\n.loginButton:hover:not(:disabled) {\n  background: #2563eb;\n}\n\n.loginButton:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n\n.loginFooter {\n  padding: 1.5rem 2rem 2rem;\n  text-align: center;\n  border-top: 1px solid #e5e7eb;\n  background: #f9fafb;\n}\n\n.loginFooter p {\n  margin: 0;\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.linkButton {\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  text-decoration: underline;\n  font-size: inherit;\n  padding: 0;\n  transition: color 0.2s ease;\n}\n\n.linkButton:hover:not(:disabled) {\n  color: #2563eb;\n}\n\n.linkButton:disabled {\n  color: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* Responsive Design */\n@media (max-width: 480px) {\n  .loginContainer {\n    padding: 1rem;\n  }\n  \n  .loginCard {\n    max-width: none;\n  }\n  \n  .loginHeader {\n    padding: 1.5rem 1.5rem 1rem;\n  }\n  \n  .loginForm {\n    padding: 1.5rem;\n  }\n  \n  .loginFooter {\n    padding: 1rem 1.5rem 1.5rem;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}