/* [project]/src/components/wholesale/WholesaleSignupForm.module.css [app-client] (css) */
.WholesaleSignupForm-module__HafJYa__formContainer {
  background: #fff;
  border-radius: 12px;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  box-shadow: 0 4px 6px #0000001a;
}

.WholesaleSignupForm-module__HafJYa__progressBar {
  margin-bottom: 3rem;
  padding: 0 2rem;
  position: relative;
}

.WholesaleSignupForm-module__HafJYa__progressSteps {
  z-index: 2;
  justify-content: space-between;
  display: flex;
  position: relative;
}

.WholesaleSignupForm-module__HafJYa__progressStep {
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__stepNumber {
  color: #6b7280;
  background: #e5e7eb;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__active .WholesaleSignupForm-module__HafJYa__stepNumber {
  color: #fff;
  background: #3b82f6;
}

.WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__completed .WholesaleSignupForm-module__HafJYa__stepNumber {
  color: #fff;
  background: #10b981;
}

.WholesaleSignupForm-module__HafJYa__stepLabel {
  color: #6b7280;
  font-size: .875rem;
  font-weight: 500;
}

.WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__active .WholesaleSignupForm-module__HafJYa__stepLabel, .WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__completed .WholesaleSignupForm-module__HafJYa__stepLabel {
  color: #374151;
}

.WholesaleSignupForm-module__HafJYa__progressLine {
  z-index: 1;
  background: #10b981;
  height: 2px;
  transition: width .3s;
  position: absolute;
  top: 20px;
  left: 2rem;
  right: 2rem;
}

.WholesaleSignupForm-module__HafJYa__progressLine:before {
  content: "";
  z-index: -1;
  background: #e5e7eb;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.WholesaleSignupForm-module__HafJYa__stepContent {
  margin-bottom: 2rem;
}

.WholesaleSignupForm-module__HafJYa__stepContent h3 {
  color: #1f2937;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.WholesaleSignupForm-module__HafJYa__formGroup {
  margin-bottom: 1.5rem;
}

.WholesaleSignupForm-module__HafJYa__formGroup label {
  color: #374151;
  margin-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  display: block;
}

.WholesaleSignupForm-module__HafJYa__formGroup input, .WholesaleSignupForm-module__HafJYa__formGroup select, .WholesaleSignupForm-module__HafJYa__formGroup textarea {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: 1rem;
  transition: border-color .2s, box-shadow .2s;
}

.WholesaleSignupForm-module__HafJYa__formGroup input:focus, .WholesaleSignupForm-module__HafJYa__formGroup select:focus, .WholesaleSignupForm-module__HafJYa__formGroup textarea:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.WholesaleSignupForm-module__HafJYa__formGroup input.WholesaleSignupForm-module__HafJYa__error, .WholesaleSignupForm-module__HafJYa__formGroup select.WholesaleSignupForm-module__HafJYa__error, .WholesaleSignupForm-module__HafJYa__formGroup textarea.WholesaleSignupForm-module__HafJYa__error {
  border-color: #ef4444;
}

.WholesaleSignupForm-module__HafJYa__formGroup input.WholesaleSignupForm-module__HafJYa__error:focus, .WholesaleSignupForm-module__HafJYa__formGroup select.WholesaleSignupForm-module__HafJYa__error:focus, .WholesaleSignupForm-module__HafJYa__formGroup textarea.WholesaleSignupForm-module__HafJYa__error:focus {
  box-shadow: 0 0 0 3px #ef44441a;
}

.WholesaleSignupForm-module__HafJYa__formRow {
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  display: grid;
}

.WholesaleSignupForm-module__HafJYa__formRow.WholesaleSignupForm-module__HafJYa__triple {
  grid-template-columns: 1fr 1fr 1fr;
}

.WholesaleSignupForm-module__HafJYa__errorText {
  color: #ef4444;
  margin-top: .25rem;
  font-size: .875rem;
  display: block;
}

.WholesaleSignupForm-module__HafJYa__checkboxGroup {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: .75rem;
  margin-top: .5rem;
  display: grid;
}

.WholesaleSignupForm-module__HafJYa__checkboxLabel {
  cursor: pointer;
  color: #374151;
  align-items: center;
  gap: .5rem;
  font-size: .875rem;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.WholesaleSignupForm-module__HafJYa__formActions {
  border-top: 1px solid #e5e7eb;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__primaryButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  min-width: 120px;
  padding: .75rem 2rem;
  font-weight: 600;
  transition: background-color .2s;
}

.WholesaleSignupForm-module__HafJYa__primaryButton:hover:not(:disabled) {
  background: #2563eb;
}

.WholesaleSignupForm-module__HafJYa__primaryButton:disabled {
  cursor: not-allowed;
  background: #9ca3af;
}

.WholesaleSignupForm-module__HafJYa__secondaryButton {
  color: #374151;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  min-width: 120px;
  padding: .75rem 2rem;
  font-weight: 600;
  transition: all .2s;
}

.WholesaleSignupForm-module__HafJYa__secondaryButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.WholesaleSignupForm-module__HafJYa__secondaryButton:disabled {
  color: #9ca3af;
  cursor: not-allowed;
  background: #f9fafb;
}

@media (width <= 768px) {
  .WholesaleSignupForm-module__HafJYa__formContainer {
    margin: 1rem;
    padding: 1.5rem;
  }

  .WholesaleSignupForm-module__HafJYa__progressBar {
    padding: 0 1rem;
  }

  .WholesaleSignupForm-module__HafJYa__formRow, .WholesaleSignupForm-module__HafJYa__formRow.WholesaleSignupForm-module__HafJYa__triple, .WholesaleSignupForm-module__HafJYa__checkboxGroup {
    grid-template-columns: 1fr;
  }

  .WholesaleSignupForm-module__HafJYa__formActions {
    flex-direction: column;
  }

  .WholesaleSignupForm-module__HafJYa__stepLabel {
    display: none;
  }
}

@media (width <= 480px) {
  .WholesaleSignupForm-module__HafJYa__formContainer {
    margin: .5rem;
    padding: 1rem;
  }

  .WholesaleSignupForm-module__HafJYa__progressSteps {
    justify-content: center;
    gap: 2rem;
  }

  .WholesaleSignupForm-module__HafJYa__stepNumber {
    width: 32px;
    height: 32px;
    font-size: .875rem;
  }
}

/*# sourceMappingURL=src_components_wholesale_WholesaleSignupForm_module_css_f9ee138c._.single.css.map*/