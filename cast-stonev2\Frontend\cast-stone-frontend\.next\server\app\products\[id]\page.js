(()=>{var e={};e.id=403,e.ids=[403],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42234:(e,a,t)=>{Promise.resolve().then(t.bind(t,44233))},44233:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\products\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\[id]\\page.tsx","default")},46808:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=t(65239),r=t(48088),o=t(88170),n=t.n(o),i=t(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(a,c);let l={children:["",{children:["products",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,44233)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\[id]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/products/[id]/page",pathname:"/products/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71269:e=>{e.exports={patinaSelector:"patinaSelector_patinaSelector__3_Zq6",selectorHeader:"patinaSelector_selectorHeader__Pc7QR",selectorTitle:"patinaSelector_selectorTitle__ISsQ3",selectedPatina:"patinaSelector_selectedPatina__Yy3Nn",patinaGrid:"patinaSelector_patinaGrid__xbIsm",patinaOption:"patinaSelector_patinaOption__po6pe",selected:"patinaSelector_selected__AV6Lp",patinaColor:"patinaSelector_patinaColor__DSXJx",patinaName:"patinaSelector_patinaName__gUsNS",patinaNote:"patinaSelector_patinaNote__xsjo2"}},79551:e=>{"use strict";e.exports=require("url")},82402:(e,a,t)=>{Promise.resolve().then(t.bind(t,92116))},92116:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>x});var s=t(60687),r=t(43210),o=t(16189);t(63968);var n=t(28253),i=t(22711),c=t(85592),l=t(34856),d=t(71269),p=t.n(d);let u=({selectedPatina:e,onPatinaChange:a})=>(0,s.jsxs)("div",{className:p().patinaSelector,children:[(0,s.jsxs)("div",{className:p().selectorHeader,children:[(0,s.jsx)("h3",{className:p().selectorTitle,children:"Select Patina"}),(0,s.jsx)("span",{className:p().selectedPatina,children:e})]}),(0,s.jsx)("div",{className:p().patinaGrid,children:[{name:"Alpine Stone",color:"#D4C4A8",description:"Light cream stone finish"},{name:"Aged Stone",color:"#B8A082",description:"Weathered natural stone"},{name:"Charcoal",color:"#5A5A5A",description:"Dark charcoal finish"},{name:"Limestone",color:"#E6DCC6",description:"Classic limestone color"},{name:"Sandstone",color:"#C9B299",description:"Warm sandstone tone"},{name:"Slate Gray",color:"#708090",description:"Cool slate gray"},{name:"Terra Cotta",color:"#B87333",description:"Earthy terra cotta"},{name:"Antique White",color:"#F5F5DC",description:"Soft antique white"},{name:"Weathered Bronze",color:"#8B7355",description:"Bronze patina finish"},{name:"Natural Stone",color:"#A0A0A0",description:"Natural stone gray"},{name:"Moss Green",color:"#8FBC8F",description:"Subtle moss green"},{name:"Rust",color:"#B7410E",description:"Oxidized rust finish"}].map(t=>(0,s.jsxs)("button",{className:`${p().patinaOption} ${e===t.name?p().selected:""}`,onClick:()=>a(t.name),title:`${t.name} - ${t.description}`,"aria-label":`Select ${t.name} patina`,children:[(0,s.jsx)("div",{className:p().patinaColor,style:{backgroundColor:t.color}}),(0,s.jsx)("span",{className:p().patinaName,children:t.name})]},t.name))}),(0,s.jsx)("div",{className:p().patinaNote,children:(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Note:"})," Patina colors are representative. Actual finish may vary due to the handcrafted nature of cast stone. Contact us for physical samples."]})})]});var _=t(94704),m=t(97827),h=t.n(m);function x(){parseInt((0,o.useParams)().id);let{addToCart:e}=(0,n._)(),{isApprovedWholesaleBuyer:a}=(0,i.u)(),[t,d]=(0,r.useState)(null),[p,m]=(0,r.useState)([]),[x,g]=(0,r.useState)(!0),[S,j]=(0,r.useState)(null),[v,P]=(0,r.useState)(1),[f,N]=(0,r.useState)("Alpine Stone"),[b,C]=(0,r.useState)(!1),y=async()=>{if(t)try{C(!0),await e(t.id,v)}catch(e){console.error("Error adding to cart:",e)}finally{C(!1)}},w=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),q=a&&t?.wholeSalePrice?t.wholeSalePrice:t?.price||0,k=a&&t?.wholeSalePrice;if(x)return(0,s.jsxs)("div",{className:h().loadingContainer,children:[(0,s.jsx)("div",{className:h().loadingSpinner}),(0,s.jsx)("p",{children:"Loading product details..."})]});if(S||!t)return(0,s.jsxs)("div",{className:h().errorContainer,children:[(0,s.jsx)("h1",{children:"Product Not Found"}),(0,s.jsx)("p",{children:S||"The requested product could not be found."})]});let A=t.stock>0;return(0,s.jsx)("div",{className:h().productPage,children:(0,s.jsxs)("div",{className:h().container,children:[(0,s.jsxs)("div",{className:h().productMain,children:[(0,s.jsx)("div",{className:h().imageSection,children:(0,s.jsx)(c.A,{images:t.images,productName:t.name})}),(0,s.jsxs)("div",{className:h().detailsSection,children:[(0,s.jsx)("h1",{className:h().productTitle,children:t.name}),(0,s.jsxs)("div",{className:h().productCode,children:["Product Code: ",t.productCode||`P-${t.id.toString().padStart(3,"0")}-AS`]}),t.productSpecifications&&(0,s.jsxs)(s.Fragment,{children:[console.log("Full productSpecifications object:",t.productSpecifications),(0,s.jsxs)("div",{className:h().keySpecsTable,children:[(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Availability:"}),(0,s.jsx)("span",{className:h().value,children:A?"In Stock":"Out of Stock"})]}),t.productSpecifications.pieces&&(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Pieces:"}),(0,s.jsx)("span",{className:h().value,children:t.productSpecifications.pieces})]}),t.productSpecifications.material&&(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Material:"}),(0,s.jsx)("span",{className:h().value,children:t.productSpecifications.material})]}),t.productSpecifications.dimensions&&(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Dimensions:"}),(0,s.jsx)("span",{className:h().value,children:t.productSpecifications.dimensions})]}),t.productSpecifications.totalWeight&&(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Total Weight:"}),(0,s.jsx)("span",{className:h().value,children:t.productSpecifications.totalWeight})]}),t.productSpecifications.photographed_In&&(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Photographed In:"}),(0,s.jsx)("span",{className:h().value,children:t.productSpecifications.photographed_In})]}),t.productSpecifications.base_Dimensions&&(0,s.jsxs)("div",{className:h().specRow,children:[(0,s.jsx)("span",{className:h().label,children:"Base Dimensions:"}),(0,s.jsx)("span",{className:h().value,children:t.productSpecifications.base_Dimensions})]})]})]}),(0,s.jsx)("div",{className:h().priceSection,children:(0,s.jsxs)("div",{className:h().priceRow,children:[(0,s.jsxs)("div",{className:h().priceDisplay,children:[(0,s.jsx)("span",{className:h().price,children:w(q)}),k&&(0,s.jsx)("span",{className:h().wholesaleLabel,children:"Wholesale Price"}),a&&t.wholeSalePrice&&(0,s.jsxs)("span",{className:h().retailPrice,children:["Retail: ",w(t.price)]})]}),(0,s.jsx)("span",{children:(0,s.jsx)("span",{})}),(0,s.jsxs)("div",{className:h().quantitySelector,children:[(0,s.jsx)("label",{htmlFor:"quantity",children:"Quantity:"}),(0,s.jsxs)("div",{className:h().quantityControls,children:[(0,s.jsx)("button",{type:"button",onClick:()=>P(Math.max(1,v-1)),disabled:v<=1,className:h().quantityBtn,children:"-"}),(0,s.jsx)("input",{id:"quantity",type:"number",value:v,onChange:e=>P(Math.max(1,parseInt(e.target.value)||1)),min:"1",max:t.stock,className:h().quantityInput}),(0,s.jsx)("button",{type:"button",onClick:()=>P(Math.min(t.stock,v+1)),disabled:v>=t.stock,className:h().quantityBtn,children:"+"})]})]}),(0,s.jsx)("div",{className:h().addToCartRow,children:(0,s.jsx)("div",{className:h().addToCartLabel,children:(0,s.jsx)("button",{onClick:y,disabled:b,className:h().addToCartBtn,children:b?"Adding...":"Add to Cart"})})})]})}),(0,s.jsx)(u,{selectedPatina:f,onPatinaChange:N}),(0,s.jsx)(l.A,{product:t})]})]}),(0,s.jsx)(_.A,{products:p})]})})}},97827:e=>{e.exports={productPage:"productPage_productPage__VAIHg",container:"productPage_container__fWpuu",loadingContainer:"productPage_loadingContainer__rsh4p",loadingSpinner:"productPage_loadingSpinner__X6sGx",spin:"productPage_spin__QA2_m",errorContainer:"productPage_errorContainer__kkQVp",productMain:"productPage_productMain__y_iUi",imageSection:"productPage_imageSection___rJpK",detailsSection:"productPage_detailsSection__JezZs",productTitle:"productPage_productTitle__cDwGB",productCode:"productPage_productCode__1eqGS",productInfo:"productPage_productInfo__j8iG4",infoRow:"productPage_infoRow__9Ipt_",infoLabel:"productPage_infoLabel__9_bEi",infoValue:"productPage_infoValue__s26Eo",priceSection:"productPage_priceSection__iD3Mp",priceRow:"productPage_priceRow___Zu_5",priceDisplay:"productPage_priceDisplay__o8a0x",purchaseSection:"productPage_purchaseSection___gWD1",price:"productPage_price__fRB_W",wholesaleLabel:"productPage_wholesaleLabel__yeDg2",retailPrice:"productPage_retailPrice__6OlWf",quantitySelector:"productPage_quantitySelector__QSASD",quantityControls:"productPage_quantityControls__2ogyS",quantityBtn:"productPage_quantityBtn__cDN14",quantityInput:"productPage_quantityInput__14t0u",addToCartRow:"productPage_addToCartRow__Yo19t",addToCartLabel:"productPage_addToCartLabel__jl_BC",addToCartWrapper:"productPage_addToCartWrapper__K0rhj",addToCartBtn:"productPage_addToCartBtn__xqat7",keySpecsTable:"productPage_keySpecsTable__bS7QG",specRow:"productPage_specRow__GbJrY",label:"productPage_label__Xiekh",value:"productPage_value__ORBR5",rightSection:"productPage_rightSection__W8yYj"}}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,72,658,913,453],()=>t(46808));module.exports=s})();