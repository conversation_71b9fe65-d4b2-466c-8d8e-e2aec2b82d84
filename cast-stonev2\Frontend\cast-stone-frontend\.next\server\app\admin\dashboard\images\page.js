(()=>{var e={};e.id=800,e.ids=[800],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27326:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["admin",{children:["dashboard",{children:["images",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48104)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\images\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\images\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/images/page",pathname:"/admin/dashboard/images",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48104:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\images\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\images\\page.tsx","default")},48702:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687),a=r(43210),n=r(83645),o=r(73441),l=r(97629);function i(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[d,c]=(0,a.useState)(!1),[m,p]=(0,a.useState)([]),[x,h]=(0,a.useState)(null),[u,b]=(0,a.useState)(null),g=(0,a.useRef)(null),v=async()=>{try{i(!0);let e=await l.Y.getAllImages();t(e)}catch(e){console.error("Error fetching images:",e),h("Failed to load images. Please try again.")}finally{i(!1)}},f=async e=>{c(!0),h(null),b(null),p([]);try{let t=[];if(e.forEach((e,r)=>{let s=l.Y.validateImageFile(e);s.isValid||t.push(`File ${r+1} (${e.name}): ${s.error}`)}),t.length>0)return void h(t.join("\n"));let r=[];for(let t=0;t<e.length;t++){let s=e[t];p(e=>[...e,`Uploading ${s.name}...`]);try{let e=await l.Y.uploadImage(s);r.push(e.imageUrl),p(e=>e.map((e,r)=>r===t?`✓ ${s.name} uploaded successfully`:e))}catch(e){p(e=>e.map((e,r)=>r===t?`✗ Failed to upload ${s.name}`:e))}}r.length>0&&(b(`Successfully uploaded ${r.length} image(s)`),await v())}catch(e){console.error("Error uploading images:",e),h("Failed to upload images. Please try again.")}finally{c(!1),p([]),g.current&&(g.current.value="")}},j=async(e,t)=>{if(confirm(`Are you sure you want to delete "${t}"? This action cannot be undone.`))try{await l.Y.deleteImage(e),b(`Successfully deleted "${t}"`),await v()}catch(e){console.error("Error deleting image:",e),h(`Failed to delete "${t}". Please try again.`)}},y=async e=>{try{await navigator.clipboard.writeText(e),b("Image URL copied to clipboard!"),setTimeout(()=>b(null),3e3)}catch(e){console.error("Error copying to clipboard:",e),h("Failed to copy URL to clipboard")}};return(0,s.jsx)(n.A,{children:(0,s.jsx)(o.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-amber-900 mb-2",children:"Image Management"}),(0,s.jsx)("p",{className:"text-amber-700",children:"Upload and manage images for your products and collections. Images are stored securely in the cloud."})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-amber-900 mb-4",children:"Upload Images"}),(0,s.jsx)("div",{className:"border-2 border-dashed border-amber-300 rounded-lg p-8 text-center hover:border-amber-400 transition-colors",onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:e=>{e.preventDefault(),e.stopPropagation();let t=Array.from(e.dataTransfer.files);t.length>0&&f(t)},children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-amber-600",children:(0,s.jsx)("svg",{className:"mx-auto h-12 w-12",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,s.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg text-amber-800",children:"Drag and drop images here, or"}),(0,s.jsx)("button",{type:"button",onClick:()=>g.current?.click(),disabled:d,className:"mt-2 px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Uploading...":"Choose Files"})]}),(0,s.jsx)("p",{className:"text-sm text-amber-600",children:"Supports JPEG, PNG, GIF, and WebP files up to 10MB each"})]})}),(0,s.jsx)("input",{ref:g,type:"file",multiple:!0,accept:"image/*",onChange:e=>{let t=e.target.files;t&&t.length>0&&f(Array.from(t))},className:"hidden"}),m.length>0&&(0,s.jsxs)("div",{className:"mt-4 p-4 bg-amber-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-medium text-amber-900 mb-2",children:"Upload Progress:"}),(0,s.jsx)("div",{className:"space-y-1",children:m.map((e,t)=>(0,s.jsx)("p",{className:"text-sm text-amber-700",children:e},t))})]})]}),x&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,s.jsx)("p",{className:"text-red-800 whitespace-pre-line",children:x}),(0,s.jsx)("button",{onClick:()=>h(null),className:"mt-2 text-sm text-red-600 hover:text-red-800",children:"Dismiss"})]}),u&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,s.jsx)("p",{className:"text-green-800",children:u}),(0,s.jsx)("button",{onClick:()=>b(null),className:"mt-2 text-sm text-green-600 hover:text-green-800",children:"Dismiss"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-amber-900",children:["Uploaded Images (",e.length,")"]}),(0,s.jsx)("button",{onClick:v,disabled:r,className:"px-4 py-2 bg-amber-100 text-amber-800 rounded-md hover:bg-amber-200 disabled:opacity-50",children:r?"Loading...":"Refresh"})]}),r?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-amber-900"}),(0,s.jsx)("p",{className:"mt-2 text-amber-700",children:"Loading images..."})]}):0===e.length?(0,s.jsx)("div",{className:"text-center py-8 text-amber-600",children:(0,s.jsx)("p",{children:"No images uploaded yet. Upload your first image above!"})}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-amber-200",children:[(0,s.jsx)("thead",{className:"bg-amber-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Image"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"File Name"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"URL"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Uploaded"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-amber-200",children:e.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-amber-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("img",{src:e.secureUrl,alt:e.fileName,className:"h-16 w-16 object-cover rounded-lg border border-amber-200"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-amber-900",children:e.fileName}),(0,s.jsx)("div",{className:"text-sm text-amber-600",children:e.publicId})]}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-amber-800 max-w-xs truncate",title:e.secureUrl,children:e.secureUrl})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-amber-600",children:new Date(e.createdAt).toLocaleDateString()}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,s.jsx)("button",{onClick:()=>y(e.secureUrl),className:"text-amber-600 hover:text-amber-900",title:"Copy URL",children:"Copy URL"}),(0,s.jsx)("button",{onClick:()=>j(e.publicId,e.fileName),className:"text-red-600 hover:text-red-900",title:"Delete Image",children:"Delete"})]})]},e.publicId))})]})})]})]})})})}},51182:(e,t,r)=>{Promise.resolve().then(r.bind(r,48104))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},90934:(e,t,r)=>{Promise.resolve().then(r.bind(r,48702))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,72,658,913,190],()=>r(27326));module.exports=s})();