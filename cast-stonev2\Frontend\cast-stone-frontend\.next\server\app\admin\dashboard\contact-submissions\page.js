(()=>{var e={};e.id=162,e.ids=[162],e.modules={1255:(e,s,t)=>{Promise.resolve().then(t.bind(t,41210))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3844:(e,s,t)=>{"use strict";t.d(s,{AdminAuthProvider:()=>i,b:()=>d});var r=t(60687),a=t(43210),n=t(58890);let o=(0,a.createContext)(void 0);function i({children:e}){let[s,t]=(0,a.useState)(null),[i,d]=(0,a.useState)(!0),l=async(e,s)=>{d(!0);try{let r=await n.userGetService.validateAdminCredentials(e,s);if(r)return t(r),localStorage.setItem("admin_session",JSON.stringify(r)),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{d(!1)}};return(0,r.jsx)(o.Provider,{value:{admin:s,isLoading:i,login:l,logout:()=>{t(null),localStorage.removeItem("admin_session")},isAuthenticated:!!s},children:e})}function d(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAdminAuth must be used within an AdminAuthProvider");return e}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17378:(e,s,t)=>{Promise.resolve().then(t.bind(t,38596))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35530:(e,s,t)=>{Promise.resolve().then(t.bind(t,53666))},38596:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\contact-submissions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\contact-submissions\\page.tsx","default")},40575:(e,s,t)=>{Promise.resolve().then(t.bind(t,3844))},41108:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>l});var r=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let l={children:["",{children:["admin",{children:["dashboard",{children:["contact-submissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38596)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\contact-submissions\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\contact-submissions\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/contact-submissions/page",pathname:"/admin/dashboard/contact-submissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},41210:(e,s,t)=>{"use strict";t.d(s,{AdminAuthProvider:()=>a});var r=t(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AdminAuthProvider() from the server but AdminAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx","AdminAuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAdminAuth() from the server but useAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx","useAdminAuth")},53666:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(60687),a=t(43210),n=t(83645),o=t(73441),i=t(21152);function d(){let[e,s]=(0,a.useState)([]),[t,d]=(0,a.useState)(!0),[l,c]=(0,a.useState)(null),[m,h]=(0,a.useState)(""),[u,x]=(0,a.useState)(""),p=async()=>{try{d(!0),c(null);let e=await i.contactFormGetService.getAll();s(e)}catch(e){c(e instanceof Error?e.message:"Failed to fetch contact submissions")}finally{d(!1)}},b=e.filter(e=>{let s=""===m||e.name.toLowerCase().includes(m.toLowerCase())||e.email.toLowerCase().includes(m.toLowerCase())||e.company?.toLowerCase().includes(m.toLowerCase())||e.message.toLowerCase().includes(m.toLowerCase()),t=""===u||e.inquiryDisplayName===u;return s&&t}),v=Array.from(new Set(e.map(e=>e.inquiryDisplayName))),f=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return t?(0,r.jsx)(n.A,{children:(0,r.jsx)(o.A,{children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900"})})})}):(0,r.jsx)(n.A,{children:(0,r.jsx)(o.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-amber-900 mb-2",children:"Contact Form Submissions"}),(0,r.jsx)("p",{className:"text-amber-700",children:"View and manage all contact form submissions from your website."})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-amber-900 mb-2",children:"Search Submissions"}),(0,r.jsx)("input",{type:"text",id:"search",value:m,onChange:e=>h(e.target.value),placeholder:"Search by name, email, company, or message...",className:"w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent text-amber-900"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"inquiry",className:"block text-sm font-medium text-amber-900 mb-2",children:"Filter by Inquiry Type"}),(0,r.jsxs)("select",{id:"inquiry",value:u,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-amber-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent text-amber-900",children:[(0,r.jsx)("option",{value:"",children:"All Inquiry Types"}),v.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]})]})}),l&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,r.jsx)("p",{className:"text-red-800",children:l}),(0,r.jsx)("button",{onClick:p,className:"mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Retry"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-amber-200 overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-amber-200",children:(0,r.jsxs)("h2",{className:"text-xl font-semibold text-amber-900",children:["Submissions (",b.length,")"]})}),0===b.length?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("p",{className:"text-amber-600",children:m||u?"No submissions match your filters.":"No contact submissions found."})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-amber-200",children:[(0,r.jsx)("thead",{className:"bg-amber-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Contact Info"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Company & State"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Inquiry Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Message"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-amber-900 uppercase tracking-wider",children:"Submitted"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-amber-200",children:b.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-amber-50 transition-colors",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"font-medium text-amber-900",children:e.name}),(0,r.jsx)("div",{className:"text-amber-600",children:e.email}),(0,r.jsx)("div",{className:"text-amber-600",children:e.phoneNumber})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"text-amber-900",children:e.company||"N/A"}),(0,r.jsx)("div",{className:"text-amber-600",children:e.state})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-amber-100 text-amber-800",children:e.inquiryDisplayName})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-amber-900 max-w-xs",children:(0,r.jsx)("div",{className:"truncate",title:e.message,children:e.message.length>100?`${e.message.substring(0,100)}...`:e.message})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-amber-600",children:f(e.createdAt)})]},e.id))})]})})]})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73441:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(43210),n=t(16189),o=t(3844);function i({children:e}){let[s,t]=(0,a.useState)(!0),{admin:i,logout:d}=(0,o.b)(),l=(0,n.useRouter)(),c=(0,n.usePathname)(),m=[{name:"Dashboard",href:"/admin/dashboard",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"})})},{name:"Collections",href:"/admin/dashboard/collections",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})},{name:"Products",href:"/admin/dashboard/products",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})},{name:"Images",href:"/admin/dashboard/images",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})},{name:"Orders",href:"/admin/dashboard/orders",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{name:"Contact Submissions",href:"/admin/dashboard/contact-submissions",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})},{name:"Wholesale Buyers",href:"/admin/dashboard/wholesale-buyers",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}];return(0,r.jsxs)("div",{className:"flex h-screen bg-black",children:[(0,r.jsxs)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${s?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 border-r border-white`,children:[(0,r.jsx)("div",{className:"flex items-center justify-center h-16 px-4 bg-black",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Cast Stone Admin"})}),(0,r.jsx)("nav",{className:"mt-8",children:(0,r.jsx)("div",{className:"px-4 space-y-2",children:m.map(e=>{let s=c===e.href;return(0,r.jsxs)("a",{href:e.href,className:`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${s?"bg-amber-100 text-amber-900 border-r-2 border-amber-900":"text-amber-800 hover:bg-amber-50 hover:text-amber-900"}`,children:[e.icon,(0,r.jsx)("span",{className:"ml-3",children:e.name})]},e.name)})})})]}),(0,r.jsxs)("div",{className:"lg:pl flex flex-col flex-1 h-full",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-black",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>t(!s),className:"lg:hidden p-2 rounded-md text-black hover:text-black hover:bg-black",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,r.jsx)("h2",{className:"ml-4 text-2xl font-bold text-black",children:m.find(e=>e.href===c)?.name||"Dashboard"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-sm text-black",children:["Welcome, ",(0,r.jsx)("span",{className:"font-semibold text-black",children:i?.email})]}),(0,r.jsx)("button",{onClick:()=>{d(),l.push("/admin/login")},className:"px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-black-800 transition-colors shadow-sm",children:"Logout"})]})]})}),(0,r.jsx)("main",{className:"flex-1 p-6 bg-white",children:e})]}),s&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-white bg-opacity-75 lg:hidden",onClick:()=>t(!1)})]})}},79551:e=>{"use strict";e.exports=require("url")},83645:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(60687);t(43210);var a=t(16189),n=t(3844);function o({children:e}){let{isAuthenticated:s,isLoading:t}=(0,n.b)();return((0,a.useRouter)(),t)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-amber-900",children:"Loading..."})]})}):s?(0,r.jsx)(r.Fragment,{children:e}):null}},99111:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>n});var r=t(37413),a=t(41210);let n={title:"Cast Stone Admin - Dashboard",description:"Admin dashboard for Cast Stone management"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:"antialiased bg-gray-50",children:(0,r.jsx)(a.AdminAuthProvider,{children:e})})})}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,72,658,913],()=>t(41108));module.exports=r})();