(()=>{var e={};e.id=222,e.ids=[222],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6017:(e,t,r)=>{Promise.resolve().then(r.bind(r,89360))},7229:(e,t,r)=>{"use strict";r.d(t,{qo:()=>h});var o=r(60687),c=r(43210),n=r(85814),a=r.n(n),i=r(28253),s=r(22711),l=r(52576),d=r.n(l);let p=({product:e,showAddToCart:t=!0,showViewDetails:r=!0})=>{let{addToCart:n,state:l}=(0,i._)(),{isApprovedWholesaleBuyer:p}=(0,s.u)(),[_,u]=(0,c.useState)(!1),[h,m]=(0,c.useState)(1),g=async()=>{try{u(!0),await n(e.id,h)}catch(e){console.error("Error adding to cart:",e)}finally{u(!1)}},x=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),v=p&&e.wholeSalePrice?e.wholeSalePrice:e.price,j=p&&e.wholeSalePrice,C=e.images&&e.images.length>0?e.images[0]:"/images/placeholder-product.jpg",N=e.stock>0;return(0,o.jsxs)("div",{className:d().productCard,children:[(0,o.jsxs)("div",{className:d().imageContainer,children:[(0,o.jsx)("img",{src:C,alt:e.name,className:d().productImage}),!N&&(0,o.jsx)("div",{className:d().outOfStockOverlay,children:(0,o.jsx)("span",{children:"Out of Stock"})})]}),(0,o.jsxs)("div",{className:d().productInfo,children:[(0,o.jsx)("h3",{className:d().productName,children:e.name}),e.description&&(0,o.jsx)("p",{className:d().productDescription,children:e.description.length>100?`${e.description.substring(0,100)}...`:e.description}),(0,o.jsxs)("div",{className:d().priceContainer,children:[(0,o.jsxs)("div",{className:d().priceSection,children:[(0,o.jsx)("span",{className:d().price,children:x(v)}),j&&(0,o.jsx)("span",{className:d().wholesaleLabel,children:"Wholesale Price"}),p&&e.wholeSalePrice&&(0,o.jsxs)("span",{className:d().retailPrice,children:["Retail: ",x(e.price)]})]}),e.collection&&(0,o.jsx)("span",{className:d().collection,children:e.collection.name})]}),(0,o.jsx)("div",{className:d().stockInfo,children:N?(0,o.jsx)("span",{className:d().inStock,children:e.stock>10?"In Stock":`Only ${e.stock} left`}):(0,o.jsx)("span",{className:d().outOfStock,children:"Out of Stock"})}),(0,o.jsxs)("div",{className:d().actionButtons,children:[r&&(0,o.jsx)(a(),{href:`/products/${e.id}`,className:d().viewDetailsBtn,children:"View Details"}),t&&N&&(0,o.jsxs)("div",{className:d().addToCartSection,children:[(0,o.jsxs)("div",{className:d().quantitySelector,children:[(0,o.jsx)("button",{type:"button",onClick:()=>m(Math.max(1,h-1)),className:d().quantityBtn,disabled:h<=1,children:"-"}),(0,o.jsx)("span",{className:d().quantity,children:h}),(0,o.jsx)("button",{type:"button",onClick:()=>m(Math.min(e.stock,h+1)),className:d().quantityBtn,disabled:h>=e.stock,children:"+"})]}),(0,o.jsx)("button",{onClick:g,disabled:_||l.isLoading,className:d().addToCartBtn,children:_?(0,o.jsx)("span",{className:d().loading,children:"Adding..."}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("svg",{className:d().cartIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,o.jsx)("path",{d:"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7"})}),"Add to Cart"]})})]})]})]})]})};var _=r(48335),u=r.n(_);let h=({products:e,isLoading:t=!1,showAddToCart:r=!0,showViewDetails:c=!0,emptyMessage:n="No products found."})=>t?(0,o.jsx)("div",{className:u().loadingContainer,children:(0,o.jsx)("div",{className:u().loadingGrid,children:Array.from({length:6}).map((e,t)=>(0,o.jsxs)("div",{className:u().loadingCard,children:[(0,o.jsx)("div",{className:u().loadingImage}),(0,o.jsxs)("div",{className:u().loadingContent,children:[(0,o.jsx)("div",{className:u().loadingTitle}),(0,o.jsx)("div",{className:u().loadingDescription}),(0,o.jsx)("div",{className:u().loadingPrice}),(0,o.jsx)("div",{className:u().loadingButton})]})]},t))})}):0===e.length?(0,o.jsxs)("div",{className:u().emptyContainer,children:[(0,o.jsx)("div",{className:u().emptyIcon,children:(0,o.jsxs)("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[(0,o.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,o.jsx)("path",{d:"M16 16s-1.5-2-4-2-4 2-4 2"}),(0,o.jsx)("line",{x1:"9",y1:"9",x2:"9.01",y2:"9"}),(0,o.jsx)("line",{x1:"15",y1:"9",x2:"15.01",y2:"9"})]})}),(0,o.jsx)("h3",{className:u().emptyTitle,children:"No Products Found"}),(0,o.jsx)("p",{className:u().emptyMessage,children:n})]}):(0,o.jsx)("div",{className:u().productGrid,children:e.map(e=>(0,o.jsx)(p,{product:e,showAddToCart:r,showViewDetails:c},e.id))});r(85592),r(34856),r(94704)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11422:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>_,tree:()=>l});var o=r(65239),c=r(48088),n=r(88170),a=r.n(n),i=r(30893),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);r.d(t,s);let l={children:["",{children:["collections",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69414)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\[id]\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},_=new o.AppPageRouteModule({definition:{kind:c.RouteKind.APP_PAGE,page:"/collections/[id]/page",pathname:"/collections/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48335:e=>{e.exports={productGrid:"productGrid_productGrid__ZMmDP",loadingContainer:"productGrid_loadingContainer__I6lQs",loadingGrid:"productGrid_loadingGrid__pGK1K",loadingCard:"productGrid_loadingCard__Y1WSl",pulse:"productGrid_pulse__AKxo6",loadingImage:"productGrid_loadingImage__7vn6G",shimmer:"productGrid_shimmer__lwSwM",loadingContent:"productGrid_loadingContent__9lBxI",loadingTitle:"productGrid_loadingTitle__cc1cI",loadingDescription:"productGrid_loadingDescription__d_ugA",loadingPrice:"productGrid_loadingPrice___QJtz",loadingButton:"productGrid_loadingButton__g0tro",emptyContainer:"productGrid_emptyContainer__zAwbL",emptyIcon:"productGrid_emptyIcon__8RKzk",emptyTitle:"productGrid_emptyTitle__HLxmW",emptyMessage:"productGrid_emptyMessage__uACmE"}},52576:e=>{e.exports={productCard:"productCard_productCard__xI8yk",imageContainer:"productCard_imageContainer__iDB5f",productImage:"productCard_productImage__GNX_E",outOfStockOverlay:"productCard_outOfStockOverlay___PQEf",productInfo:"productCard_productInfo__iWv4n",productName:"productCard_productName__rwmUL",productDescription:"productCard_productDescription__asG12",priceContainer:"productCard_priceContainer__Tw4q6",priceSection:"productCard_priceSection__K5asJ",price:"productCard_price__obPDJ",wholesaleLabel:"productCard_wholesaleLabel__qv_KS",retailPrice:"productCard_retailPrice__edSAq",collection:"productCard_collection__rpw0H",stockInfo:"productCard_stockInfo__TFFt9",inStock:"productCard_inStock__IqfYV",outOfStock:"productCard_outOfStock__2N_WF",actionButtons:"productCard_actionButtons__wVj_4",viewDetailsBtn:"productCard_viewDetailsBtn__657_H",addToCartSection:"productCard_addToCartSection__OXbtp",quantitySelector:"productCard_quantitySelector__GmTbx",quantityBtn:"productCard_quantityBtn__x8ZxX",quantity:"productCard_quantity__SxGKR",addToCartBtn:"productCard_addToCartBtn__YogtN",cartIcon:"productCard_cartIcon__xaTFW",loading:"productCard_loading__pInZC",spin:"productCard_spin__BTkaQ"}},58575:e=>{e.exports={collectionPage:"collectionPage_collectionPage__dpWwW",container:"collectionPage_container___A3BI",loadingContainer:"collectionPage_loadingContainer__WBWJh",loadingSpinner:"collectionPage_loadingSpinner__t6N6G",spin:"collectionPage_spin__AaW8Y",errorContainer:"collectionPage_errorContainer__CNrhZ",collectionHeader:"collectionPage_collectionHeader__7uNhW",headerContent:"collectionPage_headerContent__fdNxH",collectionTitle:"collectionPage_collectionTitle__6w_wT",collectionDescription:"collectionPage_collectionDescription__RlIUg",collectionMeta:"collectionPage_collectionMeta__XNauU",productCount:"collectionPage_productCount__Duq3_",collectionLevel:"collectionPage_collectionLevel__Rw6wB",filterSection:"collectionPage_filterSection__Ts87H",searchBar:"collectionPage_searchBar__M3c5x",searchInput:"collectionPage_searchInput__FNDqe",searchIcon:"collectionPage_searchIcon__k6QGR",searchField:"collectionPage_searchField__yLznb",filterToggle:"collectionPage_filterToggle__EM7PK",active:"collectionPage_active__2osrc",advancedFilters:"collectionPage_advancedFilters__Jbs1v",filterGrid:"collectionPage_filterGrid__Stgcz",filterGroup:"collectionPage_filterGroup__YkSpj",filterLabel:"collectionPage_filterLabel__Jitrc",priceRange:"collectionPage_priceRange__A7LrI",priceInput:"collectionPage_priceInput__tFv2n",checkboxLabel:"collectionPage_checkboxLabel__5NEtD",checkbox:"collectionPage_checkbox__4dK_G",sortControls:"collectionPage_sortControls__wBBIW",sortSelect:"collectionPage_sortSelect__gT3YY",sortDirection:"collectionPage_sortDirection__huoJQ",desc:"collectionPage_desc__B6Uht",clearFilters:"collectionPage_clearFilters__vVC6G",productsSection:"collectionPage_productsSection__s5zI7"}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69414:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\[id]\\page.tsx","default")},69569:(e,t,r)=>{Promise.resolve().then(r.bind(r,69414))},79551:e=>{"use strict";e.exports=require("url")},89360:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var o=r(60687),c=r(43210),n=r(16189);r(63968);var a=r(7229),i=r(58575),s=r.n(i);function l(){parseInt((0,n.useParams)().id);let[e,t]=(0,c.useState)(null),[r,i]=(0,c.useState)([]),[l,d]=(0,c.useState)([]),[p,_]=(0,c.useState)(!0),[u,h]=(0,c.useState)(null),[m,g]=(0,c.useState)(!1),[x,v]=(0,c.useState)({search:"",priceRange:{min:0,max:1e4},inStockOnly:!1,sortBy:"name",sortDirection:"asc"}),j=e=>{v(t=>({...t,...e}))};return p?(0,o.jsxs)("div",{className:s().loadingContainer,children:[(0,o.jsx)("div",{className:s().loadingSpinner}),(0,o.jsx)("p",{children:"Loading collection..."})]}):u||!e?(0,o.jsxs)("div",{className:s().errorContainer,children:[(0,o.jsx)("h1",{children:"Collection Not Found"}),(0,o.jsx)("p",{children:u||"The requested collection could not be found."})]}):(0,o.jsx)("div",{className:s().collectionPage,children:(0,o.jsxs)("div",{className:s().container,children:[(0,o.jsx)("div",{className:s().collectionHeader,children:(0,o.jsxs)("div",{className:s().headerContent,children:[(0,o.jsx)("h1",{className:s().collectionTitle,children:e.name}),(0,o.jsx)("div",{className:s().collectionMeta})]})}),(0,o.jsxs)("div",{className:s().filterSection,children:[(0,o.jsxs)("div",{className:s().searchBar,children:[(0,o.jsxs)("div",{className:s().searchInput,children:[(0,o.jsxs)("svg",{className:s().searchIcon,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,o.jsx)("circle",{cx:"11",cy:"11",r:"8",stroke:"currentColor",strokeWidth:"2"}),(0,o.jsx)("path",{d:"M21 21L16.65 16.65",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})]}),(0,o.jsx)("input",{type:"text",placeholder:"Search products...",value:x.search,onChange:e=>j({search:e.target.value}),className:s().searchField})]}),(0,o.jsxs)("button",{className:`${s().filterToggle} ${m?s().active:""}`,onClick:()=>g(!m),children:[(0,o.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:(0,o.jsx)("polygon",{points:"22,3 2,3 10,12.46 10,19 14,21 14,12.46",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),"Filters"]})]}),m&&(0,o.jsx)("div",{className:s().advancedFilters,children:(0,o.jsxs)("div",{className:s().filterGrid,children:[(0,o.jsxs)("div",{className:s().filterGroup,children:[(0,o.jsx)("label",{className:s().filterLabel,children:"Price Range"}),(0,o.jsxs)("div",{className:s().priceRange,children:[(0,o.jsx)("input",{type:"number",placeholder:"Min",value:x.priceRange.min,onChange:e=>j({priceRange:{...x.priceRange,min:Number(e.target.value)||0}}),className:s().priceInput}),(0,o.jsx)("span",{children:"to"}),(0,o.jsx)("input",{type:"number",placeholder:"Max",value:x.priceRange.max,onChange:e=>j({priceRange:{...x.priceRange,max:Number(e.target.value)||1e4}}),className:s().priceInput})]})]}),(0,o.jsx)("div",{className:s().filterGroup,children:(0,o.jsxs)("label",{className:s().checkboxLabel,children:[(0,o.jsx)("input",{type:"checkbox",checked:x.inStockOnly,onChange:e=>j({inStockOnly:e.target.checked}),className:s().checkbox}),"In Stock Only"]})}),(0,o.jsxs)("div",{className:s().filterGroup,children:[(0,o.jsx)("label",{className:s().filterLabel,children:"Sort By"}),(0,o.jsxs)("div",{className:s().sortControls,children:[(0,o.jsxs)("select",{value:x.sortBy,onChange:e=>j({sortBy:e.target.value}),className:s().sortSelect,children:[(0,o.jsx)("option",{value:"name",children:"Name"}),(0,o.jsx)("option",{value:"price",children:"Price"}),(0,o.jsx)("option",{value:"newest",children:"Newest"})]}),(0,o.jsx)("button",{className:`${s().sortDirection} ${"desc"===x.sortDirection?s().desc:""}`,onClick:()=>j({sortDirection:"asc"===x.sortDirection?"desc":"asc"}),title:`Sort ${"asc"===x.sortDirection?"Descending":"Ascending"}`,children:(0,o.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",children:(0,o.jsx)("path",{d:"M7 10L12 15L17 10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]}),(0,o.jsx)("div",{className:s().filterGroup,children:(0,o.jsx)("button",{onClick:()=>{v({search:"",priceRange:{min:0,max:1e4},inStockOnly:!1,sortBy:"name",sortDirection:"asc"})},className:s().clearFilters,children:"Clear All Filters"})})]})})]}),(0,o.jsx)("div",{className:s().productsSection,children:(0,o.jsx)(a.qo,{products:l,isLoading:p,showAddToCart:!0,showViewDetails:!0,emptyMessage:x.search||x.inStockOnly||x.priceRange.min>0||x.priceRange.max<1e4?"No products match your current filters. Try adjusting your search criteria.":"This collection doesn't have any products yet."})})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,72,658,913,453],()=>r(11422));module.exports=o})();