/* Product Page Styles - Sharp Rectangular Design */
.productPage {
  padding-top: 6rem;
  min-height: 100vh;
  background: #ffffff;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #1e40af;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1e40af;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 4rem 2rem;
  color: #1e40af;
}

.errorContainer h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Main Product Layout */
.productMain {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

.imageSection {
  position: relative;
}

.detailsSection {
  padding: 2rem 0;
}

/* Product Title */
.productTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

/* Product Code */
.productCode {
  background: #f5f5f5;
  border: 1px solid #ddd;
  padding: 0.4rem 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color:rgb(0, 0, 0);
  margin-bottom: 2rem;
  border-radius: 0; /* Sharp corners */
   display: inline-block;   
}

/* Product Info Grid */
.productInfo {
  margin-bottom: 2rem;
}

.infoRow {
  display: flex;
  align-items: baseline;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
  line-height: 1.4;
}

.infoLabel {
  font-weight: 600;
  color: #1f2937;
  font-size: 1rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.infoValue {
  color: #1f2937;
  font-size: 1rem;
  flex: 1;
}
/* Price Section */
.priceSection {
  margin: 2rem 0;
  padding: 1.5rem;
  background: white;
  /* border: 2px solid black; */
  border-radius: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-start;
}

.priceRow {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2rem;
  width: 100%;
}


.purchaseSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 0; /* Remove extra space */
}

.price {
  background:#f5f5f5;
  padding: 6px;
  font-size: 2rem;
  font-weight: 520;
  color:rgb(0, 0, 0);
}

/* Purchase Section */
.purchaseSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.quantitySelector {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.quantitySelector label {
  font-weight: 600;
  color:rgb(0, 0, 0);
}

.quantityControls {
  display: flex;
  align-items: center;
  border: 2px solidrgb(0, 0, 0);
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
}

.quantityBtn {
  background: #f5f5f5;
  color: black;
  border: black;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: background-color 0.2s;
}

.quantityBtn:hover:not(:disabled) {
  background: #1e40af;
}

.quantityBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.quantityInput {
  border: none;
  padding: 0.75rem 1rem;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  width: 80px;
  background: white;
  outline: none;
  color: black;
}

.addToCartRow {
  display: grid;
  grid-template-columns: 150px 1fr;
  width: 100%;
  align-items: center;
}

.addToCartLabel {
width: 400%;
}

.addToCartWrapper {
  display: flex;
  justify-content: center; 
  width: 100%;
}

.addToCartBtn {
  background: #f5f5f5;
  color: black;
  border: 2px solidrgb(0, 0, 0);
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;   
  max-width: 300px; 
}

.addToCartBtn:hover:not(:disabled) {
  background:rgb(91, 88, 88);
  color:black;
}

.addToCartBtn:disabled {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}
.keySpecsTable {
  width: 100%;
  max-width: 600px;
  font-family: "Arial", sans-serif;
  font-size: 15px;
  color: #111;
  border-collapse: collapse;
  margin-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.specRow {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.label {
  font-weight: 650;
  color: #111;
  min-width: 150px;
  margin-right: 3rem;
}

.value {
  color: #111;
  text-align: left;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .productMain {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .container {
    padding: 1rem;
  }
  
  .productTitle {
    font-size: 2rem;
  }
  .rightSection {
    gap: 2rem;
  }

  
  .specRow {
  display: flex;
  align-items: baseline;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e5e7eb;
  line-height: 1.4;
}

.label {
  font-weight: 600;
  color: #1f2937;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.value {
  color: #1f2937;
  flex: 1;
}
}

@media (max-width: 768px) {
  .infoRow {
    display: flex;
    align-items: baseline;
    padding: 0.4rem 0;
    border-bottom: 1px solid #e5e7eb;
    line-height: 1.3;
  }

  .infoLabel {
    font-weight: 600;
    color: #1f2937;
    margin-right: 0.4rem;
    flex-shrink: 0;
  }
  
  .purchaseSection {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 1rem;
    border-top: 2px solid #1e40af;
    margin: 2rem -1rem 0;
  }
  
  .quantitySelector {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .quantityControls {
    justify-content: center;
  }

.rightSection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.keySpecsTable {
  width: 100%;
  max-width: 600px;
  margin-top: 20px;
  font-size: 16px;
  font-family: 'Arial', sans-serif;
  color: #111;
}

  .infoLabel {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    margin-right: 0.4rem;
    flex-shrink: 0;
  }

  .infoValue {
    color: #1f2937;
    font-size: 0.9rem;
    flex: 1;
  }
  .productTitle {
    font-size: 1.5rem;
  }
  
  .price {
    font-size: 1.5rem;
  }
  
  .productCode {
    font-size: 0.8rem;
    padding: 0.5rem;
  }
  .specRow {
    display: flex;
    align-items: baseline;
    padding: 0.4rem 0;
    border-bottom: 1px solid #e5e7eb;
    line-height: 1.3;
  }

.label {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
    margin-right: 0.4rem;
    flex-shrink: 0;
  }

  .value {
    color: #1f2937;
    font-size: 0.9rem;
    flex: 1;
    word-break: break-word;
  }
}
