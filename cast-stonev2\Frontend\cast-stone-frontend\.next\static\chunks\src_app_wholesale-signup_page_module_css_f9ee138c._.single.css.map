{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/wholesale-signup/page.module.css"], "sourcesContent": [".pageContainer {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n}\n\n.header {\n  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n  color: white;\n  padding: 3rem 0;\n  text-align: center;\n}\n\n.headerContent h1 {\n  margin: 0 0 1rem;\n  font-size: 2.5rem;\n  font-weight: 700;\n}\n\n.headerContent p {\n  margin: 0;\n  font-size: 1.125rem;\n  opacity: 0.9;\n}\n\n.errorBanner {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  margin: 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.errorBanner p {\n  margin: 0;\n  font-weight: 500;\n}\n\n.closeError {\n  background: none;\n  border: none;\n  color: #dc2626;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.contentContainer {\n  display: grid;\n  grid-template-columns: 1fr 300px;\n  gap: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem;\n}\n\n.mainContent {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.sidebar {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  padding: 2rem;\n  height: fit-content;\n  position: sticky;\n  top: 2rem;\n}\n\n.benefits h3 {\n  margin: 0 0 1.5rem;\n  color: #1f2937;\n  font-size: 1.25rem;\n  font-weight: 600;\n}\n\n.benefits ul {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n\n.benefits li {\n  margin-bottom: 0.75rem;\n  padding-left: 1.5rem;\n  position: relative;\n  color: #374151;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.benefits li::before {\n  content: '✓';\n  position: absolute;\n  left: 0;\n  color: #10b981;\n  font-weight: bold;\n}\n\n.formHeader {\n  padding: 2rem 2rem 1rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.formHeader h2 {\n  margin: 0 0 0.5rem;\n  color: #1f2937;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.formHeader p {\n  margin: 0 0 1rem;\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n\n.backToLogin {\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-size: 0.875rem;\n  text-decoration: underline;\n  padding: 0;\n}\n\n.backToLogin:hover {\n  color: #2563eb;\n}\n\n.messageContainer {\n  max-width: 600px;\n  margin: 2rem auto;\n  padding: 0 2rem;\n}\n\n.successMessage,\n.pendingMessage {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n  padding: 3rem;\n  text-align: center;\n}\n\n.successIcon,\n.pendingIcon {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2rem;\n  margin: 0 auto 1.5rem;\n}\n\n.successIcon {\n  background: #d1fae5;\n  color: #10b981;\n}\n\n.pendingIcon {\n  background: #fef3c7;\n  color: #f59e0b;\n}\n\n.successMessage h2,\n.pendingMessage h2 {\n  margin: 0 0 1rem;\n  color: #1f2937;\n  font-size: 1.75rem;\n  font-weight: 600;\n}\n\n.successMessage p,\n.pendingMessage p {\n  margin: 0 0 2rem;\n  color: #6b7280;\n  font-size: 1rem;\n  line-height: 1.6;\n}\n\n.nextSteps {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin: 2rem 0;\n  text-align: left;\n}\n\n.nextSteps h3 {\n  margin: 0 0 1rem;\n  color: #1f2937;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.nextSteps ol {\n  margin: 0;\n  padding-left: 1.5rem;\n  color: #374151;\n}\n\n.nextSteps li {\n  margin-bottom: 0.5rem;\n  line-height: 1.5;\n}\n\n.contactInfo {\n  background: #f0f9ff;\n  border-radius: 8px;\n  padding: 1.5rem;\n  margin: 2rem 0;\n}\n\n.contactInfo p {\n  margin: 0;\n  color: #374151;\n  font-size: 0.875rem;\n}\n\n.contactInfo a {\n  color: #3b82f6;\n  text-decoration: none;\n}\n\n.contactInfo a:hover {\n  text-decoration: underline;\n}\n\n.primaryButton {\n  background: #3b82f6;\n  color: white;\n  border: none;\n  padding: 0.75rem 2rem;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.primaryButton:hover {\n  background: #2563eb;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .contentContainer {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n  }\n  \n  .sidebar {\n    position: static;\n    order: -1;\n  }\n}\n\n@media (max-width: 768px) {\n  .header {\n    padding: 2rem 1rem;\n  }\n  \n  .headerContent h1 {\n    font-size: 2rem;\n  }\n  \n  .contentContainer {\n    padding: 1rem;\n  }\n  \n  .sidebar {\n    padding: 1.5rem;\n  }\n  \n  .formHeader {\n    padding: 1.5rem 1.5rem 1rem;\n  }\n  \n  .successMessage,\n  .pendingMessage {\n    padding: 2rem;\n  }\n  \n  .messageContainer {\n    padding: 0 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .headerContent h1 {\n    font-size: 1.75rem;\n  }\n  \n  .headerContent p {\n    font-size: 1rem;\n  }\n  \n  .successMessage,\n  .pendingMessage {\n    padding: 1.5rem;\n  }\n  \n  .successIcon,\n  .pendingIcon {\n    width: 60px;\n    height: 60px;\n    font-size: 1.5rem;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;;;;AASA;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAKA;;;;;;;AAQA;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAKA;EACE;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAKA"}}]}