{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/wholesale-signup/page.module.css"], "sourcesContent": [".pageContainer {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\r\n  color: white;\r\n  padding: 3rem 0;\r\n  text-align: center;\r\n}\r\n\r\n.headerContent h1 {\r\n  margin: 0 0 1rem;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.headerContent p {\r\n  margin: 0;\r\n  font-size: 1.125rem;\r\n  opacity: 0.9;\r\n}\r\n\r\n.errorBanner {\r\n  background: #fef2f2;\r\n  border: 1px solid #fecaca;\r\n  color: #dc2626;\r\n  padding: 1rem;\r\n  margin: 0;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.errorBanner p {\r\n  margin: 0;\r\n  font-weight: 500;\r\n}\r\n\r\n.closeError {\r\n  background: none;\r\n  border: none;\r\n  color: #dc2626;\r\n  font-size: 1.5rem;\r\n  cursor: pointer;\r\n  padding: 0;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.contentContainer {\r\n  display: grid;\r\n  grid-template-columns: 1fr 300px;\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 2rem;\r\n}\r\n\r\n.mainContent {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.sidebar {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 2rem;\r\n}\r\n\r\n.benefits h3 {\r\n  margin: 0 0 1.5rem;\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.benefits ul {\r\n  margin: 0;\r\n  padding: 0;\r\n  list-style: none;\r\n}\r\n\r\n.benefits li {\r\n  margin-bottom: 0.75rem;\r\n  padding-left: 1.5rem;\r\n  position: relative;\r\n  color: #374151;\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.benefits li::before {\r\n  content: '✓';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #10b981;\r\n  font-weight: bold;\r\n}\r\n\r\n.formHeader {\r\n  padding: 2rem 2rem 1rem;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.formHeader h2 {\r\n  margin: 0 0 0.5rem;\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.formHeader p {\r\n  margin: 0 0 1rem;\r\n  color: #6b7280;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.backToLogin {\r\n  background: none;\r\n  border: none;\r\n  color: #3b82f6;\r\n  cursor: pointer;\r\n  font-size: 0.875rem;\r\n  text-decoration: underline;\r\n  padding: 0;\r\n}\r\n\r\n.backToLogin:hover {\r\n  color: #2563eb;\r\n}\r\n\r\n.messageContainer {\r\n  max-width: 600px;\r\n  margin: 2rem auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.successMessage,\r\n.pendingMessage {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n  padding: 3rem;\r\n  text-align: center;\r\n}\r\n\r\n.successIcon,\r\n.pendingIcon {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 2rem;\r\n  margin: 0 auto 1.5rem;\r\n}\r\n\r\n.successIcon {\r\n  background: #d1fae5;\r\n  color: #10b981;\r\n}\r\n\r\n.pendingIcon {\r\n  background: #fef3c7;\r\n  color: #f59e0b;\r\n}\r\n\r\n.successMessage h2,\r\n.pendingMessage h2 {\r\n  margin: 0 0 1rem;\r\n  color: #1f2937;\r\n  font-size: 1.75rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.successMessage p,\r\n.pendingMessage p {\r\n  margin: 0 0 2rem;\r\n  color: #6b7280;\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n}\r\n\r\n.nextSteps {\r\n  background: #f9fafb;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin: 2rem 0;\r\n  text-align: left;\r\n}\r\n\r\n.nextSteps h3 {\r\n  margin: 0 0 1rem;\r\n  color: #1f2937;\r\n  font-size: 1.125rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.nextSteps ol {\r\n  margin: 0;\r\n  padding-left: 1.5rem;\r\n  color: #374151;\r\n}\r\n\r\n.nextSteps li {\r\n  margin-bottom: 0.5rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.contactInfo {\r\n  background: #f0f9ff;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  margin: 2rem 0;\r\n}\r\n\r\n.contactInfo p {\r\n  margin: 0;\r\n  color: #374151;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.contactInfo a {\r\n  color: #3b82f6;\r\n  text-decoration: none;\r\n}\r\n\r\n.contactInfo a:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.primaryButton {\r\n  background: #3b82f6;\r\n  color: white;\r\n  border: none;\r\n  padding: 0.75rem 2rem;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease;\r\n}\r\n\r\n.primaryButton:hover {\r\n  background: #2563eb;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .contentContainer {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .sidebar {\r\n    position: static;\r\n    order: -1;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    padding: 2rem 1rem;\r\n  }\r\n  \r\n  .headerContent h1 {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .contentContainer {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .sidebar {\r\n    padding: 1.5rem;\r\n  }\r\n  \r\n  .formHeader {\r\n    padding: 1.5rem 1.5rem 1rem;\r\n  }\r\n  \r\n  .successMessage,\r\n  .pendingMessage {\r\n    padding: 2rem;\r\n  }\r\n  \r\n  .messageContainer {\r\n    padding: 0 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .headerContent h1 {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .headerContent p {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .successMessage,\r\n  .pendingMessage {\r\n    padding: 1.5rem;\r\n  }\r\n  \r\n  .successIcon,\r\n  .pendingIcon {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: 1.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AAQA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;;;;AASA;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAKA;;;;;;;AAQA;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;;;;;AAWA;;;;AAKA;EACE;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAKA"}}]}