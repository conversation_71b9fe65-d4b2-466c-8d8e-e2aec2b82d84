/* [project]/src/components/wholesale/WholesaleLogin.module.css [app-client] (css) */
.WholesaleLogin-module__KgOrfG__loginContainer {
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
  display: flex;
}

.WholesaleLogin-module__KgOrfG__loginCard {
  background: #fff;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  box-shadow: 0 4px 6px #0000001a;
}

.WholesaleLogin-module__KgOrfG__loginHeader {
  text-align: center;
  color: #fff;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  padding: 2rem 2rem 1rem;
}

.WholesaleLogin-module__KgOrfG__loginHeader h2 {
  margin: 0 0 .5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.WholesaleLogin-module__KgOrfG__loginHeader p {
  opacity: .9;
  margin: 0;
  font-size: .875rem;
}

.WholesaleLogin-module__KgOrfG__loginForm {
  padding: 2rem;
}

.WholesaleLogin-module__KgOrfG__formGroup {
  margin-bottom: 1.5rem;
}

.WholesaleLogin-module__KgOrfG__formGroup label {
  color: #374151;
  margin-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  display: block;
}

.WholesaleLogin-module__KgOrfG__formGroup input {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: 1rem;
  transition: border-color .2s, box-shadow .2s;
}

.WholesaleLogin-module__KgOrfG__formGroup input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.WholesaleLogin-module__KgOrfG__formGroup input.WholesaleLogin-module__KgOrfG__error {
  border-color: #ef4444;
}

.WholesaleLogin-module__KgOrfG__formGroup input.WholesaleLogin-module__KgOrfG__error:focus {
  box-shadow: 0 0 0 3px #ef44441a;
}

.WholesaleLogin-module__KgOrfG__formGroup input:disabled {
  cursor: not-allowed;
  background-color: #f9fafb;
}

.WholesaleLogin-module__KgOrfG__errorText {
  color: #ef4444;
  margin-top: .25rem;
  font-size: .875rem;
  display: block;
}

.WholesaleLogin-module__KgOrfG__loginButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  width: 100%;
  margin-top: .5rem;
  padding: .75rem;
  font-size: 1rem;
  font-weight: 600;
  transition: background-color .2s;
}

.WholesaleLogin-module__KgOrfG__loginButton:hover:not(:disabled) {
  background: #2563eb;
}

.WholesaleLogin-module__KgOrfG__loginButton:disabled {
  cursor: not-allowed;
  background: #9ca3af;
}

.WholesaleLogin-module__KgOrfG__loginFooter {
  text-align: center;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 1.5rem 2rem 2rem;
}

.WholesaleLogin-module__KgOrfG__loginFooter p {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
}

.WholesaleLogin-module__KgOrfG__linkButton {
  color: #3b82f6;
  cursor: pointer;
  font-size: inherit;
  background: none;
  border: none;
  padding: 0;
  text-decoration: underline;
  transition: color .2s;
}

.WholesaleLogin-module__KgOrfG__linkButton:hover:not(:disabled) {
  color: #2563eb;
}

.WholesaleLogin-module__KgOrfG__linkButton:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

@media (width <= 480px) {
  .WholesaleLogin-module__KgOrfG__loginContainer {
    padding: 1rem;
  }

  .WholesaleLogin-module__KgOrfG__loginCard {
    max-width: none;
  }

  .WholesaleLogin-module__KgOrfG__loginHeader {
    padding: 1.5rem 1.5rem 1rem;
  }

  .WholesaleLogin-module__KgOrfG__loginForm {
    padding: 1.5rem;
  }

  .WholesaleLogin-module__KgOrfG__loginFooter {
    padding: 1rem 1.5rem 1.5rem;
  }
}

/*# sourceMappingURL=src_components_wholesale_WholesaleLogin_module_css_f9ee138c._.single.css.map*/