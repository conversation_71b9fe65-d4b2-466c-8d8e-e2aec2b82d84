{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/catalog/catalog.module.css"], "sourcesContent": [".container {\r\n  min-height: 100vh;\r\n  background: #1a1a2e;\r\n  color: white;\r\n  padding: 8rem 1rem 2rem;\r\n  margin-top: 4.2rem;\r\n}\r\n\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.title {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n}\r\n\r\n.subtitle {\r\n  font-size: 1.2rem;\r\n  color: #b0b0b0;\r\n  line-height: 1.6;\r\n}\r\n\r\n.content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.catalogOptions {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\r\n  gap: 2rem;\r\n  margin-bottom: 4rem;\r\n}\r\n\r\n.catalogCard {\r\n  background: #2a2a3e;\r\n  padding: 3rem 2rem;\r\n  border-radius: 16px;\r\n  border: 2px solid #3a3a4e;\r\n  text-decoration: none;\r\n  color: inherit;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n}\r\n\r\n.catalogCard:hover {\r\n  transform: translateY(-5px);\r\n  border-color: #4a90e2;\r\n  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.2);\r\n}\r\n\r\n.cardIcon {\r\n  color: #4a90e2;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.catalogCard h3 {\r\n  font-size: 1.8rem;\r\n  font-weight: 600;\r\n  margin-bottom: 1rem;\r\n  color: white;\r\n}\r\n\r\n.catalogCard p {\r\n  color: #b0b0b0;\r\n  line-height: 1.6;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.cardAction {\r\n  color: #4a90e2;\r\n  font-weight: 500;\r\n  font-size: 1rem;\r\n}\r\n\r\n.features {\r\n  background: #2a2a3e;\r\n  padding: 3rem 2rem;\r\n  border-radius: 16px;\r\n  border: 2px solid #3a3a4e;\r\n}\r\n\r\n.features h2 {\r\n  font-size: 2rem;\r\n  font-weight: 600;\r\n  margin-bottom: 2rem;\r\n  text-align: center;\r\n  color: white;\r\n}\r\n\r\n.featureGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 2rem;\r\n}\r\n\r\n.feature {\r\n  background: #3a3a4e;\r\n  padding: 2rem;\r\n  border-radius: 12px;\r\n  border: 1px solid #4a4a5e;\r\n}\r\n\r\n.feature h4 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n  color: #4a90e2;\r\n}\r\n\r\n.feature p {\r\n  color: #b0b0b0;\r\n  line-height: 1.5;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .catalogOptions {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .featureGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .catalogCard {\r\n    padding: 2rem 1rem;\r\n  }\r\n  \r\n  .features {\r\n    padding: 2rem 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;AAQA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;EACE;;;;EAIA;;;;;EAUA;;;;EAIA", "debugId": null}}]}