[{"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\collections\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\contact-submissions\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\images\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\orders\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\products\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\login\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\cart\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\catalog\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\success\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\[id]\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\completed-projects\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx": "18", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\our-story\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\[id]\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\retail-locator\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\AdminLayout.tsx": "24", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\CollectionModal.tsx": "25", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\OrderDetailsModal.tsx": "26", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\OrderStatusModal.tsx": "27", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\ProductModal.tsx": "28", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\ProtectedRoute.tsx": "29", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\StockUpdateModal.tsx": "30", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\cart\\CartItem\\CartItem.tsx": "31", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\cart\\CartSummary\\CartSummary.tsx": "32", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\cart\\index.ts": "33", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\CatalogBanner\\CatalogBanner.tsx": "34", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\CategoriesSection\\CategoriesSection.tsx": "35", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\CollectionsCarousel\\CollectionsCarousel.tsx": "36", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\FeaturesSection\\FeaturesSection.tsx": "37", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HeroSection\\HeroSection.tsx": "38", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HomeComponent.tsx": "39", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\TestimonialsSection\\TestimonialsSection.tsx": "40", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\index.ts": "41", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\PatinaSelector\\PatinaSelector.tsx": "42", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductCard\\ProductCard.tsx": "43", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductGrid\\ProductGrid.tsx": "44", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductImageGallery\\ProductImageGallery.tsx": "45", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductSpecifications\\ProductSpecifications.tsx": "46", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\RelatedProducts\\RelatedProducts.tsx": "47", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Footer\\Footer.tsx": "48", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\Header.tsx": "49", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\NavbarTest.tsx": "50", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx": "51", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\CartContext.tsx": "52", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\delete.ts": "53", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\get.ts": "54", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\index.ts": "55", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\post.ts": "56", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\update.ts": "57", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cloudinary\\cloudinaryService.ts": "58", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\delete.ts": "59", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\get.ts": "60", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\index.ts": "61", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\post.ts": "62", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\update.ts": "63", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\contactForm\\get.ts": "64", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\contactForm\\index.ts": "65", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\contactForm\\post.ts": "66", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\get.ts": "67", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\index.ts": "68", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\post.ts": "69", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\update.ts": "70", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\delete.ts": "71", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\get.ts": "72", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\index.ts": "73", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\post.ts": "74", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\update.ts": "75", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\index.ts": "76", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\paypal.ts": "77", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\stripe.ts": "78", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\types.ts": "79", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\unified.ts": "80", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\get.ts": "81", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\index.ts": "82", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\post.ts": "83", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\update.ts": "84", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\delete.ts": "85", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\get.ts": "86", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\index.ts": "87", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\post.ts": "88", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\update.ts": "89", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\get.ts": "90", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\index.ts": "91", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\post.ts": "92", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\update.ts": "93", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\seed\\index.ts": "94", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\delete.ts": "95", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\get.ts": "96", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\index.ts": "97", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\post.ts": "98", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\update.ts": "99", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api.ts": "100", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\config\\apiConfig.ts": "101", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\config\\baseService.ts": "102", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\config\\httpClient.ts": "103", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\examples\\usage.ts": "104", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\index.ts": "105", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\types\\entities.ts": "106", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\types\\index.ts": "107", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\wholesale-buyers\\page.tsx": "108", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\wholesale-signup\\page.tsx": "109", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\wholesale\\index.ts": "110", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\wholesale\\WholesaleLogin.tsx": "111", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\wholesale\\WholesaleSignupForm.tsx": "112", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\WholesaleAuthContext.tsx": "113", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\auth\\index.ts": "114", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\email\\index.ts": "115", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\email\\post.ts": "116", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\delete.ts": "117", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\get.ts": "118", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\index.ts": "119", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\post.ts": "120", "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\utils\\sendEmail.ts": "121"}, {"size": 2047, "mtime": 1751574271681, "results": "122", "hashOfConfig": "123"}, {"size": 11935, "mtime": 1752228715730, "results": "124", "hashOfConfig": "123"}, {"size": 9334, "mtime": 1752087168208, "results": "125", "hashOfConfig": "123"}, {"size": 13383, "mtime": 1751964171102, "results": "126", "hashOfConfig": "123"}, {"size": 14003, "mtime": 1751829443646, "results": "127", "hashOfConfig": "123"}, {"size": 9511, "mtime": 1752150709231, "results": "128", "hashOfConfig": "123"}, {"size": 14782, "mtime": 1752485420962, "results": "129", "hashOfConfig": "123"}, {"size": 535, "mtime": 1751805830027, "results": "130", "hashOfConfig": "123"}, {"size": 5127, "mtime": 1752161657787, "results": "131", "hashOfConfig": "123"}, {"size": 3949, "mtime": 1751829769583, "results": "132", "hashOfConfig": "123"}, {"size": 3307, "mtime": 1752500484892, "results": "133", "hashOfConfig": "123"}, {"size": 15530, "mtime": 1752075704753, "results": "134", "hashOfConfig": "123"}, {"size": 3359, "mtime": 1751829815387, "results": "135", "hashOfConfig": "123"}, {"size": 3196, "mtime": 1752500484892, "results": "136", "hashOfConfig": "123"}, {"size": 10585, "mtime": 1752500484892, "results": "137", "hashOfConfig": "123"}, {"size": 1914, "mtime": 1752500484892, "results": "138", "hashOfConfig": "123"}, {"size": 13332, "mtime": 1752246699708, "results": "139", "hashOfConfig": "123"}, {"size": 1210, "mtime": 1753180120551, "results": "140", "hashOfConfig": "123"}, {"size": 10859, "mtime": 1752170835511, "results": "141", "hashOfConfig": "123"}, {"size": 125, "mtime": 1751574229195, "results": "142", "hashOfConfig": "123"}, {"size": 9233, "mtime": 1752500484902, "results": "143", "hashOfConfig": "123"}, {"size": 10135, "mtime": 1753180040252, "results": "144", "hashOfConfig": "123"}, {"size": 2530, "mtime": 1752500484902, "results": "145", "hashOfConfig": "123"}, {"size": 6867, "mtime": 1753180184592, "results": "146", "hashOfConfig": "123"}, {"size": 24292, "mtime": 1752227970702, "results": "147", "hashOfConfig": "123"}, {"size": 8645, "mtime": 1751805445932, "results": "148", "hashOfConfig": "123"}, {"size": 8320, "mtime": 1751805516008, "results": "149", "hashOfConfig": "123"}, {"size": 43845, "mtime": 1752688973551, "results": "150", "hashOfConfig": "123"}, {"size": 996, "mtime": 1751805021895, "results": "151", "hashOfConfig": "123"}, {"size": 6500, "mtime": 1751830024261, "results": "152", "hashOfConfig": "123"}, {"size": 5189, "mtime": 1751831170031, "results": "153", "hashOfConfig": "123"}, {"size": 4061, "mtime": 1751811843021, "results": "154", "hashOfConfig": "123"}, {"size": 146, "mtime": 1751811933500, "results": "155", "hashOfConfig": "123"}, {"size": 4717, "mtime": 1751801307849, "results": "156", "hashOfConfig": "123"}, {"size": 6888, "mtime": 1752246699711, "results": "157", "hashOfConfig": "123"}, {"size": 6880, "mtime": 1752246699712, "results": "158", "hashOfConfig": "123"}, {"size": 1630, "mtime": 1751574087008, "results": "159", "hashOfConfig": "123"}, {"size": 5183, "mtime": 1751912043691, "results": "160", "hashOfConfig": "123"}, {"size": 1257, "mtime": 1751826518179, "results": "161", "hashOfConfig": "123"}, {"size": 7620, "mtime": 1751829948594, "results": "162", "hashOfConfig": "123"}, {"size": 515, "mtime": 1752500484911, "results": "163", "hashOfConfig": "123"}, {"size": 2825, "mtime": 1752691195544, "results": "164", "hashOfConfig": "123"}, {"size": 5541, "mtime": 1753179937792, "results": "165", "hashOfConfig": "123"}, {"size": 2135, "mtime": 1751811706696, "results": "166", "hashOfConfig": "123"}, {"size": 5385, "mtime": 1752167407051, "results": "167", "hashOfConfig": "123"}, {"size": 18398, "mtime": 1752878392938, "results": "168", "hashOfConfig": "123"}, {"size": 5806, "mtime": 1752171012883, "results": "169", "hashOfConfig": "123"}, {"size": 6861, "mtime": 1752149066200, "results": "170", "hashOfConfig": "123"}, {"size": 12767, "mtime": 1752876134930, "results": "171", "hashOfConfig": "123"}, {"size": 4680, "mtime": 1751800130623, "results": "172", "hashOfConfig": "123"}, {"size": 2663, "mtime": 1751804993019, "results": "173", "hashOfConfig": "123"}, {"size": 8926, "mtime": 1753181062098, "results": "174", "hashOfConfig": "123"}, {"size": 1740, "mtime": 1751814040494, "results": "175", "hashOfConfig": "123"}, {"size": 2661, "mtime": 1752500484911, "results": "176", "hashOfConfig": "123"}, {"size": 678, "mtime": 1751811465802, "results": "177", "hashOfConfig": "123"}, {"size": 582, "mtime": 1751814005484, "results": "178", "hashOfConfig": "123"}, {"size": 671, "mtime": 1751814020238, "results": "179", "hashOfConfig": "123"}, {"size": 3612, "mtime": 1751964042820, "results": "180", "hashOfConfig": "123"}, {"size": 5714, "mtime": 1751627408554, "results": "181", "hashOfConfig": "123"}, {"size": 4432, "mtime": 1751831539403, "results": "182", "hashOfConfig": "123"}, {"size": 804, "mtime": 1751627419261, "results": "183", "hashOfConfig": "123"}, {"size": 4316, "mtime": 1752226699717, "results": "184", "hashOfConfig": "123"}, {"size": 6562, "mtime": 1752226760574, "results": "185", "hashOfConfig": "123"}, {"size": 4532, "mtime": 1752693024150, "results": "186", "hashOfConfig": "123"}, {"size": 353, "mtime": 1752087168216, "results": "187", "hashOfConfig": "123"}, {"size": 3355, "mtime": 1752693024152, "results": "188", "hashOfConfig": "123"}, {"size": 1435, "mtime": 1752485551949, "results": "189", "hashOfConfig": "123"}, {"size": 470, "mtime": 1752485420982, "results": "190", "hashOfConfig": "123"}, {"size": 617, "mtime": 1752485420983, "results": "191", "hashOfConfig": "123"}, {"size": 932, "mtime": 1752485420983, "results": "192", "hashOfConfig": "123"}, {"size": 8272, "mtime": 1751627773675, "results": "193", "hashOfConfig": "123"}, {"size": 7883, "mtime": 1751831672204, "results": "194", "hashOfConfig": "123"}, {"size": 699, "mtime": 1751627784548, "results": "195", "hashOfConfig": "123"}, {"size": 8211, "mtime": 1751830479876, "results": "196", "hashOfConfig": "123"}, {"size": 8791, "mtime": 1751627739152, "results": "197", "hashOfConfig": "123"}, {"size": 1890, "mtime": 1752075704758, "results": "198", "hashOfConfig": "123"}, {"size": 6876, "mtime": 1752075704758, "results": "199", "hashOfConfig": "123"}, {"size": 6025, "mtime": 1752075704765, "results": "200", "hashOfConfig": "123"}, {"size": 4225, "mtime": 1752075704765, "results": "201", "hashOfConfig": "123"}, {"size": 7959, "mtime": 1752075704765, "results": "202", "hashOfConfig": "123"}, {"size": 1350, "mtime": 1752485465768, "results": "203", "hashOfConfig": "123"}, {"size": 425, "mtime": 1752485420983, "results": "204", "hashOfConfig": "123"}, {"size": 572, "mtime": 1752485420983, "results": "205", "hashOfConfig": "123"}, {"size": 872, "mtime": 1752485420991, "results": "206", "hashOfConfig": "123"}, {"size": 6851, "mtime": 1751829728167, "results": "207", "hashOfConfig": "123"}, {"size": 5828, "mtime": 1751831692633, "results": "208", "hashOfConfig": "123"}, {"size": 741, "mtime": 1751627558673, "results": "209", "hashOfConfig": "123"}, {"size": 6575, "mtime": 1751627487698, "results": "210", "hashOfConfig": "123"}, {"size": 7239, "mtime": 1751627516636, "results": "211", "hashOfConfig": "123"}, {"size": 1469, "mtime": 1752485477361, "results": "212", "hashOfConfig": "123"}, {"size": 488, "mtime": 1752485420992, "results": "213", "hashOfConfig": "123"}, {"size": 635, "mtime": 1752485420992, "results": "214", "hashOfConfig": "123"}, {"size": 956, "mtime": 1752485420992, "results": "215", "hashOfConfig": "123"}, {"size": 6452, "mtime": 1751627976004, "results": "216", "hashOfConfig": "123"}, {"size": 9195, "mtime": 1751627933732, "results": "217", "hashOfConfig": "123"}, {"size": 8376, "mtime": 1751831713067, "results": "218", "hashOfConfig": "123"}, {"size": 678, "mtime": 1751627947030, "results": "219", "hashOfConfig": "123"}, {"size": 7735, "mtime": 1751627856304, "results": "220", "hashOfConfig": "123"}, {"size": 8977, "mtime": 1751830365481, "results": "221", "hashOfConfig": "123"}, {"size": 1377, "mtime": 1753113720160, "results": "222", "hashOfConfig": "123"}, {"size": 5808, "mtime": 1753113748278, "results": "223", "hashOfConfig": "123"}, {"size": 3273, "mtime": 1751830419932, "results": "224", "hashOfConfig": "123"}, {"size": 4107, "mtime": 1752500484911, "results": "225", "hashOfConfig": "123"}, {"size": 9866, "mtime": 1751964554158, "results": "226", "hashOfConfig": "123"}, {"size": 2457, "mtime": 1753177874170, "results": "227", "hashOfConfig": "123"}, {"size": 12390, "mtime": 1753177519456, "results": "228", "hashOfConfig": "123"}, {"size": 953, "mtime": 1751831220098, "results": "229", "hashOfConfig": "123"}, {"size": 14104, "mtime": 1753183546385, "results": "230", "hashOfConfig": "123"}, {"size": 6127, "mtime": 1753183386196, "results": "231", "hashOfConfig": "123"}, {"size": 112, "mtime": 1753179792084, "results": "232", "hashOfConfig": "123"}, {"size": 4090, "mtime": 1753183398471, "results": "233", "hashOfConfig": "123"}, {"size": 16470, "mtime": 1753183430078, "results": "234", "hashOfConfig": "123"}, {"size": 5004, "mtime": 1753181093548, "results": "235", "hashOfConfig": "123"}, {"size": 975, "mtime": 1753183272872, "results": "236", "hashOfConfig": "123"}, {"size": 451, "mtime": 1752691195547, "results": "237", "hashOfConfig": "123"}, {"size": 5380, "mtime": 1753183499885, "results": "238", "hashOfConfig": "123"}, {"size": 475, "mtime": 1753183329175, "results": "239", "hashOfConfig": "123"}, {"size": 2093, "mtime": 1753183297631, "results": "240", "hashOfConfig": "123"}, {"size": 709, "mtime": 1753177641280, "results": "241", "hashOfConfig": "123"}, {"size": 1145, "mtime": 1753183315024, "results": "242", "hashOfConfig": "123"}, {"size": 6359, "mtime": 1752691195550, "results": "243", "hashOfConfig": "123"}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1luihjl", {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\collections\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\contact-submissions\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\images\\page.tsx", [], ["607", "608"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\orders\\page.tsx", [], ["609", "610"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\products\\page.tsx", [], ["611"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\cart\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\catalog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\page.tsx", [], ["612", "613"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\success\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\[id]\\page.tsx", [], ["614", "615", "616"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\completed-projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\our-story\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\page.tsx", [], ["617", "618", "619"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\products\\[id]\\page.tsx", [], ["620"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\retail-locator\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\AdminLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\CollectionModal.tsx", [], ["621"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\OrderDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\OrderStatusModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\ProductModal.tsx", [], ["622"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\admin\\StockUpdateModal.tsx", [], ["623", "624", "625"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\cart\\CartItem\\CartItem.tsx", [], ["626"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\cart\\CartSummary\\CartSummary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\cart\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\CatalogBanner\\CatalogBanner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\CategoriesSection\\CategoriesSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\CollectionsCarousel\\CollectionsCarousel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\FeaturesSection\\FeaturesSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HeroSection\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\HomeComponent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\Home\\TestimonialsSection\\TestimonialsSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\PatinaSelector\\PatinaSelector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductCard\\ProductCard.tsx", [], ["627"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductGrid\\ProductGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductImageGallery\\ProductImageGallery.tsx", [], ["628", "629", "630"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\ProductSpecifications\\ProductSpecifications.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\products\\RelatedProducts\\RelatedProducts.tsx", [], ["631", "632", "633"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Footer\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\Header.tsx", [], ["634", "635", "636"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\NavbarTest.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\CartContext.tsx", [], ["637", "638", "639", "640", "641"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\delete.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\get.ts", [], ["642", "643", "644", "645", "646"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cart\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\cloudinary\\cloudinaryService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\delete.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\get.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\collections\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\contactForm\\get.ts", [], ["647"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\contactForm\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\contactForm\\post.ts", [], ["648"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\get.ts", [], ["649"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\downloadableContent\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\delete.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\get.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\post.ts", [], ["650"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\orders\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\index.ts", [], ["651", "652"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\paypal.ts", [], ["653", "654", "655", "656", "657", "658", "659"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\stripe.ts", [], ["660", "661", "662", "663", "664", "665", "666"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\types.ts", [], ["667", "668", "669", "670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\payments\\unified.ts", [], ["682", "683", "684"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\get.ts", [], ["685"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productDetails\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\delete.ts", [], ["686"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\get.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\products\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\get.ts", [], ["687"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\productSpecifications\\update.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\seed\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\delete.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\get.ts", [], ["688"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\users\\update.ts", [], ["689"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\config\\apiConfig.ts", [], ["690"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\config\\baseService.ts", [], ["691", "692", "693", "694", "695"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\config\\httpClient.ts", [], ["696", "697", "698", "699", "700"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\examples\\usage.ts", [], ["701"], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\types\\entities.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\wholesale-buyers\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\wholesale-signup\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\wholesale\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\wholesale\\WholesaleLogin.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\wholesale\\WholesaleSignupForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\WholesaleAuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\auth\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\email\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\email\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\delete.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\get.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\services\\api\\wholesaleBuyers\\post.ts", [], [], "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\utils\\sendEmail.ts", [], ["702"], {"ruleId": "703", "severity": 2, "message": "704", "line": 78, "column": 18, "nodeType": null, "messageId": "705", "endLine": 78, "endColumn": 23, "suppressions": "706"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 285, "column": 27, "nodeType": "709", "endLine": 289, "endColumn": 29, "suppressions": "710"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 43, "column": 32, "nodeType": "713", "messageId": "714", "endLine": 43, "endColumn": 35, "suggestions": "715", "suppressions": "716"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 202, "column": 68, "nodeType": "713", "messageId": "714", "endLine": 202, "endColumn": 71, "suggestions": "717", "suppressions": "718"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 190, "column": 69, "nodeType": "713", "messageId": "714", "endLine": 190, "endColumn": 72, "suggestions": "719", "suppressions": "720"}, {"ruleId": "703", "severity": 2, "message": "721", "line": 118, "column": 13, "nodeType": null, "messageId": "705", "endLine": 118, "endColumn": 22, "suppressions": "722"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 414, "column": 17, "nodeType": "709", "endLine": 418, "endColumn": 19, "suppressions": "723"}, {"ruleId": "724", "severity": 1, "message": "725", "line": 46, "column": 6, "nodeType": "726", "endLine": 46, "endColumn": 20, "suggestions": "727", "suppressions": "728"}, {"ruleId": "724", "severity": 1, "message": "729", "line": 50, "column": 6, "nodeType": "726", "endLine": 50, "endColumn": 25, "suggestions": "730", "suppressions": "731"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 247, "column": 87, "nodeType": "713", "messageId": "714", "endLine": 247, "endColumn": 90, "suggestions": "732", "suppressions": "733"}, {"ruleId": "724", "severity": 1, "message": "729", "line": 48, "column": 6, "nodeType": "726", "endLine": 48, "endColumn": 25, "suggestions": "734", "suppressions": "735"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 118, "column": 62, "nodeType": "713", "messageId": "714", "endLine": 118, "endColumn": 65, "suggestions": "736", "suppressions": "737"}, {"ruleId": "703", "severity": 2, "message": "738", "line": 143, "column": 9, "nodeType": null, "messageId": "705", "endLine": 143, "endColumn": 20, "suppressions": "739"}, {"ruleId": "724", "severity": 1, "message": "740", "line": 34, "column": 6, "nodeType": "726", "endLine": 34, "endColumn": 17, "suggestions": "741", "suppressions": "742"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 441, "column": 19, "nodeType": "709", "endLine": 449, "endColumn": 21, "suppressions": "743"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 447, "column": 19, "nodeType": "709", "endLine": 455, "endColumn": 21, "suppressions": "744"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 105, "column": 68, "nodeType": "713", "messageId": "714", "endLine": 105, "endColumn": 71, "suggestions": "745", "suppressions": "746"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 116, "column": 68, "nodeType": "713", "messageId": "714", "endLine": 116, "endColumn": 71, "suggestions": "747", "suppressions": "748"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 127, "column": 68, "nodeType": "713", "messageId": "714", "endLine": 127, "endColumn": 71, "suggestions": "749", "suppressions": "750"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 68, "column": 9, "nodeType": "709", "endLine": 72, "endColumn": 11, "suppressions": "751"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 64, "column": 9, "nodeType": "709", "endLine": 68, "endColumn": 11, "suppressions": "752"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 53, "column": 9, "nodeType": "709", "endLine": 58, "endColumn": 11, "suppressions": "753"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 129, "column": 17, "nodeType": "709", "endLine": 133, "endColumn": 19, "suppressions": "754"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 144, "column": 13, "nodeType": "709", "endLine": 148, "endColumn": 15, "suppressions": "755"}, {"ruleId": "703", "severity": 2, "message": "756", "line": 15, "column": 10, "nodeType": null, "messageId": "705", "endLine": 15, "endColumn": 22, "suppressions": "757"}, {"ruleId": "703", "severity": 2, "message": "758", "line": 15, "column": 24, "nodeType": null, "messageId": "705", "endLine": 15, "endColumn": 39, "suppressions": "759"}, {"ruleId": "707", "severity": 1, "message": "708", "line": 122, "column": 21, "nodeType": "709", "endLine": 126, "endColumn": 23, "suppressions": "760"}, {"ruleId": "761", "severity": 2, "message": "762", "line": 57, "column": 3, "nodeType": "763", "endLine": 57, "endColumn": 12, "suppressions": "764"}, {"ruleId": "761", "severity": 2, "message": "762", "line": 68, "column": 3, "nodeType": "763", "endLine": 68, "endColumn": 12, "suppressions": "765"}, {"ruleId": "761", "severity": 2, "message": "762", "line": 90, "column": 3, "nodeType": "763", "endLine": 90, "endColumn": 12, "suppressions": "766"}, {"ruleId": "724", "severity": 1, "message": "767", "line": 147, "column": 6, "nodeType": "726", "endLine": 147, "endColumn": 23, "suggestions": "768", "suppressions": "769"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 190, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 190, "endColumn": 24, "suggestions": "770", "suppressions": "771"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 207, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 207, "endColumn": 24, "suggestions": "772", "suppressions": "773"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 222, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 222, "endColumn": 24, "suggestions": "774", "suppressions": "775"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 237, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 237, "endColumn": 24, "suggestions": "776", "suppressions": "777"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 17, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 17, "endColumn": 24, "suggestions": "778", "suppressions": "779"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 35, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 35, "endColumn": 24, "suggestions": "780", "suppressions": "781"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 53, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 53, "endColumn": 24, "suggestions": "782", "suppressions": "783"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 71, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 71, "endColumn": 24, "suggestions": "784", "suppressions": "785"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 83, "column": 34, "nodeType": "713", "messageId": "714", "endLine": 83, "endColumn": 37, "suggestions": "786", "suppressions": "787"}, {"ruleId": "703", "severity": 2, "message": "788", "line": 3, "column": 10, "nodeType": null, "messageId": "705", "endLine": 3, "endColumn": 22, "suppressions": "789"}, {"ruleId": "703", "severity": 2, "message": "788", "line": 3, "column": 10, "nodeType": null, "messageId": "705", "endLine": 3, "endColumn": 22, "suppressions": "790"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 30, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 30, "endColumn": 24, "suggestions": "791", "suppressions": "792"}, {"ruleId": "703", "severity": 2, "message": "793", "line": 131, "column": 11, "nodeType": null, "messageId": "705", "endLine": 131, "endColumn": 21, "suppressions": "794"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 25, "column": 22, "nodeType": "713", "messageId": "714", "endLine": 25, "endColumn": 25, "suggestions": "795", "suppressions": "796"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 54, "column": 29, "nodeType": "713", "messageId": "714", "endLine": 54, "endColumn": 32, "suggestions": "797", "suppressions": "798"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 80, "column": 24, "nodeType": "713", "messageId": "714", "endLine": 80, "endColumn": 27, "suggestions": "799", "suppressions": "800"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 81, "column": 23, "nodeType": "713", "messageId": "714", "endLine": 81, "endColumn": 26, "suggestions": "801", "suppressions": "802"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 82, "column": 23, "nodeType": "713", "messageId": "714", "endLine": 82, "endColumn": 26, "suggestions": "803", "suppressions": "804"}, {"ruleId": "703", "severity": 2, "message": "805", "line": 86, "column": 27, "nodeType": null, "messageId": "705", "endLine": 86, "endColumn": 31, "suppressions": "806"}, {"ruleId": "703", "severity": 2, "message": "807", "line": 86, "column": 33, "nodeType": null, "messageId": "705", "endLine": 86, "endColumn": 40, "suppressions": "808"}, {"ruleId": "703", "severity": 2, "message": "807", "line": 105, "column": 31, "nodeType": null, "messageId": "705", "endLine": 105, "endColumn": 38, "suppressions": "809"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 249, "column": 14, "nodeType": "713", "messageId": "714", "endLine": 249, "endColumn": 17, "suggestions": "810", "suppressions": "811"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 8, "column": 23, "nodeType": "713", "messageId": "714", "endLine": 8, "endColumn": 26, "suggestions": "812", "suppressions": "813"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 58, "column": 96, "nodeType": "713", "messageId": "714", "endLine": 58, "endColumn": 99, "suggestions": "814", "suppressions": "815"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 73, "column": 98, "nodeType": "713", "messageId": "714", "endLine": 73, "endColumn": 101, "suggestions": "816", "suppressions": "817"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 99, "column": 86, "nodeType": "713", "messageId": "714", "endLine": 99, "endColumn": 89, "suggestions": "818", "suppressions": "819"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 109, "column": 18, "nodeType": "713", "messageId": "714", "endLine": 109, "endColumn": 21, "suggestions": "820", "suppressions": "821"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 137, "column": 31, "nodeType": "713", "messageId": "714", "endLine": 137, "endColumn": 34, "suggestions": "822", "suppressions": "823"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 148, "column": 35, "nodeType": "713", "messageId": "714", "endLine": 148, "endColumn": 38, "suggestions": "824", "suppressions": "825"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 11, "column": 29, "nodeType": "713", "messageId": "714", "endLine": 11, "endColumn": 32, "suggestions": "826", "suppressions": "827"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 20, "column": 29, "nodeType": "713", "messageId": "714", "endLine": 20, "endColumn": 32, "suggestions": "828", "suppressions": "829"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 57, "column": 29, "nodeType": "713", "messageId": "714", "endLine": 57, "endColumn": 32, "suggestions": "830", "suppressions": "831"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 107, "column": 34, "nodeType": "713", "messageId": "714", "endLine": 107, "endColumn": 37, "suggestions": "832", "suppressions": "833"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 115, "column": 38, "nodeType": "713", "messageId": "714", "endLine": 115, "endColumn": 41, "suggestions": "834", "suppressions": "835"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 116, "column": 19, "nodeType": "713", "messageId": "714", "endLine": 116, "endColumn": 22, "suggestions": "836", "suppressions": "837"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 119, "column": 18, "nodeType": "763", "messageId": "840", "endLine": 119, "endColumn": 35, "suggestions": "841", "suppressions": "842"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 123, "column": 18, "nodeType": "763", "messageId": "840", "endLine": 123, "endColumn": 38, "suggestions": "843", "suppressions": "844"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 129, "column": 23, "nodeType": "713", "messageId": "714", "endLine": 129, "endColumn": 26, "suggestions": "845", "suppressions": "846"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 129, "column": 37, "nodeType": "713", "messageId": "714", "endLine": 129, "endColumn": 40, "suggestions": "847", "suppressions": "848"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 130, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 130, "endColumn": 24, "suggestions": "849", "suppressions": "850"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 130, "column": 35, "nodeType": "713", "messageId": "714", "endLine": 130, "endColumn": 38, "suggestions": "851", "suppressions": "852"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 131, "column": 19, "nodeType": "713", "messageId": "714", "endLine": 131, "endColumn": 22, "suggestions": "853", "suppressions": "854"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 132, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 132, "endColumn": 24, "suggestions": "855", "suppressions": "856"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 147, "column": 47, "nodeType": "713", "messageId": "714", "endLine": 147, "endColumn": 50, "suggestions": "857", "suppressions": "858"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 36, "column": 22, "nodeType": "713", "messageId": "714", "endLine": 36, "endColumn": 25, "suggestions": "859", "suppressions": "860"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 180, "column": 43, "nodeType": "713", "messageId": "714", "endLine": 180, "endColumn": 46, "suggestions": "861", "suppressions": "862"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 224, "column": 29, "nodeType": "713", "messageId": "714", "endLine": 224, "endColumn": 32, "suggestions": "863", "suppressions": "864"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 30, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 30, "endColumn": 24, "suggestions": "865", "suppressions": "866"}, {"ruleId": "703", "severity": 2, "message": "867", "line": 47, "column": 19, "nodeType": null, "messageId": "705", "endLine": 47, "endColumn": 21, "suppressions": "868"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 30, "column": 21, "nodeType": "713", "messageId": "714", "endLine": 30, "endColumn": 24, "suggestions": "869", "suppressions": "870"}, {"ruleId": "703", "severity": 2, "message": "704", "line": 298, "column": 14, "nodeType": null, "messageId": "705", "endLine": 298, "endColumn": 19, "suppressions": "871"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 283, "column": 33, "nodeType": "713", "messageId": "714", "endLine": 283, "endColumn": 36, "suggestions": "872", "suppressions": "873"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 164, "column": 57, "nodeType": "713", "messageId": "714", "endLine": 164, "endColumn": 60, "suggestions": "874", "suppressions": "875"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 48, "column": 34, "nodeType": "713", "messageId": "714", "endLine": 48, "endColumn": 37, "suggestions": "876", "suppressions": "877"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 62, "column": 65, "nodeType": "713", "messageId": "714", "endLine": 62, "endColumn": 68, "suggestions": "878", "suppressions": "879"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 106, "column": 39, "nodeType": "713", "messageId": "714", "endLine": 106, "endColumn": 42, "suggestions": "880", "suppressions": "881"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 106, "column": 49, "nodeType": "713", "messageId": "714", "endLine": 106, "endColumn": 52, "suggestions": "882", "suppressions": "883"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 120, "column": 47, "nodeType": "713", "messageId": "714", "endLine": 120, "endColumn": 50, "suggestions": "884", "suppressions": "885"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 63, "column": 25, "nodeType": "713", "messageId": "714", "endLine": 63, "endColumn": 28, "suggestions": "886", "suppressions": "887"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 89, "column": 58, "nodeType": "713", "messageId": "714", "endLine": 89, "endColumn": 61, "suggestions": "888", "suppressions": "889"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 94, "column": 42, "nodeType": "713", "messageId": "714", "endLine": 94, "endColumn": 45, "suggestions": "890", "suppressions": "891"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 102, "column": 41, "nodeType": "713", "messageId": "714", "endLine": 102, "endColumn": 44, "suggestions": "892", "suppressions": "893"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 110, "column": 43, "nodeType": "713", "messageId": "714", "endLine": 110, "endColumn": 46, "suggestions": "894", "suppressions": "895"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 379, "column": 25, "nodeType": "713", "messageId": "714", "endLine": 379, "endColumn": 28, "suggestions": "896", "suppressions": "897"}, {"ruleId": "711", "severity": 2, "message": "712", "line": 122, "column": 34, "nodeType": "713", "messageId": "714", "endLine": 122, "endColumn": 37, "suggestions": "898", "suppressions": "899"}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", ["900"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["901"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["902", "903"], ["904"], ["905", "906"], ["907"], ["908", "909"], ["910"], "'orderData' is assigned a value but never used.", ["911"], ["912"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["913"], ["914"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["915"], ["916"], ["917", "918"], ["919"], ["920"], ["921"], ["922", "923"], ["924"], "'formatPrice' is assigned a value but never used.", ["925"], "React Hook useEffect has a missing dependency: 'fetchProductData'. Either include it or remove the dependency array.", ["926"], ["927"], ["928"], ["929"], ["930", "931"], ["932"], ["933", "934"], ["935"], ["936", "937"], ["938"], ["939"], ["940"], ["941"], ["942"], ["943"], "'currentIndex' is assigned a value but never used.", ["944"], "'setCurrentIndex' is assigned a value but never used.", ["945"], ["946"], "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "Identifier", ["947"], ["948"], ["949"], "React Hook useEffect has a missing dependency: 'loadCart'. Either include it or remove the dependency array.", ["950"], ["951"], ["952", "953"], ["954"], ["955", "956"], ["957"], ["958", "959"], ["960"], ["961", "962"], ["963"], ["964", "965"], ["966"], ["967", "968"], ["969"], ["970", "971"], ["972"], ["973", "974"], ["975"], ["976", "977"], ["978"], "'ApiEndpoints' is defined but never used.", ["979"], ["980"], ["981", "982"], ["983"], "'orderItems' is assigned a value but never used.", ["984"], ["985", "986"], ["987"], ["988", "989"], ["990"], ["991", "992"], ["993"], ["994", "995"], ["996"], ["997", "998"], ["999"], "'data' is defined but never used.", ["1000"], "'actions' is defined but never used.", ["1001"], ["1002"], ["1003", "1004"], ["1005"], ["1006", "1007"], ["1008"], ["1009", "1010"], ["1011"], ["1012", "1013"], ["1014"], ["1015", "1016"], ["1017"], ["1018", "1019"], ["1020"], ["1021", "1022"], ["1023"], ["1024", "1025"], ["1026"], ["1027", "1028"], ["1029"], ["1030", "1031"], ["1032"], ["1033", "1034"], ["1035"], ["1036", "1037"], ["1038"], ["1039", "1040"], ["1041"], ["1042", "1043"], ["1044"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["1045"], ["1046"], ["1047"], ["1048"], ["1049", "1050"], ["1051"], ["1052", "1053"], ["1054"], ["1055", "1056"], ["1057"], ["1058", "1059"], ["1060"], ["1061", "1062"], ["1063"], ["1064", "1065"], ["1066"], ["1067", "1068"], ["1069"], ["1070", "1071"], ["1072"], ["1073", "1074"], ["1075"], ["1076", "1077"], ["1078"], ["1079", "1080"], ["1081"], "'id' is defined but never used.", ["1082"], ["1083", "1084"], ["1085"], ["1086"], ["1087", "1088"], ["1089"], ["1090", "1091"], ["1092"], ["1093", "1094"], ["1095"], ["1096", "1097"], ["1098"], ["1099", "1100"], ["1101"], ["1102", "1103"], ["1104"], ["1105", "1106"], ["1107"], ["1108", "1109"], ["1110"], ["1111", "1112"], ["1113"], ["1114", "1115"], ["1116"], ["1117", "1118"], ["1119"], ["1120", "1121"], ["1122"], ["1123", "1124"], ["1125"], ["1126", "1127"], ["1128"], {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1132", "desc": "1133"}, {"messageId": "1134", "fix": "1135", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1137", "desc": "1133"}, {"messageId": "1134", "fix": "1138", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1139", "desc": "1133"}, {"messageId": "1134", "fix": "1140", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"desc": "1141", "fix": "1142"}, {"kind": "1129", "justification": "1130"}, {"desc": "1143", "fix": "1144"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1145", "desc": "1133"}, {"messageId": "1134", "fix": "1146", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"desc": "1143", "fix": "1147"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1148", "desc": "1133"}, {"messageId": "1134", "fix": "1149", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"desc": "1150", "fix": "1151"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1152", "desc": "1133"}, {"messageId": "1134", "fix": "1153", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1154", "desc": "1133"}, {"messageId": "1134", "fix": "1155", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1156", "desc": "1133"}, {"messageId": "1134", "fix": "1157", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"desc": "1158", "fix": "1159"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1160", "desc": "1133"}, {"messageId": "1134", "fix": "1161", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1162", "desc": "1133"}, {"messageId": "1134", "fix": "1163", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1164", "desc": "1133"}, {"messageId": "1134", "fix": "1165", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1166", "desc": "1133"}, {"messageId": "1134", "fix": "1167", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1168", "desc": "1133"}, {"messageId": "1134", "fix": "1169", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1170", "desc": "1133"}, {"messageId": "1134", "fix": "1171", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1172", "desc": "1133"}, {"messageId": "1134", "fix": "1173", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1174", "desc": "1133"}, {"messageId": "1134", "fix": "1175", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1176", "desc": "1133"}, {"messageId": "1134", "fix": "1177", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1178", "desc": "1133"}, {"messageId": "1134", "fix": "1179", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1180", "desc": "1133"}, {"messageId": "1134", "fix": "1181", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1182", "desc": "1133"}, {"messageId": "1134", "fix": "1183", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1184", "desc": "1133"}, {"messageId": "1134", "fix": "1185", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1186", "desc": "1133"}, {"messageId": "1134", "fix": "1187", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1188", "desc": "1133"}, {"messageId": "1134", "fix": "1189", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1190", "desc": "1133"}, {"messageId": "1134", "fix": "1191", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1192", "desc": "1133"}, {"messageId": "1134", "fix": "1193", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1194", "desc": "1133"}, {"messageId": "1134", "fix": "1195", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1196", "desc": "1133"}, {"messageId": "1134", "fix": "1197", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1198", "desc": "1133"}, {"messageId": "1134", "fix": "1199", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1200", "desc": "1133"}, {"messageId": "1134", "fix": "1201", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1202", "desc": "1133"}, {"messageId": "1134", "fix": "1203", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1204", "desc": "1133"}, {"messageId": "1134", "fix": "1205", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1206", "desc": "1133"}, {"messageId": "1134", "fix": "1207", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1208", "desc": "1133"}, {"messageId": "1134", "fix": "1209", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1210", "desc": "1133"}, {"messageId": "1134", "fix": "1211", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1212", "desc": "1133"}, {"messageId": "1134", "fix": "1213", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1214", "desc": "1133"}, {"messageId": "1134", "fix": "1215", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1216", "desc": "1133"}, {"messageId": "1134", "fix": "1217", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1218", "fix": "1219", "desc": "1220"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1218", "fix": "1221", "desc": "1220"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1222", "desc": "1133"}, {"messageId": "1134", "fix": "1223", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1224", "desc": "1133"}, {"messageId": "1134", "fix": "1225", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1226", "desc": "1133"}, {"messageId": "1134", "fix": "1227", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1228", "desc": "1133"}, {"messageId": "1134", "fix": "1229", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1230", "desc": "1133"}, {"messageId": "1134", "fix": "1231", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1232", "desc": "1133"}, {"messageId": "1134", "fix": "1233", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1234", "desc": "1133"}, {"messageId": "1134", "fix": "1235", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1236", "desc": "1133"}, {"messageId": "1134", "fix": "1237", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1238", "desc": "1133"}, {"messageId": "1134", "fix": "1239", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1240", "desc": "1133"}, {"messageId": "1134", "fix": "1241", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1242", "desc": "1133"}, {"messageId": "1134", "fix": "1243", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1244", "desc": "1133"}, {"messageId": "1134", "fix": "1245", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1246", "desc": "1133"}, {"messageId": "1134", "fix": "1247", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1248", "desc": "1133"}, {"messageId": "1134", "fix": "1249", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1250", "desc": "1133"}, {"messageId": "1134", "fix": "1251", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1252", "desc": "1133"}, {"messageId": "1134", "fix": "1253", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1254", "desc": "1133"}, {"messageId": "1134", "fix": "1255", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1256", "desc": "1133"}, {"messageId": "1134", "fix": "1257", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1258", "desc": "1133"}, {"messageId": "1134", "fix": "1259", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1260", "desc": "1133"}, {"messageId": "1134", "fix": "1261", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1262", "desc": "1133"}, {"messageId": "1134", "fix": "1263", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1264", "desc": "1133"}, {"messageId": "1134", "fix": "1265", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1266", "desc": "1133"}, {"messageId": "1134", "fix": "1267", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1268", "desc": "1133"}, {"messageId": "1134", "fix": "1269", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1270", "desc": "1133"}, {"messageId": "1134", "fix": "1271", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, {"messageId": "1131", "fix": "1272", "desc": "1133"}, {"messageId": "1134", "fix": "1273", "desc": "1136"}, {"kind": "1129", "justification": "1130"}, "directive", "", "suggestUnknown", {"range": "1274", "text": "1275"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1276", "text": "1277"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1278", "text": "1275"}, {"range": "1279", "text": "1277"}, {"range": "1280", "text": "1275"}, {"range": "1281", "text": "1277"}, "Update the dependencies array to be: [collectionId, fetchData]", {"range": "1282", "text": "1283"}, "Update the dependencies array to be: [products, filters, applyFilters]", {"range": "1284", "text": "1285"}, {"range": "1286", "text": "1275"}, {"range": "1287", "text": "1277"}, {"range": "1288", "text": "1285"}, {"range": "1289", "text": "1275"}, {"range": "1290", "text": "1277"}, "Update the dependencies array to be: [fetchProductData, productId]", {"range": "1291", "text": "1292"}, {"range": "1293", "text": "1275"}, {"range": "1294", "text": "1277"}, {"range": "1295", "text": "1275"}, {"range": "1296", "text": "1277"}, {"range": "1297", "text": "1275"}, {"range": "1298", "text": "1277"}, "Update the dependencies array to be: [loadCart, state.sessionId]", {"range": "1299", "text": "1300"}, {"range": "1301", "text": "1275"}, {"range": "1302", "text": "1277"}, {"range": "1303", "text": "1275"}, {"range": "1304", "text": "1277"}, {"range": "1305", "text": "1275"}, {"range": "1306", "text": "1277"}, {"range": "1307", "text": "1275"}, {"range": "1308", "text": "1277"}, {"range": "1309", "text": "1275"}, {"range": "1310", "text": "1277"}, {"range": "1311", "text": "1275"}, {"range": "1312", "text": "1277"}, {"range": "1313", "text": "1275"}, {"range": "1314", "text": "1277"}, {"range": "1315", "text": "1275"}, {"range": "1316", "text": "1277"}, {"range": "1317", "text": "1275"}, {"range": "1318", "text": "1277"}, {"range": "1319", "text": "1275"}, {"range": "1320", "text": "1277"}, {"range": "1321", "text": "1275"}, {"range": "1322", "text": "1277"}, {"range": "1323", "text": "1275"}, {"range": "1324", "text": "1277"}, {"range": "1325", "text": "1275"}, {"range": "1326", "text": "1277"}, {"range": "1327", "text": "1275"}, {"range": "1328", "text": "1277"}, {"range": "1329", "text": "1275"}, {"range": "1330", "text": "1277"}, {"range": "1331", "text": "1275"}, {"range": "1332", "text": "1277"}, {"range": "1333", "text": "1275"}, {"range": "1334", "text": "1277"}, {"range": "1335", "text": "1275"}, {"range": "1336", "text": "1277"}, {"range": "1337", "text": "1275"}, {"range": "1338", "text": "1277"}, {"range": "1339", "text": "1275"}, {"range": "1340", "text": "1277"}, {"range": "1341", "text": "1275"}, {"range": "1342", "text": "1277"}, {"range": "1343", "text": "1275"}, {"range": "1344", "text": "1277"}, {"range": "1345", "text": "1275"}, {"range": "1346", "text": "1277"}, {"range": "1347", "text": "1275"}, {"range": "1348", "text": "1277"}, {"range": "1349", "text": "1275"}, {"range": "1350", "text": "1277"}, {"range": "1351", "text": "1275"}, {"range": "1352", "text": "1277"}, {"range": "1353", "text": "1275"}, {"range": "1354", "text": "1277"}, {"range": "1355", "text": "1275"}, {"range": "1356", "text": "1277"}, {"range": "1357", "text": "1275"}, {"range": "1358", "text": "1277"}, "replaceEmptyInterfaceWithSuper", {"range": "1359", "text": "1360"}, "Replace empty interface with a type alias.", {"range": "1361", "text": "1362"}, {"range": "1363", "text": "1275"}, {"range": "1364", "text": "1277"}, {"range": "1365", "text": "1275"}, {"range": "1366", "text": "1277"}, {"range": "1367", "text": "1275"}, {"range": "1368", "text": "1277"}, {"range": "1369", "text": "1275"}, {"range": "1370", "text": "1277"}, {"range": "1371", "text": "1275"}, {"range": "1372", "text": "1277"}, {"range": "1373", "text": "1275"}, {"range": "1374", "text": "1277"}, {"range": "1375", "text": "1275"}, {"range": "1376", "text": "1277"}, {"range": "1377", "text": "1275"}, {"range": "1378", "text": "1277"}, {"range": "1379", "text": "1275"}, {"range": "1380", "text": "1277"}, {"range": "1381", "text": "1275"}, {"range": "1382", "text": "1277"}, {"range": "1383", "text": "1275"}, {"range": "1384", "text": "1277"}, {"range": "1385", "text": "1275"}, {"range": "1386", "text": "1277"}, {"range": "1387", "text": "1275"}, {"range": "1388", "text": "1277"}, {"range": "1389", "text": "1275"}, {"range": "1390", "text": "1277"}, {"range": "1391", "text": "1275"}, {"range": "1392", "text": "1277"}, {"range": "1393", "text": "1275"}, {"range": "1394", "text": "1277"}, {"range": "1395", "text": "1275"}, {"range": "1396", "text": "1277"}, {"range": "1397", "text": "1275"}, {"range": "1398", "text": "1277"}, {"range": "1399", "text": "1275"}, {"range": "1400", "text": "1277"}, {"range": "1401", "text": "1275"}, {"range": "1402", "text": "1277"}, {"range": "1403", "text": "1275"}, {"range": "1404", "text": "1277"}, {"range": "1405", "text": "1275"}, {"range": "1406", "text": "1277"}, {"range": "1407", "text": "1275"}, {"range": "1408", "text": "1277"}, {"range": "1409", "text": "1275"}, {"range": "1410", "text": "1277"}, {"range": "1411", "text": "1275"}, {"range": "1412", "text": "1277"}, {"range": "1413", "text": "1275"}, {"range": "1414", "text": "1277"}, [1832, 1835], "unknown", [1832, 1835], "never", [7424, 7427], [7424, 7427], [7275, 7278], [7275, 7278], [1459, 1473], "[collectionId, fetchData]", [1526, 1545], "[products, filters, applyFilters]", [8488, 8491], [8488, 8491], [1470, 1489], [3568, 3571], [3568, 3571], [1558, 1569], "[fetchProductData, productId]", [3333, 3336], [3333, 3336], [3839, 3842], [3839, 3842], [4341, 4344], [4341, 4344], [4702, 4719], "[loadCart, state.sessionId]", [6177, 6180], [6177, 6180], [6852, 6855], [6852, 6855], [7408, 7411], [7408, 7411], [7910, 7913], [7910, 7913], [597, 600], [597, 600], [1045, 1048], [1045, 1048], [1514, 1517], [1514, 1517], [1998, 2001], [1998, 2001], [2263, 2266], [2263, 2266], [1316, 1319], [1316, 1319], [786, 789], [786, 789], [1646, 1649], [1646, 1649], [2308, 2311], [2308, 2311], [2345, 2348], [2345, 2348], [2382, 2385], [2382, 2385], [6727, 6730], [6727, 6730], [293, 296], [293, 296], [1905, 1908], [1905, 1908], [2310, 2313], [2310, 2313], [3067, 3070], [3067, 3070], [3342, 3345], [3342, 3345], [4199, 4202], [4199, 4202], [4462, 4465], [4462, 4465], [384, 387], [384, 387], [615, 618], [615, 618], [1383, 1386], [1383, 1386], [2420, 2423], [2420, 2423], [2659, 2662], [2659, 2662], [2699, 2702], [2699, 2702], [2724, 2806], "type StripeCardElement = StripeElement", [2817, 2913], "type StripePaymentElement = StripeElement", [3015, 3018], [3015, 3018], [3029, 3032], [3029, 3032], [3075, 3078], [3075, 3078], [3089, 3092], [3089, 3092], [3131, 3134], [3131, 3134], [3166, 3169], [3166, 3169], [3592, 3595], [3592, 3595], [1265, 1268], [1265, 1268], [5247, 5250], [5247, 5250], [6362, 6365], [6362, 6365], [1231, 1234], [1231, 1234], [1350, 1353], [1350, 1353], [7069, 7072], [7069, 7072], [5187, 5190], [5187, 5190], [1331, 1334], [1331, 1334], [1644, 1647], [1644, 1647], [2626, 2629], [2626, 2629], [2636, 2639], [2636, 2639], [3005, 3008], [3005, 3008], [1722, 1725], [1722, 1725], [2349, 2352], [2349, 2352], [2517, 2520], [2517, 2520], [2742, 2745], [2742, 2745], [2970, 2973], [2970, 2973], [9490, 9493], [9490, 9493], [3068, 3071], [3068, 3071]]