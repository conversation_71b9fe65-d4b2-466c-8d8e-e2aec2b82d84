(()=>{var e={};e.id=279,e.ids=[279],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(t,c);let l={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,54787)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30277:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(60687),a=s(43210),n=s(16189),i=s(28253),o=s(63968),c=s(42379),l=s.n(c);function d(){let{state:e,clearCart:t}=(0,i._)(),s=(0,n.useRouter)(),[c,d]=(0,a.useState)(1),[m,u]=(0,a.useState)(!1),[p,h]=(0,a.useState)([]),[x,_]=(0,a.useState)(""),[v,j]=(0,a.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"United States"}),[y,f]=(0,a.useState)("stripe"),k=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),N=e=>{let{name:t,value:s}=e.target;j(e=>({...e,[t]:s}))},g=()=>["firstName","lastName","email","phone","address","city","state","zipCode"].every(e=>""!==v[e].trim()),C=async()=>{if(e.cart){u(!0),_("");try{let r=o.MV.validatePaymentAmount(q);if(!r.valid)return void _(r.errors.join(", "));v.email,v.phone,v.country,v.city,v.zipCode,e.cart.cartItems.map(e=>({productId:e.productId,quantity:e.quantity}));let a=Math.floor(1e4*Math.random()),n=await o.MV.processPayment(y,q,"USD",a,{description:`Cast Stone Order #${a}`,customer_email:v.email});if(!n.success)return void _(n.message||"Payment processing failed");if("paypal"===y&&"approvalUrl"in n){if("string"!=typeof n.approvalUrl)return void _("Invalid approval URL received from payment service.");window.location.href=n.approvalUrl;return}if("paymentIntentId"in n&&n.paymentIntentId){let e=await o.MV.completePayment(y,n.paymentIntentId,a);if(!e.payment.success)return void _(e.payment.message||"Payment completion failed");console.log("Payment completed:",e)}await t(),s.push("/checkout/success")}catch(e){console.error("Error placing order:",e),_(o.MV.handlePaymentError(e,y).userMessage)}finally{u(!1)}}};if(!e.cart||0===e.cart.cartItems.length)return null;let b=e.cart.totalAmount,P=.08*b,I=b>100?0:15,q=b+P+I;return(0,r.jsxs)("div",{className:l().container,children:[(0,r.jsxs)("div",{className:l().header,children:[(0,r.jsx)("h1",{className:l().title,children:"Checkout"}),(0,r.jsxs)("div",{className:l().stepIndicator,children:[(0,r.jsxs)("div",{className:`${l().step} ${c>=1?l().active:""}`,children:[(0,r.jsx)("span",{children:"1"}),(0,r.jsx)("label",{children:"Shipping"})]}),(0,r.jsx)("div",{className:l().stepConnector}),(0,r.jsxs)("div",{className:`${l().step} ${c>=2?l().active:""}`,children:[(0,r.jsx)("span",{children:"2"}),(0,r.jsx)("label",{children:"Payment"})]})]})]}),(0,r.jsxs)("div",{className:l().checkoutContent,children:[(0,r.jsxs)("div",{className:l().mainContent,children:[1===c&&(0,r.jsxs)("div",{className:l().shippingSection,children:[(0,r.jsx)("h2",{className:l().sectionTitle,children:"Shipping Information"}),(0,r.jsxs)("div",{className:l().formGrid,children:[(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"firstName",children:"First Name *"}),(0,r.jsx)("input",{type:"text",id:"firstName",name:"firstName",value:v.firstName,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"lastName",children:"Last Name *"}),(0,r.jsx)("input",{type:"text",id:"lastName",name:"lastName",value:v.lastName,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"email",children:"Email *"}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",value:v.email,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"phone",children:"Phone *"}),(0,r.jsx)("input",{type:"tel",id:"phone",name:"phone",value:v.phone,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:`${l().formGroup} ${l().fullWidth}`,children:[(0,r.jsx)("label",{htmlFor:"address",children:"Address *"}),(0,r.jsx)("input",{type:"text",id:"address",name:"address",value:v.address,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"city",children:"City *"}),(0,r.jsx)("input",{type:"text",id:"city",name:"city",value:v.city,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"state",children:"State *"}),(0,r.jsx)("input",{type:"text",id:"state",name:"state",value:v.state,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"zipCode",children:"ZIP Code *"}),(0,r.jsx)("input",{type:"text",id:"zipCode",name:"zipCode",value:v.zipCode,onChange:N,required:!0})]}),(0,r.jsxs)("div",{className:l().formGroup,children:[(0,r.jsx)("label",{htmlFor:"country",children:"Country *"}),(0,r.jsxs)("select",{id:"country",name:"country",value:v.country,onChange:N,required:!0,children:[(0,r.jsx)("option",{value:"United States",children:"United States"}),(0,r.jsx)("option",{value:"Canada",children:"Canada"}),(0,r.jsx)("option",{value:"United Kingdom",children:"United Kingdom"})]})]})]}),(0,r.jsx)("div",{className:l().stepActions,children:(0,r.jsx)("button",{onClick:()=>{1===c&&g()?d(2):1===c&&alert("Please fill in all required fields")},className:l().nextBtn,children:"Continue to Payment"})})]}),2===c&&(0,r.jsxs)("div",{className:l().paymentSection,children:[(0,r.jsx)("h2",{className:l().sectionTitle,children:"Payment Method"}),(0,r.jsx)("div",{className:l().paymentMethods,children:p.map(e=>(0,r.jsxs)("div",{className:`${l().paymentMethod} ${y===e.id?l().selected:""}`,onClick:()=>f(e.id),children:[(0,r.jsx)("div",{className:l().paymentIcon,children:e.icon}),(0,r.jsxs)("div",{className:l().paymentInfo,children:[(0,r.jsx)("h3",{children:e.name}),(0,r.jsx)("p",{children:e.description})]}),(0,r.jsx)("div",{className:l().radioButton,children:(0,r.jsx)("input",{type:"radio",name:"paymentMethod",value:e.id,checked:y===e.id,onChange:()=>f(e.id)})})]},e.id))}),x&&(0,r.jsx)("div",{className:l().errorMessage,children:(0,r.jsx)("p",{children:x})}),(0,r.jsxs)("div",{className:l().stepActions,children:[(0,r.jsx)("button",{onClick:()=>d(1),className:l().backBtn,children:"Back to Shipping"}),(0,r.jsx)("button",{onClick:C,disabled:m,className:l().placeOrderBtn,children:m?"Processing...":`Place Order - ${k(q)}`})]})]})]}),(0,r.jsxs)("div",{className:l().orderSummary,children:[(0,r.jsx)("h2",{className:l().summaryTitle,children:"Order Summary"}),(0,r.jsx)("div",{className:l().orderItems,children:e.cart.cartItems.map(e=>(0,r.jsxs)("div",{className:l().orderItem,children:[(0,r.jsx)("img",{src:e.product?.images?.[0]||"/images/placeholder-product.jpg",alt:e.product?.name||"Product",className:l().itemImage}),(0,r.jsxs)("div",{className:l().itemDetails,children:[(0,r.jsx)("h4",{children:e.product?.name}),(0,r.jsxs)("p",{children:["Qty: ",e.quantity]})]}),(0,r.jsx)("div",{className:l().itemPrice,children:k(e.quantity*(e.product?.price||0))})]},e.id))}),(0,r.jsxs)("div",{className:l().summaryTotals,children:[(0,r.jsxs)("div",{className:l().summaryRow,children:[(0,r.jsx)("span",{children:"Subtotal"}),(0,r.jsx)("span",{children:k(b)})]}),(0,r.jsxs)("div",{className:l().summaryRow,children:[(0,r.jsx)("span",{children:"Shipping"}),(0,r.jsx)("span",{children:0===I?"Free":k(I)})]}),(0,r.jsxs)("div",{className:l().summaryRow,children:[(0,r.jsx)("span",{children:"Tax"}),(0,r.jsx)("span",{children:k(P)})]}),(0,r.jsxs)("div",{className:l().totalRow,children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsx)("span",{children:k(q)})]})]})]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},42379:e=>{e.exports={container:"checkout_container__GDzOa",header:"checkout_header__ZlUxF",title:"checkout_title___IbnB",stepIndicator:"checkout_stepIndicator__TbDdQ",step:"checkout_step__aZu3e",active:"checkout_active__O_QWG",stepConnector:"checkout_stepConnector__YY3Vn",checkoutContent:"checkout_checkoutContent__7gpwU",mainContent:"checkout_mainContent__ecFbb",sectionTitle:"checkout_sectionTitle__Dq1nj",formGrid:"checkout_formGrid__zhLnj",formGroup:"checkout_formGroup__camUU",fullWidth:"checkout_fullWidth__TkhjX",paymentMethods:"checkout_paymentMethods__zaLSU",paymentMethod:"checkout_paymentMethod__aUipB",selected:"checkout_selected__LvBEE",paymentIcon:"checkout_paymentIcon__VPo4i",paymentInfo:"checkout_paymentInfo__rTAE_",radioButton:"checkout_radioButton__KAaLE",stepActions:"checkout_stepActions__04QJM",nextBtn:"checkout_nextBtn__hVwUq",placeOrderBtn:"checkout_placeOrderBtn__sCYwg",backBtn:"checkout_backBtn__Luemm",orderSummary:"checkout_orderSummary__FW5Y2",summaryTitle:"checkout_summaryTitle__dOGEb",orderItems:"checkout_orderItems__oGO5Z",orderItem:"checkout_orderItem__dHNHt",itemImage:"checkout_itemImage__OoMnu",itemDetails:"checkout_itemDetails__LL_iV",itemPrice:"checkout_itemPrice__rfOXL",summaryTotals:"checkout_summaryTotals__82PWD",summaryRow:"checkout_summaryRow__tA3kQ",totalRow:"checkout_totalRow__uLN8_",errorMessage:"checkout_errorMessage__WSOsj"}},50451:(e,t,s)=>{Promise.resolve().then(s.bind(s,30277))},54787:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\checkout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\checkout\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},97307:(e,t,s)=>{Promise.resolve().then(s.bind(s,54787))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,72,658,913],()=>s(9238));module.exports=r})();