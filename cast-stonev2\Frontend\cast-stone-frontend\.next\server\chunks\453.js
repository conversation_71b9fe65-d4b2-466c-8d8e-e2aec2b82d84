exports.id=453,exports.ids=[453],exports.modules={34856:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var c=a(60687),i=a(43210),t=a(79295),l=a.n(t);let n=({product:e})=>{let[s,a]=(0,i.useState)("specifications"),t=e=>{a(s===e?null:e)},n=e.productSpecifications&&Object.values(e.productSpecifications).some(e=>"string"==typeof e?""!==e.trim():null!=e),o=e.productDetails&&Object.values(e.productDetails).some(e=>"string"==typeof e?""!==e.trim():null!=e),r=e.downloadableContent&&Object.values(e.downloadableContent).some(e=>"string"==typeof e?""!==e.trim():null!=e);return(0,c.jsxs)("div",{className:l().specificationsContainer,children:[(0,c.jsxs)("div",{className:l().section,children:[(0,c.jsxs)("button",{className:`${l().sectionHeader} ${"specifications"===s?l().active:""}`,onClick:()=>t("specifications"),children:[(0,c.jsx)("span",{children:"Product Specifications"}),(0,c.jsx)("span",{className:l().toggleIcon,children:"specifications"===s?"−":"+"})]}),"specifications"===s&&n&&(0,c.jsx)("div",{className:l().sectionContent,children:(0,c.jsxs)("div",{className:l().specGrid,children:[(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Availability:"}),(0,c.jsx)("span",{className:l().specValue,children:e.stock>0?"Limited Inventory. (Ships within 13 weeks of order placement.)":"Out of Stock"})]}),e.productSpecifications?.pieces&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Pieces:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.pieces})]}),e.productSpecifications?.material&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Material:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.material})]}),e.productSpecifications?.dimensions&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Dimensions:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.dimensions})]}),e.productSpecifications?.totalWeight&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Total Weight:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.totalWeight})]}),e.productSpecifications?.weightWithWater&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Weight With Water:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.weightWithWater})]}),e.productSpecifications?.base_Dimensions&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Base Dimensions:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.base_Dimensions})]}),e.productSpecifications?.photographed_In&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Photographed In:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.photographed_In})]}),e.productSpecifications?.waterVolume&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Water Volume:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productSpecifications.waterVolume})]}),e.tags&&e.tags.length>0&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Tags:"}),(0,c.jsx)("span",{className:l().specValue,children:(0,c.jsx)("div",{className:l().tagContainer,children:e.tags.map((e,s)=>(0,c.jsx)("span",{className:l().tag,children:e},s))})})]})]})})]}),(0,c.jsxs)("div",{className:l().section,children:[(0,c.jsxs)("button",{className:`${l().sectionHeader} ${"details"===s?l().active:""}`,onClick:()=>t("details"),children:[(0,c.jsx)("span",{children:"Product Details"}),(0,c.jsx)("span",{className:l().toggleIcon,children:"details"===s?"−":"+"})]}),"details"===s&&(0,c.jsx)("div",{className:l().sectionContent,children:(0,c.jsxs)("div",{className:l().detailsContent,children:[e.description&&(0,c.jsx)("p",{className:l().description,children:e.description}),o&&(0,c.jsxs)("div",{className:l().specGrid,children:[e.productDetails?.upc&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"UPC:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.upc})]}),e.productDetails?.indoorUseOnly&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Indoor Use Only:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.indoorUseOnly})]}),e.productDetails?.assemblyRequired&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Assembly Required:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.assemblyRequired})]}),e.productDetails?.easeOfAssembly&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Ease of Assembly:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.easeOfAssembly})]}),e.productDetails?.assistanceRequired&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Assistance Required:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.assistanceRequired})]}),e.productDetails?.splashLevel&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Splash Level:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.splashLevel})]}),e.productDetails?.soundLevel&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Sound Level:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.soundLevel})]}),e.productDetails?.soundType&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Sound Type:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.soundType})]}),e.productDetails?.replacementPumpKit&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Replacement Pump Kit:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.replacementPumpKit})]}),e.productDetails?.electricalCordLength&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Electrical Cord Length:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.electricalCordLength})]}),e.productDetails?.pumpSize&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Pump Size:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.pumpSize})]}),e.productDetails?.shipMethod&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Ship Method:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.shipMethod})]}),e.productDetails?.drainage_Info&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Drainage Info:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.drainage_Info})]}),e.productDetails?.inside_Top&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Inside Top:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.inside_Top})]}),e.productDetails?.inside_Bottom&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Inside Bottom:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.inside_Bottom})]}),e.productDetails?.inside_Height&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Inside Height:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.inside_Height})]}),e.productDetails?.factory_Code&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Factory Code:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.factory_Code})]}),e.productDetails?.catalogPage&&(0,c.jsxs)("div",{className:l().specRow,children:[(0,c.jsx)("span",{className:l().specLabel,children:"Catalog Page:"}),(0,c.jsx)("span",{className:l().specValue,children:e.productDetails.catalogPage})]})]})]})})]}),(0,c.jsxs)("div",{className:l().section,children:[(0,c.jsxs)("button",{className:`${l().sectionHeader} ${"care"===s?l().active:""}`,onClick:()=>t("care"),children:[(0,c.jsx)("span",{children:"Product Care and Downloadable Content"}),(0,c.jsx)("span",{className:l().toggleIcon,children:"care"===s?"−":"+"})]}),"care"===s&&(0,c.jsx)("div",{className:l().sectionContent,children:(0,c.jsx)("div",{className:l().careContent,children:r&&(0,c.jsxs)("div",{className:l().downloadSection,children:[(0,c.jsx)("h4",{children:"Downloadable Content:"}),(0,c.jsxs)("div",{className:l().downloadLinks,children:[e.downloadableContent?.care&&(0,c.jsx)("a",{href:e.downloadableContent.care,target:"_blank",rel:"noopener noreferrer",className:l().downloadLink,children:"\uD83D\uDCC4 Care Instructions"}),e.downloadableContent?.productInstructions&&(0,c.jsx)("a",{href:e.downloadableContent.productInstructions,target:"_blank",rel:"noopener noreferrer",className:l().downloadLink,children:"\uD83D\uDCCB Product Instructions"}),e.downloadableContent?.cad&&(0,c.jsx)("a",{href:e.downloadableContent.cad,target:"_blank",rel:"noopener noreferrer",className:l().downloadLink,children:"\uD83D\uDCD0 CAD Files"})]})]})})})]}),(0,c.jsxs)("div",{className:l().shareSection,children:[(0,c.jsx)("span",{className:l().shareLabel,children:"Share"}),(0,c.jsxs)("div",{className:l().shareButtons,children:[(0,c.jsx)("button",{className:l().shareButton,"aria-label":"Share on Facebook",children:(0,c.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,c.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),(0,c.jsx)("button",{className:l().shareButton,"aria-label":"Share on Pinterest",children:(0,c.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,c.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-12.014C24.007 5.36 18.641.001 12.017.001z"})})})]})]})]})}},45727:e=>{e.exports={imageGallery:"productImageGallery_imageGallery__n5jcJ",mainImageContainer:"productImageGallery_mainImageContainer__2d6Gi",mainImage:"productImageGallery_mainImage__de06d",zoomed:"productImageGallery_zoomed__gW04g",navButton:"productImageGallery_navButton__pmcWA",prevButton:"productImageGallery_prevButton__r4pvX",nextButton:"productImageGallery_nextButton__Ffk19",imageCounter:"productImageGallery_imageCounter__Fn2cW",zoomIndicator:"productImageGallery_zoomIndicator__w_xth",thumbnailContainer:"productImageGallery_thumbnailContainer__5rAZJ",thumbnailGrid:"productImageGallery_thumbnailGrid__S8J1M",thumbnail:"productImageGallery_thumbnail__Im2ef",thumbnailImage:"productImageGallery_thumbnailImage__1Bb5B",activeThumbnail:"productImageGallery_activeThumbnail__EslMu",zoomOverlay:"productImageGallery_zoomOverlay__2Jwxo",zoomedImageContainer:"productImageGallery_zoomedImageContainer__8vdeq",zoomedImage:"productImageGallery_zoomedImage__2up2d",closeZoomButton:"productImageGallery_closeZoomButton__jb9zG"}},70440:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>i});var c=a(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,c.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79295:e=>{e.exports={specificationsContainer:"productSpecifications_specificationsContainer__ExsJP",section:"productSpecifications_section__yTpdS",sectionHeader:"productSpecifications_sectionHeader__OfKR9",active:"productSpecifications_active___ctQh",toggleIcon:"productSpecifications_toggleIcon__aGGE3",sectionContent:"productSpecifications_sectionContent__lbyYW",specGrid:"productSpecifications_specGrid__R0hxs",keySpecsTable:"productSpecifications_keySpecsTable__7L5Ew",specRow:"productSpecifications_specRow__CWtye",specLabel:"productSpecifications_specLabel__9qLUs",specValue:"productSpecifications_specValue__w6W_M",inStock:"productSpecifications_inStock__0aNgb",outOfStock:"productSpecifications_outOfStock__kBj_Z",tagContainer:"productSpecifications_tagContainer__iOz89",tag:"productSpecifications_tag__9z9rJ",detailsContent:"productSpecifications_detailsContent__4Okl4",description:"productSpecifications_description__bGf76",featureList:"productSpecifications_featureList__hrnf4",careContent:"productSpecifications_careContent__JIGsc",careSection:"productSpecifications_careSection__j_YYe",downloadSection:"productSpecifications_downloadSection__vziAC",downloadLinks:"productSpecifications_downloadLinks__wUENX",downloadLink:"productSpecifications_downloadLink__eqrqE",shareSection:"productSpecifications_shareSection__Akriu",shareLabel:"productSpecifications_shareLabel__PZjiC",shareButtons:"productSpecifications_shareButtons__reB6Z",shareButton:"productSpecifications_shareButton__G57wC"}},85592:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var c=a(60687),i=a(43210),t=a(45727),l=a.n(t);let n=({images:e,productName:s})=>{let[a,t]=(0,i.useState)(0),[n,o]=(0,i.useState)(!1),r=e.length>0?e:["/images/placeholder-product.jpg"],d=r[a],p=e=>{t(e),o(!1)};return(0,c.jsxs)("div",{className:l().imageGallery,children:[(0,c.jsxs)("div",{className:l().mainImageContainer,children:[(0,c.jsx)("img",{src:d,alt:`${s} - Image ${a+1}`,className:`${l().mainImage} ${n?l().zoomed:""}`,onClick:()=>{o(!n)}}),r.length>1&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("button",{className:`${l().navButton} ${l().prevButton}`,onClick:()=>{t(e=>0===e?r.length-1:e-1),o(!1)},"aria-label":"Previous image",children:(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,c.jsx)("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,c.jsx)("button",{className:`${l().navButton} ${l().nextButton}`,onClick:()=>{t(e=>e===r.length-1?0:e+1),o(!1)},"aria-label":"Next image",children:(0,c.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:(0,c.jsx)("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),r.length>1&&(0,c.jsxs)("div",{className:l().imageCounter,children:[a+1," / ",r.length]}),(0,c.jsxs)("div",{className:l().zoomIndicator,children:[(0,c.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:[(0,c.jsx)("circle",{cx:"11",cy:"11",r:"8",stroke:"currentColor",strokeWidth:"2"}),(0,c.jsx)("path",{d:"M21 21L16.65 16.65",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),(0,c.jsx)("line",{x1:"11",y1:"8",x2:"11",y2:"14",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),(0,c.jsx)("line",{x1:"8",y1:"11",x2:"14",y2:"11",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})]}),"Click to zoom"]})]}),r.length>1&&(0,c.jsx)("div",{className:l().thumbnailContainer,children:(0,c.jsx)("div",{className:l().thumbnailGrid,children:r.map((e,i)=>(0,c.jsx)("button",{className:`${l().thumbnail} ${i===a?l().activeThumbnail:""}`,onClick:()=>p(i),"aria-label":`View image ${i+1}`,children:(0,c.jsx)("img",{src:e,alt:`${s} thumbnail ${i+1}`,className:l().thumbnailImage})},i))})}),n&&(0,c.jsx)("div",{className:l().zoomOverlay,onClick:()=>o(!1),children:(0,c.jsxs)("div",{className:l().zoomedImageContainer,children:[(0,c.jsx)("img",{src:d,alt:`${s} - Zoomed view`,className:l().zoomedImage}),(0,c.jsx)("button",{className:l().closeZoomButton,onClick:()=>o(!1),"aria-label":"Close zoom view",children:(0,c.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:[(0,c.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"}),(0,c.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round"})]})})]})})]})}},86631:e=>{e.exports={relatedProducts:"relatedProducts_relatedProducts__AL9MF",sectionHeader:"relatedProducts_sectionHeader__W8wkl",sectionTitle:"relatedProducts_sectionTitle__aMxvg",scrollControls:"relatedProducts_scrollControls__U4JgE",scrollButton:"relatedProducts_scrollButton__XwIzU",disabled:"relatedProducts_disabled__uoeUy",productsContainer:"relatedProducts_productsContainer__90DuD",productsGrid:"relatedProducts_productsGrid__A4_Fq",productCard:"relatedProducts_productCard__MMP_j",productLink:"relatedProducts_productLink__jhjXd",imageContainer:"relatedProducts_imageContainer__KaBlC",productImage:"relatedProducts_productImage__it2PW",outOfStockOverlay:"relatedProducts_outOfStockOverlay__4FsDH",productInfo:"relatedProducts_productInfo__4uK6T",productName:"relatedProducts_productName__D5nFe",productDetails:"relatedProducts_productDetails__dYa_Q",productCode:"relatedProducts_productCode__9f972",availability:"relatedProducts_availability__7N8Ht",inStock:"relatedProducts_inStock__fE8y6",outOfStock:"relatedProducts_outOfStock__70Ee2",priceContainer:"relatedProducts_priceContainer__BS15X",price:"relatedProducts_price__nPp4W"}},94704:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var c=a(60687),i=a(43210),t=a(85814),l=a.n(t),n=a(86631),o=a.n(n);let r=({products:e})=>{let[s,a]=(0,i.useState)(0),[t,n]=(0,i.useState)(!1),[r,d]=(0,i.useState)(!0),p=(0,i.useRef)(null),u=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),m=()=>{if(p.current){let{scrollLeft:e,scrollWidth:s,clientWidth:a}=p.current;n(e>0),d(e<s-a-1)}};return((0,i.useEffect)(()=>{let e=p.current;if(e)return m(),e.addEventListener("scroll",m),()=>e.removeEventListener("scroll",m)},[e]),e&&0!==e.length)?(0,c.jsxs)("div",{className:o().relatedProducts,children:[(0,c.jsxs)("div",{className:o().sectionHeader,children:[(0,c.jsx)("h2",{className:o().sectionTitle,children:"You May Also Like"}),e.length>3&&(0,c.jsxs)("div",{className:o().scrollControls,children:[(0,c.jsx)("button",{className:`${o().scrollButton} ${!t?o().disabled:""}`,onClick:()=>{p.current&&p.current.scrollBy({left:-320,behavior:"smooth"})},disabled:!t,"aria-label":"Scroll left",children:(0,c.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:(0,c.jsx)("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,c.jsx)("button",{className:`${o().scrollButton} ${!r?o().disabled:""}`,onClick:()=>{p.current&&p.current.scrollBy({left:320,behavior:"smooth"})},disabled:!r,"aria-label":"Scroll right",children:(0,c.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",children:(0,c.jsx)("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]}),(0,c.jsx)("div",{className:o().productsContainer,ref:p,children:(0,c.jsx)("div",{className:o().productsGrid,children:e.map(e=>{let s=e.images&&e.images.length>0?e.images[0]:"/images/placeholder-product.jpg",a=e.stock>0;return(0,c.jsx)("div",{className:o().productCard,children:(0,c.jsxs)(l(),{href:`/products/${e.id}`,className:o().productLink,children:[(0,c.jsxs)("div",{className:o().imageContainer,children:[(0,c.jsx)("img",{src:s,alt:e.name,className:o().productImage}),!a&&(0,c.jsx)("div",{className:o().outOfStockOverlay,children:(0,c.jsx)("span",{children:"Out of Stock"})})]}),(0,c.jsxs)("div",{className:o().productInfo,children:[(0,c.jsx)("h3",{className:o().productName,children:e.name}),(0,c.jsxs)("div",{className:o().productDetails,children:[(0,c.jsxs)("span",{className:o().productCode,children:["P-",e.id.toString().padStart(3,"0"),"-AS"]}),(0,c.jsx)("div",{className:o().availability,children:a?(0,c.jsx)("span",{className:o().inStock,children:"Available in 14 Colors And 3 sizes"}):(0,c.jsx)("span",{className:o().outOfStock,children:"Currently Out of Stock"})})]}),(0,c.jsx)("div",{className:o().priceContainer,children:(0,c.jsx)("span",{className:o().price,children:u(e.price)})})]})]})},e.id)})})})]}):null}}};