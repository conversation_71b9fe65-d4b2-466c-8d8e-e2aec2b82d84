/* [project]/src/app/catalog/catalog.module.css [app-client] (css) */
.catalog-module__ZyI-Aq__container {
  color: #fff;
  background: #1a1a2e;
  min-height: 100vh;
  margin-top: 4.2rem;
  padding: 8rem 1rem 2rem;
}

.catalog-module__ZyI-Aq__header {
  text-align: center;
  max-width: 800px;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-right: auto;
}

.catalog-module__ZyI-Aq__title {
  color: #fff;
  margin-bottom: 1rem;
  font-size: 3rem;
  font-weight: 700;
}

.catalog-module__ZyI-Aq__subtitle {
  color: #b0b0b0;
  font-size: 1.2rem;
  line-height: 1.6;
}

.catalog-module__ZyI-Aq__content {
  max-width: 1200px;
  margin: 0 auto;
}

.catalog-module__ZyI-Aq__catalogOptions {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
  display: grid;
}

.catalog-module__ZyI-Aq__catalogCard {
  color: inherit;
  text-align: center;
  background: #2a2a3e;
  border: 2px solid #3a3a4e;
  border-radius: 16px;
  padding: 3rem 2rem;
  text-decoration: none;
  transition: all .3s;
}

.catalog-module__ZyI-Aq__catalogCard:hover {
  border-color: #4a90e2;
  transform: translateY(-5px);
  box-shadow: 0 10px 30px #4a90e233;
}

.catalog-module__ZyI-Aq__cardIcon {
  color: #4a90e2;
  margin-bottom: 1.5rem;
}

.catalog-module__ZyI-Aq__catalogCard h3 {
  color: #fff;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.catalog-module__ZyI-Aq__catalogCard p {
  color: #b0b0b0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.catalog-module__ZyI-Aq__cardAction {
  color: #4a90e2;
  font-size: 1rem;
  font-weight: 500;
}

.catalog-module__ZyI-Aq__features {
  background: #2a2a3e;
  border: 2px solid #3a3a4e;
  border-radius: 16px;
  padding: 3rem 2rem;
}

.catalog-module__ZyI-Aq__features h2 {
  text-align: center;
  color: #fff;
  margin-bottom: 2rem;
  font-size: 2rem;
  font-weight: 600;
}

.catalog-module__ZyI-Aq__featureGrid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  display: grid;
}

.catalog-module__ZyI-Aq__feature {
  background: #3a3a4e;
  border: 1px solid #4a4a5e;
  border-radius: 12px;
  padding: 2rem;
}

.catalog-module__ZyI-Aq__feature h4 {
  color: #4a90e2;
  margin-bottom: .5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

.catalog-module__ZyI-Aq__feature p {
  color: #b0b0b0;
  line-height: 1.5;
}

@media (width <= 768px) {
  .catalog-module__ZyI-Aq__title {
    font-size: 2rem;
  }

  .catalog-module__ZyI-Aq__catalogOptions, .catalog-module__ZyI-Aq__featureGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .catalog-module__ZyI-Aq__container {
    padding: 1rem;
  }

  .catalog-module__ZyI-Aq__catalogCard, .catalog-module__ZyI-Aq__features {
    padding: 2rem 1rem;
  }
}


/*# sourceMappingURL=src_app_catalog_catalog_module_788356ae.css.map*/