/* [project]/src/app/admin/dashboard/wholesale-buyers/page.module.css [app-client] (css) */
.page-module__cnDrva__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.page-module__cnDrva__header {
  margin-bottom: 2rem;
}

.page-module__cnDrva__header h1 {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 2rem;
  font-weight: 700;
}

.page-module__cnDrva__header p {
  color: #6b7280;
  margin: 0;
  font-size: 1rem;
}

.page-module__cnDrva__errorBanner {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  display: flex;
}

.page-module__cnDrva__errorBanner p {
  margin: 0;
  font-weight: 500;
}

.page-module__cnDrva__closeError {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 1.5rem;
  display: flex;
}

.page-module__cnDrva__loadingContainer {
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4rem;
  display: flex;
}

.page-module__cnDrva__loadingSpinner {
  border: 4px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  animation: 1s linear infinite page-module__cnDrva__spin;
}

@keyframes page-module__cnDrva__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.page-module__cnDrva__statsGrid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  display: grid;
}

.page-module__cnDrva__statCard {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px #0000001a;
}

.page-module__cnDrva__statCard h3 {
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: .5px;
  margin: 0 0 .5rem;
  font-size: .875rem;
  font-weight: 500;
}

.page-module__cnDrva__statNumber {
  color: #1f2937;
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
}

.page-module__cnDrva__statNumber.page-module__cnDrva__pending {
  color: #f59e0b;
}

.page-module__cnDrva__statNumber.page-module__cnDrva__approved {
  color: #10b981;
}

.page-module__cnDrva__statNumber.page-module__cnDrva__rejected {
  color: #ef4444;
}

.page-module__cnDrva__filters {
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
}

.page-module__cnDrva__filterGroup {
  flex-direction: column;
  gap: .5rem;
  display: flex;
}

.page-module__cnDrva__filterGroup label {
  color: #374151;
  font-size: .875rem;
  font-weight: 500;
}

.page-module__cnDrva__searchInput, .page-module__cnDrva__statusFilter {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  min-width: 200px;
  padding: .5rem .75rem;
  font-size: .875rem;
}

.page-module__cnDrva__searchInput:focus, .page-module__cnDrva__statusFilter:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.page-module__cnDrva__tableContainer {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px #0000001a;
}

.page-module__cnDrva__table {
  border-collapse: collapse;
  width: 100%;
}

.page-module__cnDrva__table th {
  text-align: left;
  color: #374151;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: .75rem 1rem;
  font-size: .875rem;
  font-weight: 600;
}

.page-module__cnDrva__table td {
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  padding: .75rem 1rem;
  font-size: .875rem;
}

.page-module__cnDrva__table tbody tr:hover {
  background: #f9fafb;
}

.page-module__cnDrva__statusBadge {
  text-transform: uppercase;
  letter-spacing: .5px;
  border-radius: 9999px;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 600;
  display: inline-block;
}

.page-module__cnDrva__statusPending {
  color: #92400e;
  background: #fef3c7;
}

.page-module__cnDrva__statusApproved {
  color: #065f46;
  background: #d1fae5;
}

.page-module__cnDrva__statusRejected {
  color: #991b1b;
  background: #fee2e2;
}

.page-module__cnDrva__viewButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 4px;
  padding: .375rem .75rem;
  font-size: .75rem;
  font-weight: 500;
  transition: background-color .2s;
}

.page-module__cnDrva__viewButton:hover {
  background: #2563eb;
}

.page-module__cnDrva__emptyState {
  text-align: center;
  color: #6b7280;
  padding: 3rem;
}

.page-module__cnDrva__modalOverlay {
  z-index: 1000;
  background: #00000080;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  display: flex;
  position: fixed;
  inset: 0;
}

.page-module__cnDrva__modal {
  background: #fff;
  border-radius: 12px;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  overflow: hidden;
}

.page-module__cnDrva__modalHeader {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  display: flex;
}

.page-module__cnDrva__modalHeader h2 {
  color: #1f2937;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-module__cnDrva__closeButton {
  cursor: pointer;
  color: #6b7280;
  background: none;
  border: none;
  border-radius: 4px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  font-size: 1.5rem;
  display: flex;
}

.page-module__cnDrva__closeButton:hover {
  background: #f3f4f6;
}

.page-module__cnDrva__modalContent {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.page-module__cnDrva__buyerInfo h3 {
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  margin: 2rem 0 1rem;
  padding-bottom: .5rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.page-module__cnDrva__buyerInfo h3:first-child {
  margin-top: 0;
}

.page-module__cnDrva__infoGrid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  display: grid;
}

.page-module__cnDrva__infoGrid div, .page-module__cnDrva__address {
  color: #374151;
  font-size: .875rem;
  line-height: 1.5;
}

.page-module__cnDrva__hearAbout {
  flex-wrap: wrap;
  gap: .5rem;
  margin-bottom: 1rem;
  display: flex;
}

.page-module__cnDrva__tag {
  color: #3730a3;
  background: #e0e7ff;
  border-radius: 9999px;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 500;
}

.page-module__cnDrva__comments {
  color: #374151;
  background: #f9fafb;
  border-radius: 6px;
  margin: 0;
  padding: 1rem;
  font-size: .875rem;
  line-height: 1.5;
}

.page-module__cnDrva__statusInfo {
  background: #f9fafb;
  border-radius: 6px;
  padding: 1rem;
}

.page-module__cnDrva__statusInfo p {
  color: #374151;
  margin: 0 0 .5rem;
  font-size: .875rem;
}

.page-module__cnDrva__statusInfo p:last-child {
  margin-bottom: 0;
}

.page-module__cnDrva__modalActions {
  border-top: 1px solid #e5e7eb;
  gap: 1rem;
  padding: 1.5rem 2rem;
  display: flex;
}

.page-module__cnDrva__approveButton {
  color: #fff;
  cursor: pointer;
  background: #10b981;
  border: none;
  border-radius: 6px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: background-color .2s;
}

.page-module__cnDrva__approveButton:hover:not(:disabled) {
  background: #059669;
}

.page-module__cnDrva__rejectButton {
  color: #fff;
  cursor: pointer;
  background: #ef4444;
  border: none;
  border-radius: 6px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: background-color .2s;
}

.page-module__cnDrva__rejectButton:hover:not(:disabled) {
  background: #dc2626;
}

.page-module__cnDrva__rejectForm {
  flex: 1;
}

.page-module__cnDrva__rejectTextarea {
  resize: vertical;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 100%;
  margin-bottom: 1rem;
  padding: .75rem;
  font-size: .875rem;
}

.page-module__cnDrva__rejectTextarea:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.page-module__cnDrva__rejectActions {
  gap: 1rem;
  display: flex;
}

.page-module__cnDrva__confirmRejectButton {
  color: #fff;
  cursor: pointer;
  background: #ef4444;
  border: none;
  border-radius: 6px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: background-color .2s;
}

.page-module__cnDrva__confirmRejectButton:hover:not(:disabled) {
  background: #dc2626;
}

.page-module__cnDrva__confirmRejectButton:disabled {
  cursor: not-allowed;
  background: #9ca3af;
}

.page-module__cnDrva__cancelButton {
  color: #374151;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: .75rem 1.5rem;
  font-weight: 600;
  transition: all .2s;
}

.page-module__cnDrva__cancelButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.page-module__cnDrva__approveButton:disabled, .page-module__cnDrva__rejectButton:disabled, .page-module__cnDrva__cancelButton:disabled {
  opacity: .5;
  cursor: not-allowed;
}

@media (width <= 768px) {
  .page-module__cnDrva__container {
    padding: 1rem;
  }

  .page-module__cnDrva__filters {
    flex-direction: column;
  }

  .page-module__cnDrva__searchInput, .page-module__cnDrva__statusFilter {
    min-width: auto;
  }

  .page-module__cnDrva__table {
    font-size: .75rem;
  }

  .page-module__cnDrva__table th, .page-module__cnDrva__table td, .page-module__cnDrva__modalOverlay {
    padding: .5rem;
  }

  .page-module__cnDrva__modalContent {
    padding: 1rem;
  }

  .page-module__cnDrva__modalActions {
    flex-direction: column;
    padding: 1rem;
  }

  .page-module__cnDrva__rejectActions {
    flex-direction: column;
  }
}


/*# sourceMappingURL=src_app_admin_dashboard_wholesale-buyers_page_module_add8e820.css.map*/