{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/contexts/AdminAuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { User } from '@/services/types/entities';\nimport { userGetService } from '@/services/api/users/get';\n\ninterface AdminAuthContextType {\n  admin: User | null;\n  isLoading: boolean;\n  login: (email: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  isAuthenticated: boolean;\n}\n\nconst AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);\n\ninterface AdminAuthProviderProps {\n  children: ReactNode;\n}\n\nexport function AdminAuthProvider({ children }: AdminAuthProviderProps) {\n  const [admin, setAdmin] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkExistingSession = () => {\n      try {\n        const storedAdmin = localStorage.getItem('admin_session');\n        if (storedAdmin) {\n          const adminData = JSON.parse(storedAdmin);\n          // Verify the session is still valid (simple check)\n          if (adminData.email && adminData.role === 'admin') {\n            setAdmin(adminData);\n          } else {\n            localStorage.removeItem('admin_session');\n          }\n        }\n      } catch (error) {\n        console.error('Error checking existing session:', error);\n        localStorage.removeItem('admin_session');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkExistingSession();\n  }, []);\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true);\n    try {\n      const adminUser = await userGetService.validateAdminCredentials(email, password);\n      \n      if (adminUser) {\n        setAdmin(adminUser);\n        // Store session in localStorage (in production, use secure httpOnly cookies)\n        localStorage.setItem('admin_session', JSON.stringify(adminUser));\n        return true;\n      }\n      \n      return false;\n    } catch (error) {\n      console.error('Login error:', error);\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setAdmin(null);\n    localStorage.removeItem('admin_session');\n  };\n\n  const value: AdminAuthContextType = {\n    admin,\n    isLoading,\n    login,\n    logout,\n    isAuthenticated: !!admin\n  };\n\n  return (\n    <AdminAuthContext.Provider value={value}>\n      {children}\n    </AdminAuthContext.Provider>\n  );\n}\n\nexport function useAdminAuth() {\n  const context = useContext(AdminAuthContext);\n  if (context === undefined) {\n    throw new Error('useAdminAuth must be used within an AdminAuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAcA,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,SAAS,kBAAkB,EAAE,QAAQ,EAA0B;;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;oEAAuB;oBAC3B,IAAI;wBACF,MAAM,cAAc,aAAa,OAAO,CAAC;wBACzC,IAAI,aAAa;4BACf,MAAM,YAAY,KAAK,KAAK,CAAC;4BAC7B,mDAAmD;4BACnD,IAAI,UAAU,KAAK,IAAI,UAAU,IAAI,KAAK,SAAS;gCACjD,SAAS;4BACX,OAAO;gCACL,aAAa,UAAU,CAAC;4BAC1B;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,aAAa,UAAU,CAAC;oBAC1B,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QACb,IAAI;YACF,MAAM,YAAY,MAAM,yIAAA,CAAA,iBAAc,CAAC,wBAAwB,CAAC,OAAO;YAEvE,IAAI,WAAW;gBACb,SAAS;gBACT,6EAA6E;gBAC7E,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBACrD,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,SAAS;QACT,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,QAA8B;QAClC;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;GApEgB;KAAA;AAsET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}