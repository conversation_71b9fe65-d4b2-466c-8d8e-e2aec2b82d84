exports.id=190,exports.ids=[190],exports.modules={1255:(e,t,r)=>{Promise.resolve().then(r.bind(r,41210))},3844:(e,t,r)=>{"use strict";r.d(t,{AdminAuthProvider:()=>i,b:()=>l});var s=r(60687),a=r(43210),n=r(58890);let o=(0,a.createContext)(void 0);function i({children:e}){let[t,r]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),d=async(e,t)=>{l(!0);try{let s=await n.userGetService.validateAdminCredentials(e,t);if(s)return r(s),localStorage.setItem("admin_session",JSON.stringify(s)),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{l(!1)}};return(0,s.jsx)(o.Provider,{value:{admin:t,isLoading:i,login:d,logout:()=>{r(null),localStorage.removeItem("admin_session")},isAuthenticated:!!t},children:e})}function l(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAdminAuth must be used within an AdminAuthProvider");return e}},40575:(e,t,r)=>{Promise.resolve().then(r.bind(r,3844))},41210:(e,t,r)=>{"use strict";r.d(t,{AdminAuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call AdminAuthProvider() from the server but AdminAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx","AdminAuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAdminAuth() from the server but useAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx","useAdminAuth")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73441:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(60687),a=r(43210),n=r(16189),o=r(3844);function i({children:e}){let[t,r]=(0,a.useState)(!0),{admin:i,logout:l}=(0,o.b)(),d=(0,n.useRouter)(),c=(0,n.usePathname)(),m=[{name:"Dashboard",href:"/admin/dashboard",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"})})},{name:"Collections",href:"/admin/dashboard/collections",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})},{name:"Products",href:"/admin/dashboard/products",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})},{name:"Images",href:"/admin/dashboard/images",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})},{name:"Orders",href:"/admin/dashboard/orders",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{name:"Contact Submissions",href:"/admin/dashboard/contact-submissions",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})},{name:"Wholesale Buyers",href:"/admin/dashboard/wholesale-buyers",icon:(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}];return(0,s.jsxs)("div",{className:"flex h-screen bg-black",children:[(0,s.jsxs)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${t?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 border-r border-white`,children:[(0,s.jsx)("div",{className:"flex items-center justify-center h-16 px-4 bg-black",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-white",children:"Cast Stone Admin"})}),(0,s.jsx)("nav",{className:"mt-8",children:(0,s.jsx)("div",{className:"px-4 space-y-2",children:m.map(e=>{let t=c===e.href;return(0,s.jsxs)("a",{href:e.href,className:`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${t?"bg-amber-100 text-amber-900 border-r-2 border-amber-900":"text-amber-800 hover:bg-amber-50 hover:text-amber-900"}`,children:[e.icon,(0,s.jsx)("span",{className:"ml-3",children:e.name})]},e.name)})})})]}),(0,s.jsxs)("div",{className:"lg:pl flex flex-col flex-1 h-full",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-black",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("button",{onClick:()=>r(!t),className:"lg:hidden p-2 rounded-md text-black hover:text-black hover:bg-black",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,s.jsx)("h2",{className:"ml-4 text-2xl font-bold text-black",children:m.find(e=>e.href===c)?.name||"Dashboard"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-sm text-black",children:["Welcome, ",(0,s.jsx)("span",{className:"font-semibold text-black",children:i?.email})]}),(0,s.jsx)("button",{onClick:()=>{l(),d.push("/admin/login")},className:"px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-black-800 transition-colors shadow-sm",children:"Logout"})]})]})}),(0,s.jsx)("main",{className:"flex-1 p-6 bg-white",children:e})]}),t&&(0,s.jsx)("div",{className:"fixed inset-0 z-40 bg-white bg-opacity-75 lg:hidden",onClick:()=>r(!1)})]})}},83645:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687);r(43210);var a=r(16189),n=r(3844);function o({children:e}){let{isAuthenticated:t,isLoading:r}=(0,n.b)();return((0,a.useRouter)(),r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-amber-900",children:"Loading..."})]})}):t?(0,s.jsx)(s.Fragment,{children:e}):null}},97629:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});var s=r(41459);class a{async uploadImage(e){let t=new FormData;t.append("image",e);try{let e=await fetch(`${this.baseUrl}/uploadImage`,{method:"POST",body:t});if(!e.ok){let t=await e.json();throw Error(t.message||"Failed to upload image")}return await e.json()}catch(e){throw console.error("Error uploading image:",e),e}}async getAllImages(){try{let e=await fetch(`${this.baseUrl}/images`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok){let t=await e.json();throw Error(t.message||"Failed to fetch images")}return(await e.json()).images}catch(e){throw console.error("Error fetching images:",e),e}}async deleteImage(e){try{let t=encodeURIComponent(e),r=await fetch(`${this.baseUrl}/images/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to delete image")}return await r.json()}catch(e){throw console.error("Error deleting image:",e),e}}async uploadMultipleImages(e){return Promise.all(e.map(e=>this.uploadImage(e)))}validateImageFile(e){return["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(e.type.toLowerCase())?e.size>0xa00000?{isValid:!1,error:"File size too large. Maximum size is 10MB."}:{isValid:!0}:{isValid:!1,error:"Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed."}}constructor(){this.baseUrl=`${s.lq}/cloudinary`}}let n=new a},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(37413),a=r(41210);let n={title:"Cast Stone Admin - Dashboard",description:"Admin dashboard for Cast Stone management"};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"antialiased bg-gray-50",children:(0,s.jsx)(a.AdminAuthProvider,{children:e})})})}}};