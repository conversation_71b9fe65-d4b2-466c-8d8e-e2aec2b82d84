{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/admin/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAdminAuth } from '@/contexts/AdminAuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading } = useAdminAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/admin/login');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-white\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto\"></div>\n          <p className=\"mt-4 text-amber-900\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null; // Will redirect to login\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IAClD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;;;;;;IAI3C;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBAAO;kBAAG;;AACZ;GA1BwB;;QACiB,uIAAA,CAAA,eAAY;QACpC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAdminAuth } from '@/contexts/AdminAuthContext';\r\n\r\ninterface AdminLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function AdminLayout({ children }: AdminLayoutProps) {\r\n  const [sidebarOpen, setSidebarOpen] = useState(true);\r\n  const { admin, logout } = useAdminAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n    router.push('/admin/login');\r\n  };\r\n\r\n  const navigation = [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/admin/dashboard',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      name: 'Collections',\r\n      href: '/admin/dashboard/collections',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      name: 'Products',\r\n      href: '/admin/dashboard/products',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      name: 'Images',\r\n      href: '/admin/dashboard/images',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      name: 'Orders',\r\n      href: '/admin/dashboard/orders',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      name: 'Contact Submissions',\r\n      href: '/admin/dashboard/contact-submissions',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n    {\r\n      name: 'Wholesale Buyers',\r\n      href: '/admin/dashboard/wholesale-buyers',\r\n      icon: (\r\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n        </svg>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"flex h-screen bg-black\">\r\n      {/* Sidebar */}\r\n      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 border-r border-white`}>\r\n        <div className=\"flex items-center justify-center h-16 px-4 bg-black\">\r\n          <h1 className=\"text-xl font-bold text-white\">Cast Stone Admin</h1>\r\n        </div>\r\n\r\n        <nav className=\"mt-8\">\r\n          <div className=\"px-4 space-y-2\">\r\n            {navigation.map((item) => {\r\n              const isActive = pathname === item.href;\r\n              return (\r\n                <a\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${\r\n                    isActive\r\n                      ? 'bg-amber-100 text-amber-900 border-r-2 border-amber-900'\r\n                      : 'text-amber-800 hover:bg-amber-50 hover:text-amber-900'\r\n                  }`}\r\n                >\r\n                  {item.icon}\r\n                  <span className=\"ml-3\">{item.name}</span>\r\n                </a>\r\n              );\r\n            })}\r\n          </div>\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div className={`lg:pl flex flex-col flex-1 h-full`}>\r\n        {/* Top header */}\r\n        <header className=\"bg-white shadow-sm border-b border-black\">\r\n          <div className=\"flex items-center justify-between px-6 py-4\">\r\n            <div className=\"flex items-center\">\r\n              <button\r\n                onClick={() => setSidebarOpen(!sidebarOpen)}\r\n                className=\"lg:hidden p-2 rounded-md text-black hover:text-black hover:bg-black\"\r\n              >\r\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\r\n                </svg>\r\n              </button>\r\n              <h2 className=\"ml-4 text-2xl font-bold text-black\">\r\n                {navigation.find(item => item.href === pathname)?.name || 'Dashboard'}\r\n              </h2>\r\n            </div>\r\n\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"text-sm text-black\">\r\n                Welcome, <span className=\"font-semibold text-black\">{admin?.email}</span>\r\n              </div>\r\n              <button\r\n                onClick={handleLogout}\r\n                className=\"px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-black-800 transition-colors shadow-sm\"\r\n              >\r\n                Logout\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Page content */}\r\n        <main className=\"flex-1 p-6 bg-white\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n\r\n      {/* Mobile sidebar overlay */}\r\n      {sidebarOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40 bg-white bg-opacity-75 lg:hidden\"\r\n          onClick={() => setSidebarOpen(false)}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,YAAY,EAAE,QAAQ,EAAoB;;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjE,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAC,8DAA8D,EAAE,cAAc,kBAAkB,oBAAoB,0GAA0G,CAAC;;kCAC9O,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAA+B;;;;;;;;;;;kCAG/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,4DACA,yDACJ;;wCAED,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;sDAAQ,KAAK,IAAI;;;;;;;mCAT5B,KAAK,IAAI;;;;;4BAYpB;;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAI,WAAW,CAAC,iCAAiC,CAAC;;kCAEjD,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDACX,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ;;;;;;;;;;;;8CAI9D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDAAqB;8DACzB,6LAAC;oDAAK,WAAU;8DAA4B,OAAO;;;;;;;;;;;;sDAE9D,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;YAKJ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;;;;;;;AAKxC;GA5JwB;;QAEI,uIAAA,CAAA,eAAY;QACvB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAJN", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport ProtectedRoute from '@/components/admin/ProtectedRoute';\r\nimport AdminLayout from '@/components/admin/AdminLayout';\r\nimport { collectionService, productService, orderService, userService, contactFormService } from '@/services';\r\n\r\ninterface DashboardStats {\r\n  totalCollections: number;\r\n  totalProducts: number;\r\n  totalOrders: number;\r\n  totalUsers: number;\r\n  totalContactSubmissions: number;\r\n  lowStockProducts: number;\r\n  pendingOrders: number;\r\n}\r\n\r\nexport default function AdminDashboardPage() {\r\n  const [stats, setStats] = useState<DashboardStats>({\r\n    totalCollections: 0,\r\n    totalProducts: 0,\r\n    totalOrders: 0,\r\n    totalUsers: 0,\r\n    totalContactSubmissions: 0,\r\n    lowStockProducts: 0,\r\n    pendingOrders: 0,\r\n  });\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const fetchDashboardStats = async () => {\r\n      try {\r\n        const [collections, products, orders, users, contactSubmissions] = await Promise.all([\r\n          collectionService.get.getAll(),\r\n          productService.get.getAll(),\r\n          orderService.get.getAll(),\r\n          userService.get.getAll(),\r\n          (await contactFormService.get()).getAll(),\r\n        ]);\r\n\r\n        // Calculate low stock products (stock < 10)\r\n        const lowStockProducts = products.filter(product => product.stock < 10).length;\r\n\r\n        // Calculate pending orders (assuming statusId 1 is pending)\r\n        const pendingOrders = orders.filter(order => order.statusId === 1).length;\r\n\r\n        setStats({\r\n          totalCollections: collections.length,\r\n          totalProducts: products.length,\r\n          totalOrders: orders.length,\r\n          totalUsers: users.length,\r\n          totalContactSubmissions: contactSubmissions.length,\r\n          lowStockProducts,\r\n          pendingOrders,\r\n        });\r\n      } catch (error) {\r\n        console.error('Error fetching dashboard stats:', error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDashboardStats();\r\n  }, []);\r\n\r\n  const statCards = [\r\n    {\r\n      title: 'Total Collections',\r\n      value: stats.totalCollections,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-blue-50',\r\n      href: '/admin/dashboard/collections',\r\n    },\r\n    {\r\n      title: 'Total Products',\r\n      value: stats.totalProducts,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-green-50',\r\n      href: '/admin/dashboard/products',\r\n    },\r\n    {\r\n      title: 'Total Orders',\r\n      value: stats.totalOrders,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-purple-50',\r\n      href: '/admin/dashboard/orders',\r\n    },\r\n    {\r\n      title: 'Total Users',\r\n      value: stats.totalUsers,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-black\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-white',\r\n    },\r\n    {\r\n      title: 'Low Stock Products',\r\n      value: stats.lowStockProducts,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-red-50',\r\n      href: '/admin/dashboard/products?filter=lowStock',\r\n    },\r\n    {\r\n      title: 'Pending Orders',\r\n      value: stats.pendingOrders,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-orange-50',\r\n      href: '/admin/dashboard/orders?filter=pending',\r\n    },\r\n    {\r\n      title: 'Contact Submissions',\r\n      value: stats.totalContactSubmissions,\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\r\n        </svg>\r\n      ),\r\n      bgColor: 'bg-purple-50',\r\n      href: '/admin/dashboard/contact-submissions',\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <ProtectedRoute>\r\n      <AdminLayout>\r\n        <div className=\"space-y-6\">\r\n          {/* Welcome Section */}\r\n          <div className=\"bg-white rounded-lg shadow-sm border border-black p-8\">\r\n            <h1 className=\"text-3xl font-bold text-black mb-3\">Welcome to Cast Stone Admin</h1>\r\n            <p className=\"text-black text-lg\">\r\n              Manage your collections, products, and orders from this comprehensive dashboard.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Stats Grid */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {statCards.map((card, index) => (\r\n              <div\r\n                key={index}\r\n                className={`${card.bgColor} rounded-lg border border-black p-6 hover:shadow-lg transition-all duration-200 ${card.href ? 'cursor-pointer hover:scale-105' : ''}`}\r\n                onClick={() => card.href && (window.location.href = card.href)}\r\n              >\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <p className=\"text-sm font-semibold text-black\">{card.title}</p>\r\n                    <p className=\"text-3xl font-bold text-black mt-2\">\r\n                      {isLoading ? (\r\n                        <div className=\"animate-pulse bg-black h-8 w-16 rounded\"></div>\r\n                      ) : (\r\n                        card.value\r\n                      )}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex-shrink-0\">\r\n                    {card.icon}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          {/* Quick Actions */}\r\n          <div className=\"bg-white rounded-lg shadow-sm border border-black p-6\">\r\n            <h2 className=\"text-xl font-bold text-black mb-6\">Quick Actions</h2>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n              <a\r\n                href=\"/admin/dashboard/collections\"\r\n                className=\"flex items-center p-6 bg-white rounded-lg hover:bg-blue transition-all duration-200 border border-black hover:shadow-md\"\r\n              >\r\n                <svg className=\"w-8 h-8 text-black mr-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n                </svg>\r\n                <span className=\"font-semibold text-black\">Add Collection</span>\r\n              </a>\r\n\r\n              <a\r\n                href=\"/admin/dashboard/products\"\r\n                className=\"flex items-center p-6 bg-white rounded-lg hover:bg-blue transition-all duration-200 border border-black hover:shadow-md\"\r\n              >\r\n                <svg className=\"w-8 h-8 text-black mr-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\r\n                </svg>\r\n                <span className=\"font-semibold text-black\">Add Product</span>\r\n              </a>\r\n\r\n              <a\r\n                href=\"/admin/dashboard/orders\"\r\n                className=\"flex items-center p-6 bg-white rounded-lg hover:bg-gray transition-all duration-200 border border-black hover:shadow-md\"\r\n              >\r\n                <svg className=\"w-8 h-8 text-black mr-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\r\n                </svg>\r\n                <span className=\"font-semibold text-black\">Manage Orders</span>\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </AdminLayout>\r\n    </ProtectedRoute>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,kBAAkB;QAClB,eAAe;QACf,aAAa;QACb,YAAY;QACZ,yBAAyB;QACzB,kBAAkB;QAClB,eAAe;IACjB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;oEAAsB;oBAC1B,IAAI;wBACF,MAAM,CAAC,aAAa,UAAU,QAAQ,OAAO,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACnF,iKAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,MAAM;4BAC5B,8JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;4BACzB,4JAAA,CAAA,eAAY,CAAC,GAAG,CAAC,MAAM;4BACvB,2JAAA,CAAA,cAAW,CAAC,GAAG,CAAC,MAAM;4BACtB,CAAC,MAAM,iKAAA,CAAA,qBAAkB,CAAC,GAAG,EAAE,EAAE,MAAM;yBACxC;wBAED,4CAA4C;wBAC5C,MAAM,mBAAmB,SAAS,MAAM;gFAAC,CAAA,UAAW,QAAQ,KAAK,GAAG;+EAAI,MAAM;wBAE9E,4DAA4D;wBAC5D,MAAM,gBAAgB,OAAO,MAAM;gFAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;+EAAG,MAAM;wBAEzE,SAAS;4BACP,kBAAkB,YAAY,MAAM;4BACpC,eAAe,SAAS,MAAM;4BAC9B,aAAa,OAAO,MAAM;4BAC1B,YAAY,MAAM,MAAM;4BACxB,yBAAyB,mBAAmB,MAAM;4BAClD;4BACA;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;uCAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,gBAAgB;YAC7B,oBACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,oBACE,6LAAC;gBAAI,WAAU;gBAAyB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAChF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW;YACxB,oBACE,6LAAC;gBAAI,WAAU;gBAA0B,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,oBACE,6LAAC;gBAAI,WAAU;gBAAqB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC5E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO,MAAM,gBAAgB;YAC7B,oBACE,6LAAC;gBAAI,WAAU;gBAAuB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC9E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,oBACE,6LAAC;gBAAI,WAAU;gBAA0B,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO,MAAM,uBAAuB;YACpC,oBACE,6LAAC;gBAAI,WAAU;gBAA0B,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BACjF,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,SAAS;YACT,MAAM;QACR;KACD;IAED,qBACE,6LAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC,6IAAA,CAAA,UAAW;sBACV,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gCAEC,WAAW,GAAG,KAAK,OAAO,CAAC,gFAAgF,EAAE,KAAK,IAAI,GAAG,mCAAmC,IAAI;gCAChK,SAAS,IAAM,KAAK,IAAI,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,IAAI;0CAE7D,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC,KAAK,KAAK;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DACV,0BACC,6LAAC;wDAAI,WAAU;;;;;+DAEf,KAAK,KAAK;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;;;;;;;+BAhBT;;;;;;;;;;kCAwBX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;kDAG7C,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;kDAG7C,6LAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GA7MwB;KAAA", "debugId": null}}]}