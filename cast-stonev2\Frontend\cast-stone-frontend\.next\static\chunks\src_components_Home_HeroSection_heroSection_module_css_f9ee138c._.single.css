/* [project]/src/components/Home/HeroSection/heroSection.module.css [app-client] (css) */
.heroSection-module__CcYIFG__hero {
  background-color: #1a1a1a;
  justify-content: center;
  align-items: center;
  height: 100vh;
  min-height: 700px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.heroSection-module__CcYIFG__imageContainer {
  z-index: 1;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.heroSection-module__CcYIFG__imageSlide {
  opacity: 0;
  will-change: transform, opacity;
  width: 100%;
  height: 100%;
  transition: opacity 1.5s ease-in-out;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1);
}

.heroSection-module__CcYIFG__imageSlide.heroSection-module__CcYIFG__active {
  opacity: 1;
  animation: 5s ease-out forwards heroSection-module__CcYIFG__smoothZoom;
}

.heroSection-module__CcYIFG__imageSlide:not(.heroSection-module__CcYIFG__active) {
  animation: none;
  transform: scale(1.06);
}

@keyframes heroSection-module__CcYIFG__smoothZoom {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.06);
  }
}

.heroSection-module__CcYIFG__backgroundImage {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
}

.heroSection-module__CcYIFG__imageOverlay {
  z-index: 2;
  background: linear-gradient(135deg, #0000004d 0%, #0003 50%, #0006 100%);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.heroSection-module__CcYIFG__container {
  z-index: 3;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.heroSection-module__CcYIFG__content {
  max-width: 900px;
  margin: 0 auto;
  padding-top: 2rem;
}

.heroSection-module__CcYIFG__title {
  color: #fff;
  text-shadow: 2px 2px 8px #0000004d;
  letter-spacing: .02em;
  margin-bottom: 2rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.1;
}

.heroSection-module__CcYIFG__titleLine1, .heroSection-module__CcYIFG__titleLine2 {
  animation: 1s ease-out forwards heroSection-module__CcYIFG__fadeInUp;
  display: block;
}

.heroSection-module__CcYIFG__titleLine1 {
  opacity: 0;
  animation-delay: .3s;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__titleLine2 {
  opacity: 0;
  animation-delay: .6s;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__subtitle {
  color: #ffffffe6;
  text-shadow: 1px 1px 3px #0006;
  opacity: 0;
  max-width: 700px;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-right: auto;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 1.3rem;
  font-weight: 300;
  line-height: 1.7;
  animation: 1s ease-out .9s forwards heroSection-module__CcYIFG__fadeInUp;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__actions {
  opacity: 0;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  animation: 1s ease-out 1.2s forwards heroSection-module__CcYIFG__fadeInUp;
  display: flex;
  transform: translateY(30px);
}

.heroSection-module__CcYIFG__primaryButton, .heroSection-module__CcYIFG__secondaryButton {
  letter-spacing: .15em;
  text-transform: uppercase;
  cursor: pointer;
  border: 2px solid #0000;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  min-width: 220px;
  padding: 1.2rem 3rem;
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: .9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all .4s cubic-bezier(.4, 0, .2, 1);
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

.heroSection-module__CcYIFG__primaryButton {
  color: #000c;
  backdrop-filter: blur(10px);
  background: #ffffffe6;
  border-color: #ffffffe6;
}

.heroSection-module__CcYIFG__primaryButton:hover {
  color: #000;
  background: #fff;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px #0003;
}

.heroSection-module__CcYIFG__secondaryButton {
  color: #fff;
  backdrop-filter: blur(10px);
  background: none;
  border-color: #fffc;
}

.heroSection-module__CcYIFG__secondaryButton:hover {
  background: #ffffff1a;
  border-color: #fff;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px #ffffff1a;
}

.heroSection-module__CcYIFG__buttonText {
  z-index: 2;
  position: relative;
}

.heroSection-module__CcYIFG__buttonRipple {
  z-index: 1;
  background: radial-gradient(circle, #ffffff4d 0%, #0000 70%);
  width: 100%;
  height: 100%;
  transition: transform .6s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0);
}

.heroSection-module__CcYIFG__primaryButton:active .heroSection-module__CcYIFG__buttonRipple, .heroSection-module__CcYIFG__secondaryButton:active .heroSection-module__CcYIFG__buttonRipple {
  transform: scale(1);
}

.heroSection-module__CcYIFG__scrollIndicator {
  z-index: 3;
  opacity: 0;
  animation: 1s ease-out 1.5s forwards heroSection-module__CcYIFG__fadeInUp;
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.heroSection-module__CcYIFG__scrollArrow {
  color: #fff;
  cursor: pointer;
  transition: color .3s;
  animation: 2s infinite heroSection-module__CcYIFG__bounce;
}

.heroSection-module__CcYIFG__scrollArrow:hover {
  color: #d4af8c;
}

@keyframes heroSection-module__CcYIFG__fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes heroSection-module__CcYIFG__bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

@media (width <= 1024px) {
  .heroSection-module__CcYIFG__title {
    font-size: 3.5rem;
  }

  .heroSection-module__CcYIFG__subtitle {
    font-size: 1.1rem;
  }

  .heroSection-module__CcYIFG__container {
    padding: 0 1.5rem;
  }
}

@media (width <= 768px) {
  .heroSection-module__CcYIFG__title {
    font-size: 2.5rem;
  }

  .heroSection-module__CcYIFG__subtitle {
    margin-bottom: 2rem;
    font-size: 1rem;
  }

  .heroSection-module__CcYIFG__actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .heroSection-module__CcYIFG__primaryButton, .heroSection-module__CcYIFG__secondaryButton {
    width: 100%;
    max-width: 280px;
    padding: .875rem 2rem;
  }

  .heroSection-module__CcYIFG__container {
    padding: 0 1rem;
  }
}

@media (width <= 480px) {
  .heroSection-module__CcYIFG__hero {
    min-height: 500px;
  }

  .heroSection-module__CcYIFG__title {
    margin-bottom: 1rem;
    font-size: 2rem;
  }

  .heroSection-module__CcYIFG__subtitle {
    margin-bottom: 1.5rem;
    font-size: .9rem;
  }

  .heroSection-module__CcYIFG__primaryButton, .heroSection-module__CcYIFG__secondaryButton {
    padding: .75rem 1.5rem;
    font-size: .8rem;
  }
}

.heroSection-module__CcYIFG__navArrow {
  color: #fffc;
  cursor: pointer;
  z-index: 4;
  opacity: .7;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  transition: all .3s;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.heroSection-module__CcYIFG__navArrow:hover {
  color: #fff;
  opacity: 1;
  background: #fff3;
  border-color: #fff9;
  transform: translateY(-50%)scale(1.1);
}

.heroSection-module__CcYIFG__navArrowLeft {
  left: 2rem;
}

.heroSection-module__CcYIFG__navArrowRight {
  right: 2rem;
}

.heroSection-module__CcYIFG__indicators {
  justify-content: center;
  gap: .75rem;
  margin-top: 3rem;
  padding: 1rem 0;
  display: flex;
}

.heroSection-module__CcYIFG__indicator {
  cursor: pointer;
  background: none;
  border: 2px solid #ffffff80;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
  position: relative;
}

.heroSection-module__CcYIFG__indicator:hover {
  border-color: #fffc;
  transform: scale(1.2);
}

.heroSection-module__CcYIFG__indicatorActive {
  background: #ffffffe6;
  border-color: #ffffffe6;
  box-shadow: 0 0 10px #ffffff80;
}

@media (width <= 768px) {
  .heroSection-module__CcYIFG__hero {
    min-height: 500px;
  }

  .heroSection-module__CcYIFG__navArrow {
    opacity: .6;
    width: 50px;
    height: 50px;
  }

  .heroSection-module__CcYIFG__navArrowLeft {
    left: 1rem;
  }

  .heroSection-module__CcYIFG__navArrowRight {
    right: 1rem;
  }

  .heroSection-module__CcYIFG__indicators {
    gap: .5rem;
    margin-top: 2rem;
  }

  .heroSection-module__CcYIFG__indicator {
    width: 10px;
    height: 10px;
  }
}

/*# sourceMappingURL=src_components_Home_HeroSection_heroSection_module_css_f9ee138c._.single.css.map*/