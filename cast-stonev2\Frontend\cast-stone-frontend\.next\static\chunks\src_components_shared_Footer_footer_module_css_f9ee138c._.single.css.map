{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/shared/Footer/footer.module.css"], "sourcesContent": ["/* Footer Styles */\r\n.footer {\r\n  background: #1e40af;\r\n  color: #ffffff;\r\n  padding: 4rem 0 2rem;\r\n  position: relative;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Content Grid */\r\n.content {\r\n  display: grid;\r\n  grid-template-columns: 2fr 1fr 1fr 1fr;\r\n  gap: 3rem;\r\n  margin-bottom: 3rem;\r\n  padding-bottom: 3rem;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n/* Brand Section */\r\n.brand {\r\n  max-width: 400px;\r\n}\r\n\r\n.brandName {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: #white;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.brandDescription {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1rem;\r\n  line-height: 1.6;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 2rem;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Social Links */\r\n.socialLinks {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n.socialLink {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 50%;\r\n  color: #white;\r\n  text-decoration: none;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.socialLink:hover {\r\n  background: #white;\r\n  color: #4a3728;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Link Groups */\r\n.linkGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.linkGroupTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #white;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.01em;\r\n}\r\n\r\n.linkList {\r\n  list-style: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.link {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  font-weight: 400;\r\n  line-height: 1.4;\r\n}\r\n\r\n.link:hover {\r\n  color: #white;\r\n  transform: translateX(4px);\r\n}\r\n\r\n/* Contact Info */\r\n.contactInfo {\r\n  grid-column: 1 / -1;\r\n  margin-top: 2rem;\r\n  padding-top: 2rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.contactTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: #white;\r\n  margin-bottom: 1.5rem;\r\n  letter-spacing: -0.01em;\r\n}\r\n\r\n.contactDetails {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 1.5rem;\r\n}\r\n\r\n.contactItem {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.contactLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  color: #white;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n}\r\n\r\n.contactValue {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  line-height: 1.4;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Bottom Section */\r\n.bottom {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: 2rem;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.copyright {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-weight: 400;\r\n}\r\n\r\n.legalLinks {\r\n  display: flex;\r\n  gap: 2rem;\r\n}\r\n\r\n.legalLink {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  text-decoration: none;\r\n  transition: color 0.3s ease;\r\n  font-weight: 400;\r\n}\r\n\r\n.legalLink:hover {\r\n  color: #white;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .footer {\r\n    padding: 3rem 0 1.5rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n\r\n  .content {\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 2.5rem;\r\n  }\r\n\r\n  .brand {\r\n    grid-column: 1 / -1;\r\n    max-width: none;\r\n    text-align: center;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .socialLinks {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .footer {\r\n    padding: 2.5rem 0 1rem;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n\r\n  .content {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .brand {\r\n    margin-bottom: 1.5rem;\r\n  }\r\n\r\n  .brandName {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .brandDescription {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .linkGroup {\r\n    align-items: center;\r\n  }\r\n\r\n  .linkGroupTitle {\r\n    font-size: 1.125rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .contactInfo {\r\n    margin-top: 1.5rem;\r\n    padding-top: 1.5rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .contactDetails {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .bottom {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .legalLinks {\r\n    gap: 1rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .footer {\r\n    padding: 2rem 0 1rem;\r\n  }\r\n\r\n  .brandName {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .brandDescription {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .socialLinks {\r\n    gap: 0.75rem;\r\n  }\r\n\r\n  .socialLink {\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n\r\n  .linkGroupTitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .link {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .contactTitle {\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .contactLabel {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .contactValue {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .legalLinks {\r\n    flex-direction: column;\r\n    gap: 0.5rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;;EAOA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}