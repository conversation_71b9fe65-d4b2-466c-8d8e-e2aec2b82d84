exports.id=913,exports.ids=[913],exports.modules={7419:(e,t,r)=>{"use strict";r.d(t,{orderGetService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async getAll(){return this.logApiCall("GET",a.FB.Orders.Base),this.handleResponse(this.client.get(a.FB.Orders.Base))}async getById(e){return this.logApiCall("GET",a.FB.Orders.ById(e)),this.handleResponse(this.client.get(a.FB.Orders.ById(e)))}async getDetails(e){return this.logApiCall("GET",a.FB.Orders.Details(e)),this.handleResponse(this.client.get(a.FB.Orders.Details(e)))}async getByUser(e){return this.logApiCall("GET",a.FB.Orders.ByUser(e)),this.handleResponse(this.client.get(a.FB.Orders.ByUser(e)))}async getByEmail(e){return this.logApiCall("GET",a.FB.Orders.ByEmail(e)),this.handleResponse(this.client.get(a.FB.Orders.ByEmail(e)))}async getByStatus(e){return this.logApiCall("GET",a.FB.Orders.ByStatus(e)),this.handleResponse(this.client.get(a.FB.Orders.ByStatus(e)))}async getPending(){return this.logApiCall("GET",a.FB.Orders.Pending),this.handleResponse(this.client.get(a.FB.Orders.Pending))}async getRecent(e=10){return this.logApiCall("GET",a.FB.Orders.Recent,{count:e}),this.handleResponse(this.client.get(a.FB.Orders.Recent,{count:e}))}async getTotalRevenue(){return this.logApiCall("GET",a.FB.Orders.Revenue.Total),this.handleResponse(this.client.get(a.FB.Orders.Revenue.Total))}async getRevenueByDateRange(e,t){let r={startDate:s.F.formatDate(e),endDate:s.F.formatDate(t)};return this.logApiCall("GET",a.FB.Orders.Revenue.Range,r),this.handleResponse(this.client.get(a.FB.Orders.Revenue.Range,r))}async getFiltered(e){let t=s.F.cleanObject(e);return this.logApiCall("GET",a.FB.Orders.Filter,t),this.handlePaginatedResponse(this.client.get(a.FB.Orders.Filter,t))}async getPaginated(e=1,t=10,r="createdAt",s="desc"){return this.getFiltered({pageNumber:e,pageSize:t,sortBy:r,sortDirection:s})}async getByAmountRange(e,t){return(await this.getFiltered({minAmount:e,maxAmount:t,pageSize:100})).data}async getByPaymentMethod(e){return(await this.getFiltered({paymentMethod:e,pageSize:100})).data}async getByCountry(e){return(await this.getFiltered({country:e,pageSize:100})).data}async getByCity(e){return(await this.getFiltered({city:e,pageSize:100})).data}async getByDateRange(e,t){let r={createdAfter:s.F.formatDate(e),createdBefore:s.F.formatDate(t),pageSize:100};return(await this.getFiltered(r)).data}async getByStatuses(e){let t=e.map(e=>this.getByStatus(e));return(await Promise.all(t)).flat().filter((e,t,r)=>t===r.findIndex(t=>t.id===e.id))}async getUserOrderHistory(e,t=1,r=10){return this.getFiltered({userId:e,pageNumber:t,pageSize:r,sortBy:"createdAt",sortDirection:"desc"})}async getGuestOrderHistory(e,t=1,r=10){return this.getFiltered({email:e,pageNumber:t,pageSize:r,sortBy:"createdAt",sortDirection:"desc"})}async getStatistics(){try{let[e,t,r]=await Promise.all([this.getTotalRevenue(),this.getPending(),this.getAll()]),s=r.length,a=r.filter(e=>e.statusName.toLowerCase().includes("delivered")||e.statusName.toLowerCase().includes("completed")).length;return{totalOrders:s,totalRevenue:e,averageOrderValue:Math.round(100*(s>0?e/s:0))/100,pendingOrders:t.length,completedOrders:a}}catch(e){throw console.error("Error getting order statistics:",e),e}}}let o=new n},18207:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(60687),a=r(43210),n=r(85814),o=r.n(n),i=r(28253),l=r(35255),c=r(62614),d=r.n(c),u=r(16189);let h=({title:e="Cast Stone"})=>{let{getCartSummary:t}=(0,i._)(),[r,n]=(0,a.useState)([]),[c,h]=(0,a.useState)(null),[p,m]=(0,a.useState)(!1),[g,y]=(0,a.useState)(!1),f=(0,a.useRef)({}),C=(0,u.usePathname)();if(C.startsWith("/admin"))return null;(0,a.useEffect)(()=>{let e=()=>{y(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,a.useEffect)(()=>{(async()=>{try{m(!0);let e=await l.at.getHierarchy();n(e)}catch(e){console.error("Failed to fetch collections:",e)}finally{m(!1)}})()},[]);let w=e=>{h(c===e?null:e)};(0,a.useEffect)(()=>{let e=e=>{let t=e.target;Object.values(f.current).some(e=>e&&e.contains(t))||h(null)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let v=e=>e.map(e=>({label:e.name,href:`/collections/${e.id}`,children:e.children.length>0?v(e.children):void 0})),B=v(r),P="/"===C;return(0,s.jsx)("header",{className:`${d().header} ${g?d().scrolled:""} ${!P?d().nonHomePage:""}`,children:(0,s.jsxs)("div",{className:d().container,children:[(0,s.jsx)("div",{className:d().logo,children:(0,s.jsxs)(o(),{href:"/",className:d().logoLink,children:[(0,s.jsx)("span",{className:d().logoText,children:e}),(0,s.jsx)("span",{className:d().logoSubtext,children:"Interiors & Decorations"})]})}),(0,s.jsx)("nav",{className:d().nav,children:(0,s.jsxs)("ul",{className:d().navList,children:[(0,s.jsx)("li",{className:d().navItem,children:(0,s.jsxs)("div",{className:d().dropdownContainer,ref:e=>{f.current.company=e},children:[(0,s.jsxs)("button",{className:`${d().navButton} ${"company"===c?d().active:""}`,onClick:()=>w("company"),"aria-expanded":"company"===c,children:["Company",(0,s.jsx)("span",{className:`${d().dropdownIcon} ${"company"===c?d().rotated:""}`,children:(0,s.jsx)("svg",{width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",children:(0,s.jsx)("path",{d:"M1 1.5L6 6.5L11 1.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),"company"===c&&(0,s.jsx)("div",{className:d().dropdown,children:(0,s.jsx)("ul",{className:d().dropdownList,children:[{label:"Contact Us",href:"/contact"},{label:"Our Story",href:"/our-story"},{label:"Retail Locator",href:"/retail-locator"},{label:"Wholesale Signup",href:"/wholesale-signup"}].map((e,t)=>(0,s.jsx)("li",{className:d().dropdownItem,children:(0,s.jsx)(o(),{href:e.href,className:d().dropdownLink,children:e.label})},t))})})]})}),(0,s.jsx)("li",{className:d().navItem,children:(0,s.jsx)(o(),{href:"/products",className:d().navLink,children:"Products"})}),(0,s.jsx)("li",{className:d().navItem,children:(0,s.jsxs)("div",{className:d().dropdownContainer,ref:e=>{f.current.collections=e},children:[(0,s.jsxs)("button",{className:`${d().navButton} ${"collections"===c?d().active:""}`,onClick:()=>w("collections"),"aria-expanded":"collections"===c,disabled:p,children:["Collections",p?(0,s.jsx)("span",{className:d().loadingIcon,children:(0,s.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",children:(0,s.jsxs)("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeDasharray:"31.416",strokeDashoffset:"31.416",children:[(0,s.jsx)("animate",{attributeName:"stroke-dasharray",dur:"2s",values:"0 31.416;15.708 15.708;0 31.416",repeatCount:"indefinite"}),(0,s.jsx)("animate",{attributeName:"stroke-dashoffset",dur:"2s",values:"0;-15.708;-31.416",repeatCount:"indefinite"})]})})}):(0,s.jsx)("span",{className:`${d().dropdownIcon} ${"collections"===c?d().rotated:""}`,children:(0,s.jsx)("svg",{width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",children:(0,s.jsx)("path",{d:"M1 1.5L6 6.5L11 1.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),"collections"===c&&!p&&(0,s.jsx)("div",{className:d().dropdown,children:(0,s.jsx)("ul",{className:d().dropdownList,children:B.map((e,t)=>(0,s.jsxs)("li",{className:d().dropdownItem,children:[(0,s.jsx)(o(),{href:e.href,className:d().dropdownLink,children:e.label}),e.children&&e.children.length>0&&(0,s.jsx)("ul",{className:d().subDropdownList,children:e.children.map((e,t)=>(0,s.jsxs)("li",{className:d().subDropdownItem,children:[(0,s.jsx)(o(),{href:e.href,className:d().subDropdownLink,children:e.label}),e.children&&e.children.length>0&&(0,s.jsx)("ul",{className:d().subSubDropdownList,children:e.children.map((e,t)=>(0,s.jsx)("li",{className:d().subSubDropdownItem,children:(0,s.jsx)(o(),{href:e.href,className:d().subSubDropdownLink,children:e.label})},t))})]},t))})]},t))})})]})}),(0,s.jsx)("li",{className:d().navItem,children:(0,s.jsx)(o(),{href:"/completed-projects",className:d().navLink,children:"Completed Projects"})}),(0,s.jsx)("li",{className:d().navItem,children:(0,s.jsxs)("div",{className:d().dropdownContainer,ref:e=>{f.current.discover=e},children:[(0,s.jsxs)("button",{className:`${d().navButton} ${"discover"===c?d().active:""}`,onClick:()=>w("discover"),"aria-expanded":"discover"===c,children:["Discover",(0,s.jsx)("span",{className:`${d().dropdownIcon} ${"discover"===c?d().rotated:""}`,children:(0,s.jsx)("svg",{width:"12",height:"8",viewBox:"0 0 12 8",fill:"none",children:(0,s.jsx)("path",{d:"M1 1.5L6 6.5L11 1.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),"discover"===c&&(0,s.jsx)("div",{className:d().dropdown,children:(0,s.jsx)("ul",{className:d().dropdownList,children:[{label:"Catalog",href:"/catalog"},{label:"Finishes",href:"/finishes"},{label:"Videos",href:"/videos"},{label:"Technical Info",href:"/technical-info"},{label:"FAQ",href:"/faq"}].map((e,t)=>(0,s.jsx)("li",{className:d().dropdownItem,children:(0,s.jsx)(o(),{href:e.href,className:d().dropdownLink,children:e.label})},t))})})]})})]})}),(0,s.jsx)("div",{className:d().cartContainer,children:(0,s.jsx)(o(),{href:"/cart",className:d().cartLink,children:(0,s.jsxs)("div",{className:d().cartIconWrapper,children:[(0,s.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",children:(0,s.jsx)("path",{d:"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7"})}),t().totalItems>0&&(0,s.jsx)("span",{className:d().cartBadge,children:t().totalItems})]})})})]})})}},19867:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(60687);r(43210);var a=r(85814),n=r.n(a),o=r(80614),i=r.n(o),l=r(16189);let c=({companyName:e="Cast Stone"})=>{let t=new Date().getFullYear();return(0,l.usePathname)().startsWith("/admin")?null:(0,s.jsx)("footer",{className:i().footer,children:(0,s.jsxs)("div",{className:i().container,children:[(0,s.jsxs)("div",{className:i().content,children:[(0,s.jsxs)("div",{className:i().brand,children:[(0,s.jsx)("h3",{className:i().brandName,children:e}),(0,s.jsx)("p",{className:i().brandDescription,children:"Creating timeless beauty with handcrafted cast stone elements for over 25 years."}),(0,s.jsxs)("div",{className:i().socialLinks,children:[(0,s.jsx)("a",{href:"#",className:i().socialLink,"aria-label":"Facebook",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})}),(0,s.jsx)("a",{href:"#",className:i().socialLink,"aria-label":"Instagram",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),(0,s.jsx)("a",{href:"#",className:i().socialLink,"aria-label":"LinkedIn",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,s.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})})]})]}),(0,s.jsxs)("div",{className:i().linkGroup,children:[(0,s.jsx)("h4",{className:i().linkGroupTitle,children:"Company"}),(0,s.jsxs)("ul",{className:i().linkList,children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/contact",className:i().link,children:"Contact Us"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/our-story",className:i().link,children:"Our Story"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/retail-locator",className:i().link,children:"Retail Locator"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/wholesale-signup",className:i().link,children:"Wholesale Sign-up"})})]})]}),(0,s.jsxs)("div",{className:i().linkGroup,children:[(0,s.jsx)("h4",{className:i().linkGroupTitle,children:"Products"}),(0,s.jsxs)("ul",{className:i().linkList,children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/products/architectural",className:i().link,children:"Architectural Products"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/products/designer",className:i().link,children:"Designer Products"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/products/limited-edition",className:i().link,children:"Limited Edition"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/products/sealers",className:i().link,children:"Cast Stone Sealers"})})]})]}),(0,s.jsxs)("div",{className:i().linkGroup,children:[(0,s.jsx)("h4",{className:i().linkGroupTitle,children:"Discover"}),(0,s.jsxs)("ul",{className:i().linkList,children:[(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/catalog",className:i().link,children:"Catalog"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/collections",className:i().link,children:"Collections"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/completed-projects",className:i().link,children:"Completed Projects"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/videos",className:i().link,children:"Videos"})}),(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:"/faq",className:i().link,children:"FAQs"})})]})]})]}),(0,s.jsxs)("div",{className:i().contactInfo,children:[(0,s.jsx)("h4",{className:i().contactTitle,children:"Contact Info"}),(0,s.jsxs)("div",{className:i().contactDetails,children:[(0,s.jsxs)("div",{className:i().contactItem,children:[(0,s.jsx)("span",{className:i().contactLabel,children:"Address:"}),(0,s.jsxs)("span",{className:i().contactValue,children:["123 Artisan Way",(0,s.jsx)("br",{}),"Craftsman City, CC 12345"]})]}),(0,s.jsxs)("div",{className:i().contactItem,children:[(0,s.jsx)("span",{className:i().contactLabel,children:"Phone:"}),(0,s.jsx)("span",{className:i().contactValue,children:"(*************"})]}),(0,s.jsxs)("div",{className:i().contactItem,children:[(0,s.jsx)("span",{className:i().contactLabel,children:"Email:"}),(0,s.jsx)("span",{className:i().contactValue,children:"<EMAIL>"})]})]})]}),(0,s.jsxs)("div",{className:i().bottom,children:[(0,s.jsxs)("p",{className:i().copyright,children:["\xa9 ",t," ",e,". All rights reserved."]}),(0,s.jsxs)("div",{className:i().legalLinks,children:[(0,s.jsx)(n(),{href:"/privacy",className:i().legalLink,children:"Privacy Policy"}),(0,s.jsx)(n(),{href:"/terms",className:i().legalLink,children:"Terms of Service"})]})]})]})})}},21152:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ContactFormGetService:()=>a,contactFormGetService:()=>n});var s=r(22805);class a extends s.d{async getAll(){return this.logApiCall("GET","/contactform"),this.handleResponse(this.client.get("/contactform"))}async getById(e){if(this.logApiCall("GET",`/contactform/${e}`),!e||e<=0)throw Error("Valid submission ID is required");return this.handleResponse(this.client.get(`/contactform/${e}`))}async getRecent(e=10){if(this.logApiCall("GET",`/contactform/recent?count=${e}`),e<=0||e>100)throw Error("Count must be between 1 and 100");return this.handleResponse(this.client.get(`/contactform/recent?count=${e}`))}async getByInquiryType(e){return this.logApiCall("GET",`/contactform/inquiry/${e}`),this.handleResponse(this.client.get(`/contactform/inquiry/${e}`))}async getByDateRange(e,t){let r=e.toISOString(),s=t.toISOString();if(this.logApiCall("GET",`/contactform/date-range?startDate=${r}&endDate=${s}`),e>t)throw Error("Start date must be before end date");return this.handleResponse(this.client.get(`/contactform/date-range?startDate=${r}&endDate=${s}`))}async getPaginated(e=1,t=20){if(this.logApiCall("GET",`/contactform?page=${e}&pageSize=${t}`),e<=0)throw Error("Page must be greater than 0");if(t<=0||t>100)throw Error("Page size must be between 1 and 100");let r=await this.getAll(),s=(e-1)*t;return{submissions:r.slice(s,s+t),totalCount:r.length,currentPage:e,totalPages:Math.ceil(r.length/t)}}async search(e){if(this.logApiCall("GET",`/contactform/search?q=${encodeURIComponent(e)}`),!e||0===e.trim().length)throw Error("Search query is required");let t=await this.getAll(),r=e.toLowerCase();return t.filter(e=>e.name.toLowerCase().includes(r)||e.email.toLowerCase().includes(r)||e.company?.toLowerCase().includes(r)||e.message.toLowerCase().includes(r)||e.inquiryDisplayName.toLowerCase().includes(r))}}let n=new a},22711:(e,t,r)=>{"use strict";r.d(t,{WholesaleAuthProvider:()=>i,u:()=>l});var s=r(60687),a=r(43210),n=r(63968);let o=(0,a.createContext)(void 0);function i({children:e}){let[t,r]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),[c,d]=(0,a.useState)(!1),u=async(e,t)=>{l(!0);try{let s=await n.y1.login({email:e,password:t});if(s.success&&s.data){let e=s.data;if(e.isValid&&e.user)return r(e.user),d(e.isApprovedWholesaleBuyer),localStorage.setItem("wholesale_session",JSON.stringify(e.user)),{success:!0};return{success:!1,error:e.errorMessage||"Invalid credentials"}}return{success:!1,error:s.message||"Login failed"}}catch(e){return console.error("Login error:",e),{success:!1,error:e instanceof Error?e.message:"An unexpected error occurred"}}finally{l(!1)}},h=async e=>{try{let t=await n.y1.checkWholesaleStatus(e);return t.success&&!0===t.data}catch(e){return console.error("Error checking wholesale status:",e),!1}},p=async()=>{if(t?.email)try{let e=await n.y1.getUserByEmail(t.email);if(e.success&&e.data){r(e.data),localStorage.setItem("wholesale_session",JSON.stringify(e.data));let s=await h(t.email);d(s)}}catch(e){console.error("Error refreshing user data:",e)}};return(0,s.jsx)(o.Provider,{value:{user:t,isLoading:i,isApprovedWholesaleBuyer:c,login:u,logout:()=>{r(null),d(!1),localStorage.removeItem("wholesale_session")},isAuthenticated:!!t,checkWholesaleStatus:h,refreshUserData:p},children:e})}function l(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useWholesaleAuth must be used within a WholesaleAuthProvider");return e}},22805:(e,t,r)=>{"use strict";r.d(t,{F:()=>n,d:()=>a});var s=r(51162);class a{async handleResponse(e){try{let t=await e;if(t.success&&void 0!==t.data)return t.data;throw Error(t.message||"API call failed")}catch(e){throw console.error("API Error:",e),e}}async handlePaginatedResponse(e){try{let t=await e;if(t.success&&void 0!==t.data)return t.data;throw Error(t.message||"API call failed")}catch(e){throw console.error("API Error:",e),e}}async handleVoidResponse(e){try{return(await e).success}catch(e){throw console.error("API Error:",e),e}}logApiCall(e,t,r){}constructor(){this.client=s.o}}class n{static formatDate(e){return e.toISOString()}static parseDate(e){return new Date(e)}static isValidEmail(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}static formatCurrency(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e)}static debounce(e,t){let r;return(...s)=>{clearTimeout(r),r=setTimeout(()=>e(...s),t)}}static cleanObject(e){let t={};return Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&(t[e]=r)}),t}}},26484:(e,t,r)=>{Promise.resolve().then(r.bind(r,70569)),Promise.resolve().then(r.bind(r,78501)),Promise.resolve().then(r.bind(r,37043)),Promise.resolve().then(r.bind(r,68653))},28253:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>c,_:()=>d});var s=r(60687),a=r(43210),n=r(63968);let o={cart:null,isLoading:!1,error:null,sessionId:""};function i(e,t){switch(t.type){case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_ERROR":return{...e,error:t.payload,isLoading:!1};case"SET_CART":return{...e,cart:t.payload,isLoading:!1,error:null};case"SET_SESSION_ID":return{...e,sessionId:t.payload};case"ADD_ITEM":if(!e.cart)return e;let r=e.cart.cartItems.findIndex(e=>e.productId===t.payload.productId);if(!(r>=0))return{...e,cart:{...e.cart,cartItems:[...e.cart.cartItems,t.payload]}};{let s=[...e.cart.cartItems];return s[r]={...s[r],quantity:s[r].quantity+t.payload.quantity},{...e,cart:{...e.cart,cartItems:s}}}case"UPDATE_ITEM":if(!e.cart)return e;let s=e.cart.cartItems.map(e=>e.productId===t.payload.productId?{...e,quantity:t.payload.quantity}:e);return{...e,cart:{...e.cart,cartItems:s}};case"REMOVE_ITEM":if(!e.cart)return e;let a=e.cart.cartItems.filter(e=>e.productId!==t.payload);return{...e,cart:{...e.cart,cartItems:a}};case"CLEAR_CART":return{...e,cart:e.cart?{...e.cart,cartItems:[],totalItems:0,totalAmount:0}:null};default:return e}}let l=(0,a.createContext)(void 0);function c({children:e}){let[t,r]=(0,a.useReducer)(i,o),c=async e=>{try{r({type:"SET_LOADING",payload:!0});let s=null;e?s=await n.CV.get.getByUserId(e):t.sessionId&&t.sessionId.length>0&&(s=await n.CV.get.getOrCreate(void 0,t.sessionId)),r({type:"SET_CART",payload:s})}catch(e){console.error("Error loading cart:",e),r({type:"SET_ERROR",payload:"Failed to load cart"})}finally{r({type:"SET_LOADING",payload:!1})}},d=async(e,s,a)=>{try{if(r({type:"SET_LOADING",payload:!0}),!a&&(!t.sessionId||0===t.sessionId.length))throw Error("No user ID or session ID available for cart operation");let o={productId:e,quantity:s,userId:a,sessionId:a?void 0:t.sessionId},i=await n.CV.post.addToCart(o);r({type:"SET_CART",payload:i})}catch(e){console.error("Error adding to cart:",e),r({type:"SET_ERROR",payload:e.message||"Failed to add item to cart"})}finally{r({type:"SET_LOADING",payload:!1})}},u=async(e,s)=>{if(t.cart)try{r({type:"SET_LOADING",payload:!0});let a=await n.CV.update.updateCartItem(t.cart.id,e,{quantity:s});r({type:"SET_CART",payload:a})}catch(e){console.error("Error updating cart item:",e),r({type:"SET_ERROR",payload:e.message||"Failed to update cart item"})}},h=async e=>{if(t.cart)try{r({type:"SET_LOADING",payload:!0}),await n.CV.delete.removeFromCart(t.cart.id,e),r({type:"REMOVE_ITEM",payload:e}),r({type:"SET_LOADING",payload:!1})}catch(e){console.error("Error removing from cart:",e),r({type:"SET_ERROR",payload:e.message||"Failed to remove item from cart"})}},p=async()=>{if(t.cart)try{r({type:"SET_LOADING",payload:!0}),await n.CV.delete.clearCart(t.cart.id),r({type:"CLEAR_CART"}),r({type:"SET_LOADING",payload:!1})}catch(e){console.error("Error clearing cart:",e),r({type:"SET_ERROR",payload:e.message||"Failed to clear cart"})}},m=async e=>{await c(e)};return(0,s.jsx)(l.Provider,{value:{state:t,addToCart:d,updateCartItem:u,removeFromCart:h,clearCart:p,getCartSummary:()=>t.cart?{totalItems:t.cart.totalItems,totalAmount:t.cart.totalAmount}:{totalItems:0,totalAmount:0},refreshCart:m},children:e})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},32829:(e,t,r)=>{"use strict";r.d(t,{userUpdateService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async update(e,t){return this.logApiCall("PUT",a.FB.Users.ById(e),t),this.validateUpdateRequest(t),this.handleResponse(this.client.put(a.FB.Users.ById(e),t))}async activate(e){return this.logApiCall("PATCH",a.FB.Users.Activate(e)),this.handleVoidResponse(this.client.patch(a.FB.Users.Activate(e)))}async deactivate(e){return this.logApiCall("PATCH",a.FB.Users.Deactivate(e)),this.handleVoidResponse(this.client.patch(a.FB.Users.Deactivate(e)))}async updateProfile(e,t,r,s){let a=await this.getCurrentUser(e),n={role:a.role,name:t??a.name,phoneNumber:r??a.phoneNumber,country:s?.country??a.country,city:s?.city??a.city,zipCode:s?.zipCode??a.zipCode,active:a.active};return this.update(e,n)}async updateContact(e,t,r){let s=await this.getCurrentUser(e),a={role:s.role,name:s.name,phoneNumber:t??s.phoneNumber,country:r?.country??s.country,city:r?.city??s.city,zipCode:r?.zipCode??s.zipCode,active:s.active};return this.update(e,a)}async updateRole(e,t){if(!["admin","customer","guest"].includes(t))throw Error("Invalid role. Must be admin, customer, or guest");let r=await this.getCurrentUser(e),s={role:t,name:r.name,phoneNumber:r.phoneNumber,country:r.country,city:r.city,zipCode:r.zipCode,active:r.active};return this.update(e,s)}async updateAddress(e,t,r,s){let a=await this.getCurrentUser(e),n={role:a.role,name:a.name,phoneNumber:a.phoneNumber,country:t??a.country,city:r??a.city,zipCode:s??a.zipCode,active:a.active};return this.update(e,n)}async toggleActiveStatus(e){return(await this.getCurrentUser(e)).active?await this.deactivate(e):await this.activate(e),this.getCurrentUser(e)}async bulkUpdateRole(e,t){this.logApiCall("PUT","Bulk Update User Roles",{userIds:e,newRole:t,count:e.length});let r=await Promise.allSettled(e.map(e=>this.updateRole(e,t))),s=r.filter(e=>"fulfilled"===e.status).length,a=r.filter(e=>"rejected"===e.status).map((t,r)=>`User ${e[r]}: ${"rejected"===t.status?t.reason:"Unknown error"}`);return{success:s===e.length,updatedCount:s,errors:a}}async bulkActivate(e){this.logApiCall("PATCH","Bulk Activate Users",{userIds:e,count:e.length});let t=await Promise.allSettled(e.map(e=>this.activate(e))),r=t.filter(e=>"fulfilled"===e.status&&!0===e.value).length,s=t.filter(e=>"rejected"===e.status).map((t,r)=>`User ${e[r]}: ${"rejected"===t.status?t.reason:"Unknown error"}`);return{success:r===e.length,activatedCount:r,errors:s}}async bulkDeactivate(e){this.logApiCall("PATCH","Bulk Deactivate Users",{userIds:e,count:e.length});let t=await Promise.allSettled(e.map(e=>this.deactivate(e))),r=t.filter(e=>"fulfilled"===e.status&&!0===e.value).length,s=t.filter(e=>"rejected"===e.status).map((t,r)=>`User ${e[r]}: ${"rejected"===t.status?t.reason:"Unknown error"}`);return{success:r===e.length,deactivatedCount:r,errors:s}}async promoteToAdmin(e){return this.updateRole(e,"admin")}async demoteToCustomer(e){return this.updateRole(e,"customer")}async updatePreferences(e,t){return console.log("User preferences update not implemented:",t),this.getCurrentUser(e)}async getCurrentUser(e){let t=await this.client.get(a.FB.Users.ById(e));if(!t.success||!t.data)throw Error("User not found");return t.data}validateUpdateRequest(e){if(!e.role||!["admin","customer","guest"].includes(e.role))throw Error("Valid role is required (admin, customer, or guest)");if(e.phoneNumber&&!this.isValidPhoneNumber(e.phoneNumber))throw Error("Invalid phone number format");if(e.name&&e.name.length>100)throw Error("Name must be 100 characters or less");if(e.country&&e.country.length>100)throw Error("Country must be 100 characters or less");if(e.city&&e.city.length>100)throw Error("City must be 100 characters or less");if(e.zipCode&&e.zipCode.length>20)throw Error("Zip code must be 20 characters or less")}isValidPhoneNumber(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}}let o=new n},33207:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},35255:(e,t,r)=>{"use strict";r.d(t,{at:()=>s.collectionGetService,Yn:()=>h});var s=r(91791),a=r(22805),n=r(41459);class o extends a.d{async create(e){return this.logApiCall("POST",n.FB.Collections.Base,e),this.validateCreateRequest(e),this.handleResponse(this.client.post(n.FB.Collections.Base,e))}async createRootCollection(e,t,r=[],s=!1,a){return this.create({name:e,description:t,level:1,parentCollectionId:void 0,childCollectionIds:void 0,tags:r,published:s,createdBy:a,images:[]})}async createSubCollection(e,t,r,s,a=[],n=!1,o){return this.create({name:e,description:t,level:r,parentCollectionId:s,childCollectionIds:void 0,tags:a,published:n,createdBy:o,images:[]})}async createBatch(e){return this.logApiCall("POST","Batch Collections",{count:e.length}),Promise.all(e.map(e=>this.create(e)))}async refreshAllRelationships(){this.logApiCall("POST",n.FB.Collections.RefreshRelationships);try{let e=await this.client.post(n.FB.Collections.RefreshRelationships);if(e.success&&void 0!==e.data)return{success:!0,updatedCount:e.data,message:e.message||`Updated ${e.data} collection relationships`};return{success:!1,updatedCount:0,message:e.message||"Failed to refresh relationships"}}catch(e){return console.error("Error refreshing collection relationships:",e),{success:!1,updatedCount:0,message:e instanceof Error?e.message:"Unknown error occurred"}}}validateCreateRequest(e){if(!e.name||0===e.name.trim().length)throw Error("Collection name is required");if(e.name.length>200)throw Error("Collection name must be 200 characters or less");if(!e.level||e.level<1||e.level>3)throw Error("Collection level must be 1, 2, or 3");if(1===e.level&&e.parentCollectionId)throw Error("Root collections (level 1) cannot have a parent");if(e.level>1&&!e.parentCollectionId)throw Error("Sub-collections (level 2-3) must have a parent");if(!e.createdBy||0===e.createdBy.trim().length)throw Error("CreatedBy is required");if(e.createdBy.length>100)throw Error("CreatedBy must be 100 characters or less");if(e.description&&e.description.length>1e3)throw Error("Description must be 1000 characters or less")}}let i=new o;var l=r(89690);class c extends a.d{async delete(e){return this.logApiCall("DELETE",n.FB.Collections.ById(e)),this.handleVoidResponse(this.client.delete(n.FB.Collections.ById(e)))}async deleteBatch(e){return this.logApiCall("DELETE","Batch Collections",{count:e.length}),Promise.all(e.map(e=>this.delete(e)))}async unpublish(e,t){this.logApiCall("PATCH",`Unpublish Collection ${e}`);try{let{collectionUpdateService:s}=await Promise.resolve().then(r.bind(r,89690));return await s.updatePublishStatus(e,!1,t),!0}catch(e){return console.error("Failed to unpublish collection:",e),!1}}async canDelete(e){try{let t,{collectionGetService:s}=await Promise.resolve().then(r.bind(r,91791)),a=(await s.getChildren(e)).length>0,n=await s.getById(e),o=n.products&&n.products.length>0,i=!a&&!o;return!i&&(a&&o?t="Collection has both child collections and products":a?t="Collection has child collections":o&&(t="Collection has products")),{canDelete:i,reason:t,hasChildren:a,hasProducts:o}}catch(e){return console.error("Error checking if collection can be deleted:",e),{canDelete:!1,reason:"Error checking collection dependencies"}}}async safeDelete(e){try{let t=await this.canDelete(e);if(!t.canDelete)return{success:!1,message:t.reason||"Collection cannot be deleted"};if(await this.delete(e))return{success:!0,message:"Collection deleted successfully"};return{success:!1,message:"Failed to delete collection"}}catch(e){return console.error("Error during safe delete:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error occurred"}}}async forceDelete(e,t){try{let{collectionGetService:s}=await Promise.resolve().then(r.bind(r,91791)),a=[],n=0,o=await s.getById(e);for(let r of(t&&o.products.length>0&&(n=o.products.length,console.warn("Product moving not implemented - would move",n,"products")),await s.getChildren(e))){let e=await this.forceDelete(r.id,t);e.success&&e.deletedCollections&&a.push(...e.deletedCollections)}let i=await this.delete(e);return i&&a.push(e),{success:i,message:i?`Successfully deleted collection and ${a.length-1} child collections`:"Failed to delete collection",deletedCollections:a,movedProducts:n}}catch(e){return console.error("Error during force delete:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error occurred"}}}}let d=new c;class u{constructor(){this.get=s.collectionGetService,this.post=i,this.update=l.collectionUpdateService,this.delete=d}}let h=new u},37043:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\CartContext.tsx","CartProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\CartContext.tsx","useCart")},41459:(e,t,r)=>{"use strict";r.d(t,{Al:()=>l,FB:()=>n,Go:()=>i,hD:()=>o,lq:()=>s,ym:()=>a});let s="https://cast-stonev2.onrender.com/api";var a=function(e){return e.GET="GET",e.POST="POST",e.PUT="PUT",e.PATCH="PATCH",e.DELETE="DELETE",e}({});let n={Collections:{Base:"/collections",ById:e=>`/collections/${e}`,ByLevel:e=>`/collections/level/${e}`,Children:e=>`/collections/${e}/children`,Hierarchy:"/collections/hierarchy",Published:"/collections/published",Search:"/collections/search",Filter:"/collections/filter",RefreshRelationships:"/collections/refresh-relationships"},Products:{Base:"/products",ById:e=>`/products/${e}`,ByCollection:e=>`/products/collection/${e}`,InStock:"/products/in-stock",Featured:"/products/featured",Latest:"/products/latest",Search:"/products/search",PriceRange:"/products/price-range",UpdateStock:e=>`/products/${e}/stock`,Filter:"/products/filter"},ProductSpecifications:{Base:"/productspecifications",ById:e=>`/productspecifications/${e}`,ByProduct:e=>`/productspecifications/product/${e}`},ProductDetails:{Base:"/productdetails",ById:e=>`/productdetails/${e}`,ByProduct:e=>`/productdetails/product/${e}`},DownloadableContent:{Base:"/downloadablecontent",ById:e=>`/downloadablecontent/${e}`,ByProduct:e=>`/downloadablecontent/product/${e}`},Orders:{Base:"/orders",ById:e=>`/orders/${e}`,ByUser:e=>`/orders/user/${e}`,ByEmail:e=>`/orders/email/${e}`,ByStatus:e=>`/orders/status/${e}`,UpdateStatus:e=>`/orders/${e}/status`,Cancel:e=>`/orders/${e}/cancel`,Pending:"/orders/pending",Recent:"/orders/recent",Details:e=>`/orders/${e}/details`,Revenue:{Total:"/orders/revenue/total",Range:"/orders/revenue/range"},Filter:"/orders/filter"},Users:{Base:"/users",ById:e=>`/users/${e}`,ByEmail:e=>`/users/email/${e}`,ByRole:e=>`/users/role/${e}`,Active:"/users/active",Recent:"/users/recent",Deactivate:e=>`/users/${e}/deactivate`,Activate:e=>`/users/${e}/activate`,WithOrders:e=>`/users/${e}/orders`,EmailExists:e=>`/users/email-exists/${e}`,Filter:"/users/filter"},Cart:{Base:"/cart",ByUserId:e=>`/cart/user/${e}`,BySessionId:e=>`/cart/session/${e}`,SummaryByUserId:e=>`/cart/summary/user/${e}`,SummaryBySessionId:e=>`/cart/summary/session/${e}`,Add:"/cart/add",UpdateItem:(e,t)=>`/cart/${e}/items/${t}`,RemoveItem:(e,t)=>`/cart/${e}/items/${t}`,RemoveCartItem:e=>`/cart/items/${e}`,Clear:e=>`/cart/${e}/clear`,ClearByUserId:e=>`/cart/user/${e}/clear`,ClearBySessionId:e=>`/cart/session/${e}/clear`,GetOrCreate:"/cart/get-or-create"},Payments:"/payments",Seed:{All:"/seed/all",Statuses:"/seed/statuses",AdminUser:"/seed/admin-user",Collections:"/seed/collections",Products:"/seed/products"}};class o extends Error{constructor(e,t,r){super(e),this.status=t,this.errors=r,this.name="ApiError"}}let i=e=>{let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&""!==r&&(Array.isArray(r)?r.forEach(r=>t.append(e,String(r))):t.append(e,String(r)))});let r=t.toString();return r?`?${r}`:""},l={"Content-Type":"application/json",Accept:"application/json"}},48870:(e,t,r)=>{"use strict";r.d(t,{orderUpdateService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async updateStatus(e,t){return this.logApiCall("PATCH",a.FB.Orders.UpdateStatus(e),{statusId:t}),this.handleVoidResponse(this.client.patch(a.FB.Orders.UpdateStatus(e),{statusId:t}))}async cancel(e){return this.logApiCall("PATCH",a.FB.Orders.Cancel(e)),this.handleVoidResponse(this.client.patch(a.FB.Orders.Cancel(e)))}async confirm(e){return this.updateStatus(e,2)}async markAsProcessing(e){return this.updateStatus(e,3)}async markAsShipped(e){return this.updateStatus(e,4)}async markAsDelivered(e){return this.updateStatus(e,5)}async markAsReturned(e){return this.updateStatus(e,7)}async markAsRefunded(e){return this.updateStatus(e,8)}async markPaymentCompleted(e){return this.updateStatus(e,10)}async markPaymentFailed(e){return this.updateStatus(e,11)}async markPaymentRefunded(e){return this.updateStatus(e,12)}async bulkUpdateStatus(e,t){this.logApiCall("PATCH","Bulk Update Order Status",{orderIds:e,statusId:t,count:e.length});let r=await Promise.allSettled(e.map(e=>this.updateStatus(e,t))),s=r.filter(e=>"fulfilled"===e.status&&!0===e.value).length,a=r.filter(e=>"rejected"===e.status).map((t,r)=>`Order ${e[r]}: ${"rejected"===t.status?t.reason:"Unknown error"}`);return{success:s===e.length,updatedCount:s,errors:a}}async processOrder(e){try{let{orderGetService:t}=await Promise.resolve().then(r.bind(r,7419)),s=await t.getById(e);switch(s.statusId){case 1:return await this.confirm(e),{success:!0,currentStatus:"Confirmed",message:"Order confirmed successfully"};case 2:return await this.markAsProcessing(e),{success:!0,currentStatus:"Processing",message:"Order moved to processing"};case 3:return await this.markAsShipped(e),{success:!0,currentStatus:"Shipped",message:"Order marked as shipped"};case 4:return await this.markAsDelivered(e),{success:!0,currentStatus:"Delivered",message:"Order marked as delivered"};default:return{success:!1,currentStatus:s.status.statusName,message:"Order cannot be processed further"}}}catch(e){return console.error("Error processing order:",e),{success:!1,currentStatus:"Unknown",message:e instanceof Error?e.message:"Failed to process order"}}}async reverseStatus(e){try{let t,s,{orderGetService:a}=await Promise.resolve().then(r.bind(r,7419)),n=await a.getById(e);switch(n.statusId){case 5:t=4,s="Shipped";break;case 4:t=3,s="Processing";break;case 3:t=2,s="Confirmed";break;case 2:t=1,s="Pending";break;default:return{success:!1,previousStatus:n.status.statusName,message:"Order status cannot be reversed"}}return await this.updateStatus(e,t),{success:!0,previousStatus:s,message:`Order status reversed to ${s}`}}catch(e){return console.error("Error reversing order status:",e),{success:!1,previousStatus:"Unknown",message:e instanceof Error?e.message:"Failed to reverse order status"}}}async canUpdateStatus(e,t){try{let{orderGetService:s}=await Promise.resolve().then(r.bind(r,7419)),a=await s.getById(e),n=(({1:[2,6],2:[3,6],3:[4,6],4:[5,7],5:[7],6:[],7:[8],8:[]})[a.statusId]||[]).includes(t);return{canUpdate:n,currentStatus:a.status.statusName,reason:n?void 0:"Invalid status transition"}}catch(e){return console.error("Error checking status update validity:",e),{canUpdate:!1,reason:"Error checking order status"}}}async safeUpdateStatus(e,t){try{let r=await this.canUpdateStatus(e,t);if(!r.canUpdate)return{success:!1,message:r.reason||"Status update not allowed"};let s=await this.updateStatus(e,t);return{success:s,message:s?"Order status updated successfully":"Failed to update order status"}}catch(e){return console.error("Error in safe status update:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error occurred"}}}}let o=new n},51162:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var s=r(41459);class a{constructor(e=s.lq){this.baseUrl=e}async request(e,t=s.ym.GET,r={}){let{params:a,...n}=r,o=`${this.baseUrl}${e}`;a&&(o+=(0,s.Go)(a));let i={method:t,headers:{...s.Al,...n.headers},...n};t!==s.ym.GET&&n.body&&"object"==typeof n.body&&(i.body=JSON.stringify(n.body));try{let e,t=await fetch(o,i),r=t.headers.get("content-type");if(r&&r.includes("application/json"))e=await t.json();else{let r=await t.text();e={success:t.ok,message:t.ok?"Success":"Error",data:r}}if(!t.ok)throw new s.hD(e.message||`HTTP error! status: ${t.status}`,t.status,e.errors);return e}catch(e){if(e instanceof s.hD)throw e;throw console.error("API request failed:",e),new s.hD(e instanceof Error?e.message:"Unknown error occurred")}}async get(e,t){return this.request(e,s.ym.GET,{params:t})}async post(e,t,r){return this.request(e,s.ym.POST,{...r,body:t})}async put(e,t,r){return this.request(e,s.ym.PUT,{...r,body:t})}async patch(e,t,r){return this.request(e,s.ym.PATCH,{...r,body:t})}async delete(e,t){return this.request(e,s.ym.DELETE,t)}async upload(e,t,r){let a={...r,headers:{...r?.headers},body:t};return a.headers&&"Content-Type"in a.headers&&delete a.headers["Content-Type"],this.request(e,s.ym.POST,a)}}let n=new a},58890:(e,t,r)=>{"use strict";r.d(t,{userGetService:()=>o});var s=r(41459),a=r(22805);class n extends a.d{async getAll(){return this.logApiCall("GET",s.FB.Users.Base),this.handleResponse(this.client.get(s.FB.Users.Base))}async getById(e){return this.logApiCall("GET",s.FB.Users.ById(e)),this.handleResponse(this.client.get(s.FB.Users.ById(e)))}async getByEmail(e){return this.logApiCall("GET",s.FB.Users.ByEmail(e)),this.handleResponse(this.client.get(s.FB.Users.ByEmail(e)))}async getByRole(e){return this.logApiCall("GET",s.FB.Users.ByRole(e)),this.handleResponse(this.client.get(s.FB.Users.ByRole(e)))}async getActive(){return this.logApiCall("GET",s.FB.Users.Active),this.handleResponse(this.client.get(s.FB.Users.Active))}async getRecent(e=10){return this.logApiCall("GET",s.FB.Users.Recent,{count:e}),this.handleResponse(this.client.get(s.FB.Users.Recent,{count:e}))}async getWithOrders(e){return this.logApiCall("GET",s.FB.Users.WithOrders(e)),this.handleResponse(this.client.get(s.FB.Users.WithOrders(e)))}async emailExists(e){return this.logApiCall("GET",s.FB.Users.EmailExists(e)),this.handleResponse(this.client.get(s.FB.Users.EmailExists(e)))}async getFiltered(e){let t=a.F.cleanObject(e);return this.logApiCall("GET",s.FB.Users.Filter,t),this.handlePaginatedResponse(this.client.get(s.FB.Users.Filter,t))}async getPaginated(e=1,t=10,r="createdAt",s="desc"){return this.getFiltered({pageNumber:e,pageSize:t,sortBy:r,sortDirection:s})}async getAdmins(){return this.getByRole("admin")}async getCustomers(){return this.getByRole("customer")}async getInactive(){return(await this.getFiltered({active:!1,pageSize:100})).data}async getByCountry(e){return(await this.getFiltered({country:e,pageSize:100})).data}async getByCity(e){return(await this.getFiltered({city:e,pageSize:100})).data}async searchByName(e){return(await this.getFiltered({name:e,pageSize:100})).data}async searchByEmail(e){return(await this.getFiltered({email:e,pageSize:100})).data}async getByDateRange(e,t){let r={createdAfter:a.F.formatDate(e),createdBefore:a.F.formatDate(t),pageSize:100};return(await this.getFiltered(r)).data}async getStatistics(){try{let[e,t,r,s]=await Promise.all([this.getAll(),this.getActive(),this.getAdmins(),this.getCustomers()]),a=new Date;a.setDate(a.getDate()-30);let n=await this.getByDateRange(a,new Date);return{totalUsers:e.length,activeUsers:t.length,inactiveUsers:e.length-t.length,adminUsers:r.length,customerUsers:s.length,recentSignups:n.length}}catch(e){throw console.error("Error getting user statistics:",e),e}}async getProfile(e){try{return{user:await this.getWithOrders(e),orderCount:0,totalSpent:0,lastOrderDate:void 0}}catch(e){throw console.error("Error getting user profile:",e),e}}async validateUser(e){try{let t=await this.getById(e);return{exists:!0,active:t.active,user:t}}catch(e){return{exists:!1,active:!1}}}async validateAdminCredentials(e,t){try{let r=await this.getByEmail(e);if(!r||"admin"!==r.role||!r.active)return null;return"<EMAIL>"===e&&"132Trent@!"===t?r:null}catch(e){return console.error("Error validating admin credentials:",e),null}}}let o=new n},61135:()=>{},62614:e=>{e.exports={Headerroot:"header_Headerroot__qFKmr",header:"header_header__Zkve9",scrolled:"header_scrolled__KaDQf",nonHomePage:"header_nonHomePage__nsuQe",container:"header_container__fzi45",logo:"header_logo__Dghye",logoLink:"header_logoLink__vkk8O",logoText:"header_logoText__lEG6_",logoSubtext:"header_logoSubtext__6NOcI",nav:"header_nav__NxBck",navList:"header_navList__3vdeA",navItem:"header_navItem__83azt",navLink:"header_navLink__r8nlj",navButton:"header_navButton__nsuVv",active:"header_active__PVd5K",dropdownContainer:"header_dropdownContainer__VWoxc",dropdownIcon:"header_dropdownIcon__Zm6Wf",rotated:"header_rotated__xpZ_L",loadingIcon:"header_loadingIcon__Ak61V",dropdown:"header_dropdown__zM7fw",dropdownSlideIn:"header_dropdownSlideIn__5QwVb",dropdownList:"header_dropdownList__woAOM",dropdownItem:"header_dropdownItem__QAhhq",dropdownLink:"header_dropdownLink___cGcQ",subDropdownList:"header_subDropdownList__3jCpc",subDropdownItem:"header_subDropdownItem__RZ7SQ",subDropdownLink:"header_subDropdownLink__XLqny",subSubDropdownList:"header_subSubDropdownList__zA6dK",subSubDropdownItem:"header_subSubDropdownItem__iPXjR",subSubDropdownLink:"header_subSubDropdownLink__zfl1R",cartContainer:"header_cartContainer__nGodr",cartLink:"header_cartLink__ymTVi",cartIconWrapper:"header_cartIconWrapper__vnRO_",cartBadge:"header_cartBadge__F45rS",cartBadgeAppear:"header_cartBadgeAppear__BXBeQ"}},62868:(e,t,r)=>{Promise.resolve().then(r.bind(r,19867)),Promise.resolve().then(r.bind(r,18207)),Promise.resolve().then(r.bind(r,28253)),Promise.resolve().then(r.bind(r,22711))},63968:(e,t,r)=>{"use strict";r.d(t,{y1:()=>eB,CV:()=>ee,Yn:()=>n.Yn,eI:()=>eu,Qo:()=>$,MV:()=>el,jU:()=>p,Dv:()=>V,LV:()=>ew});var s=r(41459);r(51162);var a=r(22805);r(81093);var n=r(35255),o=r(83974);class i extends a.d{async create(e){return this.logApiCall("POST",s.FB.Products.Base,e),this.validateCreateRequest(e),this.handleResponse(this.client.post(s.FB.Products.Base,e))}async createSimple(e,t,r,s,a,n=[],o=[]){return this.create({name:e,description:t,price:r,stock:s,collectionId:a,images:n,tags:o})}async createBatch(e){return this.logApiCall("POST","Batch Products",{count:e.length}),Promise.all(e.map(e=>this.create(e)))}async createWithImages(e,t){try{let r=await this.uploadImages(t),s={...e,images:r};return this.create(s)}catch(e){throw console.error("Error creating product with images:",e),e}}async duplicate(e,t,r={}){try{let a=await this.client.get(s.FB.Products.ById(e));if(!a.success||!a.data)throw Error("Original product not found");let n=a.data,o={name:t,description:n.description,price:n.price,stock:0,collectionId:n.collectionId,images:[...n.images],tags:[...n.tags],...r};return this.create(o)}catch(e){throw console.error("Error duplicating product:",e),e}}async createVariant(e,t,r=0,a={}){try{let n=await this.client.get(s.FB.Products.ById(e));if(!n.success||!n.data)throw Error("Base product not found");let o=n.data,i={name:`${o.name} - ${t}`,description:o.description,price:o.price+r,stock:0,collectionId:o.collectionId,images:[...o.images],tags:[...o.tags,"variant"],...a};return this.create(i)}catch(e){throw console.error("Error creating product variant:",e),e}}async uploadImages(e){let t=[];for(let r of e){new FormData().append("image",r);try{t.push(`/images/products/${Date.now()}-${r.name}`)}catch(e){throw console.error("Error uploading image:",e),Error(`Failed to upload image: ${r.name}`)}}return t}validateCreateRequest(e){if(!e.name||0===e.name.trim().length)throw Error("Product name is required");if(e.name.length>200)throw Error("Product name must be 200 characters or less");if(e.price<=0)throw Error("Product price must be greater than 0");if(e.stock<0)throw Error("Product stock cannot be negative");if(!e.collectionId)throw Error("Collection ID is required");if(e.description&&e.description.length>1e3)throw Error("Description must be 1000 characters or less");if(e.images&&e.images.length>0){for(let t of e.images)if(!this.isValidImageUrl(t))throw Error(`Invalid image URL: ${t}`)}}isValidImageUrl(e){try{return new URL(e),!0}catch{return e.startsWith("/")||e.startsWith("./")||e.startsWith("../")}}}let l=new i;var c=r(86537);class d extends a.d{async delete(e){return this.logApiCall("DELETE",s.FB.Products.ById(e)),this.handleVoidResponse(this.client.delete(s.FB.Products.ById(e)))}async deleteBatch(e){return this.logApiCall("DELETE","Batch Products",{count:e.length}),Promise.all(e.map(e=>this.delete(e)))}async softDelete(e){this.logApiCall("PATCH",`Soft Delete Product ${e}`);try{let{productUpdateService:t}=await Promise.resolve().then(r.bind(r,86537));return await t.updateStock(e,0),!0}catch(e){return console.error("Failed to soft delete product:",e),!1}}async canDelete(e){try{return{canDelete:!0,hasOrders:!1,isInActiveOrders:!1}}catch(e){return console.error("Error checking if product can be deleted:",e),{canDelete:!1,reason:"Error checking product dependencies"}}}async safeDelete(e){try{let t=await this.canDelete(e);if(!t.canDelete)return{success:!1,message:t.reason||"Product cannot be deleted"};if(await this.delete(e))return{success:!0,message:"Product deleted successfully"};return{success:!1,message:"Failed to delete product"}}catch(e){return console.error("Error during safe delete:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error occurred"}}}async archive(e){try{let{productUpdateService:t}=await Promise.resolve().then(r.bind(r,86537));return await t.updateStock(e,0),await t.addTags(e,["archived"]),{success:!0,message:"Product archived successfully"}}catch(e){return console.error("Error archiving product:",e),{success:!1,message:e instanceof Error?e.message:"Failed to archive product"}}}async restore(e,t=1){try{let{productUpdateService:s}=await Promise.resolve().then(r.bind(r,86537));return await s.updateStock(e,t),await s.removeTags(e,["archived"]),{success:!0,message:"Product restored successfully"}}catch(e){return console.error("Error restoring product:",e),{success:!1,message:e instanceof Error?e.message:"Failed to restore product"}}}async deleteByCollection(e){try{let{productGetService:t}=await Promise.resolve().then(r.bind(r,83974)),s=await t.getByCollection(e);if(0===s.length)return{success:!0,message:"No products found in collection",deletedCount:0};let a=s.map(e=>e.id),n=(await this.deleteBatch(a)).filter(e=>e).length;return{success:n===s.length,message:`Deleted ${n} of ${s.length} products`,deletedCount:n}}catch(e){return console.error("Error deleting products by collection:",e),{success:!1,message:e instanceof Error?e.message:"Failed to delete products",deletedCount:0}}}async deleteOutOfStock(){try{let{productGetService:e}=await Promise.resolve().then(r.bind(r,83974)),t=await e.getOutOfStock();if(0===t.length)return{success:!0,message:"No out of stock products found",deletedCount:0};let s=t.map(e=>e.id),a=(await this.deleteBatch(s)).filter(e=>e).length;return{success:a===t.length,message:`Deleted ${a} out of stock products`,deletedCount:a}}catch(e){return console.error("Error deleting out of stock products:",e),{success:!1,message:e instanceof Error?e.message:"Failed to delete out of stock products",deletedCount:0}}}}let u=new d;class h{constructor(){this.get=o.productGetService,this.post=l,this.update=c.productUpdateService,this.delete=u}}let p=new h;class m extends a.d{async getAll(){return this.logApiCall("GET",s.FB.ProductSpecifications.Base),this.handleResponse(this.client.get(s.FB.ProductSpecifications.Base))}async getById(e){return this.logApiCall("GET",`${s.FB.ProductSpecifications.Base}/${e}`),this.handleResponse(this.client.get(`${s.FB.ProductSpecifications.Base}/${e}`))}async getByProductId(e){this.logApiCall("GET",`${s.FB.ProductSpecifications.Base}/product/${e}`);try{return await this.handleResponse(this.client.get(`${s.FB.ProductSpecifications.Base}/product/${e}`))}catch(e){if(e.response?.status===404)return null;throw e}}}class g extends a.d{async create(e){return this.logApiCall("POST",s.FB.ProductSpecifications.Base,e),this.handleResponse(this.client.post(s.FB.ProductSpecifications.Base,e))}}class y extends a.d{async update(e,t){return this.logApiCall("PUT",`${s.FB.ProductSpecifications.Base}/${e}`,t),this.handleResponse(this.client.put(`${s.FB.ProductSpecifications.Base}/${e}`,t))}async delete(e){return this.logApiCall("DELETE",`${s.FB.ProductSpecifications.Base}/${e}`),this.handleResponse(this.client.delete(`${s.FB.ProductSpecifications.Base}/${e}`))}}class f{constructor(){this.get=new m,this.post=new g,this.update=new y}}let C=new f;class w extends a.d{async getAll(){return this.logApiCall("GET",s.FB.ProductDetails.Base),this.handleResponse(this.client.get(s.FB.ProductDetails.Base))}async getById(e){return this.logApiCall("GET",`${s.FB.ProductDetails.Base}/${e}`),this.handleResponse(this.client.get(`${s.FB.ProductDetails.Base}/${e}`))}async getByProductId(e){this.logApiCall("GET",`${s.FB.ProductDetails.Base}/product/${e}`);try{return await this.handleResponse(this.client.get(`${s.FB.ProductDetails.Base}/product/${e}`))}catch(e){if(e.response?.status===404)return null;throw e}}}class v extends a.d{async create(e){return this.logApiCall("POST",s.FB.ProductDetails.Base,e),this.handleResponse(this.client.post(s.FB.ProductDetails.Base,e))}}class B extends a.d{async update(e,t){return this.logApiCall("PUT",`${s.FB.ProductDetails.Base}/${e}`,t),this.handleResponse(this.client.put(`${s.FB.ProductDetails.Base}/${e}`,t))}async delete(e){return this.logApiCall("DELETE",`${s.FB.ProductDetails.Base}/${e}`),this.handleResponse(this.client.delete(`${s.FB.ProductDetails.Base}/${e}`))}}class P{constructor(){this.get=new w,this.post=new v,this.update=new B}}let E=new P;class b extends a.d{async getAll(){return this.logApiCall("GET",s.FB.DownloadableContent.Base),this.handleResponse(this.client.get(s.FB.DownloadableContent.Base))}async getById(e){return this.logApiCall("GET",`${s.FB.DownloadableContent.Base}/${e}`),this.handleResponse(this.client.get(`${s.FB.DownloadableContent.Base}/${e}`))}async getByProductId(e){this.logApiCall("GET",`${s.FB.DownloadableContent.Base}/product/${e}`);try{return await this.handleResponse(this.client.get(`${s.FB.DownloadableContent.Base}/product/${e}`))}catch(e){if(e.response?.status===404)return null;throw e}}}class S extends a.d{async create(e){return this.logApiCall("POST",s.FB.DownloadableContent.Base,e),this.handleResponse(this.client.post(s.FB.DownloadableContent.Base,e))}}class F extends a.d{async update(e,t){return this.logApiCall("PUT",`${s.FB.DownloadableContent.Base}/${e}`,t),this.handleResponse(this.client.put(`${s.FB.DownloadableContent.Base}/${e}`,t))}async delete(e){return this.logApiCall("DELETE",`${s.FB.DownloadableContent.Base}/${e}`),this.handleResponse(this.client.delete(`${s.FB.DownloadableContent.Base}/${e}`))}}class I{constructor(){this.get=new b,this.post=new S,this.update=new F}}let A=new I;var R=r(7419);class D extends a.d{async create(e){return this.logApiCall("POST",s.FB.Orders.Base,e),this.validateCreateRequest(e),this.handleResponse(this.client.post(s.FB.Orders.Base,e))}async createCustomerOrder(e,t,r,s={},a){let n={userId:e,email:t,phoneNumber:s.phoneNumber,country:s.country,city:s.city,zipCode:s.zipCode,paymentMethod:a,orderItems:r};return this.create(n)}async createGuestOrder(e,t,r={},s){let a={userId:void 0,email:e,phoneNumber:r.phoneNumber,country:r.country,city:r.city,zipCode:r.zipCode,paymentMethod:s,orderItems:t};return this.create(a)}async createFromCart(e,t,r){let s=e.map(e=>({productId:e.productId,quantity:e.quantity})),a={userId:t.userId,email:t.email,phoneNumber:t.phoneNumber,country:t.country,city:t.city,zipCode:t.zipCode,paymentMethod:r,orderItems:s};return this.create(a)}async createQuickOrder(e,t,r,s){return this.createFromCart([{productId:e,quantity:t}],r,s)}async createBulkOrder(e,t,r){return this.createFromCart(e,t,r)}async createRepeatOrder(e,t,s){try{let{orderGetService:a}=await Promise.resolve().then(r.bind(r,7419)),n=(await a.getDetails(e)).orderItems.map(e=>({productId:e.productId,quantity:e.quantity})),o={userId:t.userId,email:t.email,phoneNumber:t.phoneNumber,country:t.country,city:t.city,zipCode:t.zipCode,paymentMethod:s,orderItems:n};return this.create(o)}catch(e){throw console.error("Error creating repeat order:",e),Error("Failed to create repeat order")}}async createWithValidation(e){try{let t=await this.validateStock(e.orderItems);if(!t.valid)return{success:!1,errors:t.errors};let r=await this.create(e);return{success:!0,order:r}}catch(e){return console.error("Error creating order with validation:",e),{success:!1,errors:[e instanceof Error?e.message:"Unknown error occurred"]}}}async validateStock(e){try{let t=[];for(let r of e)r.quantity<=0&&t.push(`Invalid quantity for product ${r.productId}`);return{valid:0===t.length,errors:t.length>0?t:void 0}}catch(e){return console.error("Error validating stock:",e),{valid:!1,errors:["Error validating stock availability"]}}}validateCreateRequest(e){if(!e.email||!a.F.isValidEmail(e.email))throw Error("Valid email is required");if(!e.orderItems||0===e.orderItems.length)throw Error("At least one order item is required");for(let t of e.orderItems){if(!t.productId||t.productId<=0)throw Error("Valid product ID is required for all order items");if(!t.quantity||t.quantity<=0)throw Error("Quantity must be greater than 0 for all order items")}if(e.phoneNumber&&!this.isValidPhoneNumber(e.phoneNumber))throw Error("Invalid phone number format");if(e.zipCode&&e.zipCode.length>20)throw Error("Zip code must be 20 characters or less")}isValidPhoneNumber(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}}let x=new D;var T=r(48870);class k extends a.d{async delete(e){return this.logApiCall("DELETE",s.FB.Orders.ById(e)),this.handleVoidResponse(this.client.delete(s.FB.Orders.ById(e)))}async deleteBatch(e){return this.logApiCall("DELETE","Batch Orders",{count:e.length}),Promise.all(e.map(e=>this.delete(e)))}async canDelete(e){try{let t,{orderGetService:s}=await Promise.resolve().then(r.bind(r,7419)),a=await s.getById(e),n=[1,6,11].includes(a.statusId);if(!n)switch(a.statusId){case 2:case 3:case 4:t="Cannot delete confirmed or processing orders";break;case 5:t="Cannot delete delivered orders";break;case 7:case 8:t="Cannot delete returned or refunded orders";break;default:t="Order cannot be deleted in current status"}return{canDelete:n,reason:t,orderStatus:a.status.statusName}}catch(e){return console.error("Error checking if order can be deleted:",e),{canDelete:!1,reason:"Error checking order status"}}}async safeDelete(e){try{let t=await this.canDelete(e);if(!t.canDelete)return{success:!1,message:t.reason||"Order cannot be deleted"};if(await this.delete(e))return{success:!0,message:"Order deleted successfully"};return{success:!1,message:"Failed to delete order"}}catch(e){return console.error("Error during safe delete:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error occurred"}}}async cancelInsteadOfDelete(e){try{let{orderUpdateService:t}=await Promise.resolve().then(r.bind(r,48870)),s=await t.cancel(e);return{success:s,message:s?"Order cancelled successfully":"Failed to cancel order"}}catch(e){return console.error("Error cancelling order:",e),{success:!1,message:e instanceof Error?e.message:"Failed to cancel order"}}}async deleteByStatus(e){try{let{orderGetService:t}=await Promise.resolve().then(r.bind(r,7419)),s=await t.getByStatus(e);if(0===s.length)return{success:!0,message:"No orders found with specified status",deletedCount:0,totalCount:0};let a=[];for(let e of s)(await this.canDelete(e.id)).canDelete&&a.push(e.id);if(0===a.length)return{success:!1,message:"No orders can be deleted in current status",deletedCount:0,totalCount:s.length};let n=(await this.deleteBatch(a)).filter(e=>e).length;return{success:n===a.length,message:`Deleted ${n} of ${a.length} eligible orders`,deletedCount:n,totalCount:s.length}}catch(e){return console.error("Error deleting orders by status:",e),{success:!1,message:e instanceof Error?e.message:"Failed to delete orders",deletedCount:0,totalCount:0}}}async deleteOldCancelledOrders(e=30){try{let{orderGetService:t}=await Promise.resolve().then(r.bind(r,7419)),s=new Date;s.setDate(s.getDate()-e);let a=(await t.getByStatus(6)).filter(e=>new Date(e.createdAt)<s);if(0===a.length)return{success:!0,message:`No cancelled orders older than ${e} days found`,deletedCount:0};let n=a.map(e=>e.id),o=(await this.deleteBatch(n)).filter(e=>e).length;return{success:o===a.length,message:`Deleted ${o} old cancelled orders`,deletedCount:o}}catch(e){return console.error("Error deleting old cancelled orders:",e),{success:!1,message:e instanceof Error?e.message:"Failed to delete old orders",deletedCount:0}}}async archive(e){try{let{orderUpdateService:t}=await Promise.resolve().then(r.bind(r,48870)),s=await this.canDelete(e);if(!s.canDelete)return{success:!1,message:s.reason||"Order cannot be archived"};let a=await t.cancel(e);return{success:a,message:a?"Order archived successfully":"Failed to archive order"}}catch(e){return console.error("Error archiving order:",e),{success:!1,message:e instanceof Error?e.message:"Failed to archive order"}}}}let _=new k;class U{constructor(){this.get=R.orderGetService,this.post=x,this.update=T.orderUpdateService,this.delete=_}}let $=new U;var N=r(58890);class j extends a.d{async create(e){return this.logApiCall("POST",s.FB.Users.Base,{...e,password:"[HIDDEN]"}),this.validateCreateRequest(e),this.handleResponse(this.client.post(s.FB.Users.Base,e))}async createCustomer(e,t,r,s,a){let n={role:"customer",email:e,password:t,name:r,phoneNumber:s,country:a?.country,city:a?.city,zipCode:a?.zipCode,active:!0};return this.create(n)}async createAdmin(e,t,r,s){return this.create({role:"admin",email:e,password:t,name:r,phoneNumber:s,active:!0})}async register(e,t,s,a){let{userGetService:n}=await Promise.resolve().then(r.bind(r,58890));if(await n.emailExists(e))throw Error("Email already exists");return this.createCustomer(e,t,s,a)}async createGuest(e){let t={role:"guest",email:e,password:this.generateRandomPassword(),active:!0};return this.create(t)}async createBatch(e){return this.logApiCall("POST","Batch Users",{count:e.length}),Promise.all(e.map(e=>this.create(e)))}async importFromCsv(e){let t={success:!0,imported:0,failed:0,errors:[]};for(let[r,s]of e.entries())try{let e={role:s.role||"customer",email:s.email,password:this.generateRandomPassword(),name:s.name,phoneNumber:s.phoneNumber,country:s.country,city:s.city,zipCode:s.zipCode,active:!0};await this.create(e),t.imported++}catch(e){t.failed++,t.errors.push(`Row ${r+1}: ${e instanceof Error?e.message:"Unknown error"}`)}return t.success=0===t.failed,t}async createWithVerification(e){try{return{user:await this.create(e),verificationRequired:!0,verificationToken:this.generateVerificationToken()}}catch(e){throw console.error("Error creating user with verification:",e),e}}async createFromSocialLogin(e,t,r,s){let a={role:"customer",email:r,password:this.generateRandomPassword(),name:s,active:!0};return this.create(a)}generateRandomPassword(){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",t="";for(let r=0;r<12;r++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}generateVerificationToken(){return Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)}validateCreateRequest(e){if(!e.email||!a.F.isValidEmail(e.email))throw Error("Valid email is required");if(!e.password||e.password.length<6)throw Error("Password must be at least 6 characters long");if(!e.role||!["admin","customer","guest"].includes(e.role))throw Error("Valid role is required (admin, customer, or guest)");if(e.phoneNumber&&!this.isValidPhoneNumber(e.phoneNumber))throw Error("Invalid phone number format");if(e.name&&e.name.length>100)throw Error("Name must be 100 characters or less");if(e.country&&e.country.length>100)throw Error("Country must be 100 characters or less");if(e.city&&e.city.length>100)throw Error("City must be 100 characters or less");if(e.zipCode&&e.zipCode.length>20)throw Error("Zip code must be 20 characters or less");if(!this.isStrongPassword(e.password))throw Error("Password must contain at least one uppercase letter, one lowercase letter, and one number")}isValidPhoneNumber(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}isStrongPassword(e){let t=/[A-Z]/.test(e),r=/[a-z]/.test(e),s=/\d/.test(e);return t&&r&&s}}let L=new j;var O=r(32829);class G extends a.d{async delete(e){return this.logApiCall("DELETE",s.FB.Users.ById(e)),this.handleVoidResponse(this.client.delete(s.FB.Users.ById(e)))}async deleteBatch(e){return this.logApiCall("DELETE","Batch Users",{count:e.length}),Promise.all(e.map(e=>this.delete(e)))}async softDelete(e){this.logApiCall("PATCH",`Soft Delete User ${e}`);try{let{userUpdateService:t}=await Promise.resolve().then(r.bind(r,32829));return await t.deactivate(e),!0}catch(e){return console.error("Failed to soft delete user:",e),!1}}async canDelete(e){try{let t,{userGetService:s}=await Promise.resolve().then(r.bind(r,58890)),a=await s.getById(e),n="admin"===a.role,o=!n;return!o&&n&&(t="Cannot delete admin users"),{canDelete:o,reason:t,hasOrders:!1,isAdmin:n}}catch(e){return console.error("Error checking if user can be deleted:",e),{canDelete:!1,reason:"Error checking user dependencies"}}}async safeDelete(e){try{let t=await this.canDelete(e);if(!t.canDelete)return{success:!1,message:t.reason||"User cannot be deleted"};if(await this.delete(e))return{success:!0,message:"User deleted successfully"};return{success:!1,message:"Failed to delete user"}}catch(e){return console.error("Error during safe delete:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error occurred"}}}async archive(e){try{let{userUpdateService:t}=await Promise.resolve().then(r.bind(r,32829));return await t.deactivate(e),{success:!0,message:"User archived successfully"}}catch(e){return console.error("Error archiving user:",e),{success:!1,message:e instanceof Error?e.message:"Failed to archive user"}}}async restore(e){try{let{userUpdateService:t}=await Promise.resolve().then(r.bind(r,32829));return await t.activate(e),{success:!0,message:"User restored successfully"}}catch(e){return console.error("Error restoring user:",e),{success:!1,message:e instanceof Error?e.message:"Failed to restore user"}}}async deleteInactiveUsers(e=365){try{let{userGetService:t}=await Promise.resolve().then(r.bind(r,58890)),s=new Date;s.setDate(s.getDate()-e);let a=(await t.getInactive()).filter(e=>new Date(e.createdAt)<s);if(0===a.length)return{success:!0,message:`No inactive users older than ${e} days found`,deletedCount:0};let n=[];for(let e of a)(await this.canDelete(e.id)).canDelete&&n.push(e.id);if(0===n.length)return{success:!1,message:"No inactive users can be safely deleted",deletedCount:0};let o=(await this.deleteBatch(n)).filter(e=>e).length;return{success:o===n.length,message:`Deleted ${o} inactive users`,deletedCount:o}}catch(e){return console.error("Error deleting inactive users:",e),{success:!1,message:e instanceof Error?e.message:"Failed to delete inactive users",deletedCount:0}}}async deleteGuestUsers(){try{let{userGetService:e}=await Promise.resolve().then(r.bind(r,58890)),t=await e.getByRole("guest");if(0===t.length)return{success:!0,message:"No guest users found",deletedCount:0};let s=t.map(e=>e.id),a=(await this.deleteBatch(s)).filter(e=>e).length;return{success:a===t.length,message:`Deleted ${a} guest users`,deletedCount:a}}catch(e){return console.error("Error deleting guest users:",e),{success:!1,message:e instanceof Error?e.message:"Failed to delete guest users",deletedCount:0}}}async anonymize(e){try{let{userUpdateService:t}=await Promise.resolve().then(r.bind(r,32829));return await t.update(e,{role:"customer",name:"Anonymized User",phoneNumber:void 0,country:void 0,city:void 0,zipCode:void 0,active:!1}),{success:!0,message:"User data anonymized successfully"}}catch(e){return console.error("Error anonymizing user:",e),{success:!1,message:e instanceof Error?e.message:"Failed to anonymize user"}}}async bulkSoftDelete(e){this.logApiCall("PATCH","Bulk Soft Delete Users",{count:e.length});let t=await Promise.allSettled(e.map(e=>this.softDelete(e))),r=t.filter(e=>"fulfilled"===e.status&&!0===e.value).length,s=t.filter(e=>"rejected"===e.status).map((t,r)=>`User ${e[r]}: ${"rejected"===t.status?t.reason:"Unknown error"}`);return{success:r===e.length,deactivatedCount:r,errors:s}}}let q=new G;class z{constructor(){this.get=N.userGetService,this.post=L,this.update=O.userUpdateService,this.delete=q}}let V=new z;class M extends a.d{async getByUserId(e){let t=s.FB.Cart.ByUserId(e);this.logApiCall("GET",t);try{return this.handleResponse(this.client.get(t))}catch(e){if(e.response?.status===404)return null;throw e}}async getBySessionId(e){let t=s.FB.Cart.BySessionId(e);this.logApiCall("GET",t);try{return this.handleResponse(this.client.get(t))}catch(e){if(e.response?.status===404)return null;throw e}}async getSummaryByUserId(e){let t=s.FB.Cart.SummaryByUserId(e);this.logApiCall("GET",t);try{return this.handleResponse(this.client.get(t))}catch(e){if(e.response?.status===404)return null;throw e}}async getSummaryBySessionId(e){let t=s.FB.Cart.SummaryBySessionId(e);this.logApiCall("GET",t);try{return this.handleResponse(this.client.get(t))}catch(e){if(e.response?.status===404)return null;throw e}}async getOrCreate(e,t){let r={};e&&(r.userId=e),t&&(r.sessionId=t);let a=s.FB.Cart.GetOrCreate;return this.logApiCall("POST",a,r),this.handleResponse(this.client.post(a,null,{params:r}))}}let H=new M;class W extends a.d{async addToCart(e){let t=s.FB.Cart.Add;return this.logApiCall("POST",t),this.handleResponse(this.client.post(t,e))}}let K=new W;class Q extends a.d{async updateCartItem(e,t,r){let a=s.FB.Cart.UpdateItem(e,t);return this.logApiCall("PUT",a),this.handleResponse(this.client.put(a,r))}}let Y=new Q;class Z extends a.d{async removeFromCart(e,t){let r=s.FB.Cart.RemoveItem(e,t);return this.logApiCall("DELETE",r),this.handleResponse(this.client.delete(r))}async removeCartItem(e){let t=s.FB.Cart.RemoveCartItem(e);return this.logApiCall("DELETE",t),this.handleResponse(this.client.delete(t))}async clearCart(e){let t=s.FB.Cart.Clear(e);return this.logApiCall("DELETE",t),this.handleResponse(this.client.delete(t))}async clearCartByUserId(e){let t=s.FB.Cart.ClearByUserId(e);return this.logApiCall("DELETE",t),this.handleResponse(this.client.delete(t))}async clearCartBySessionId(e){let t=s.FB.Cart.ClearBySessionId(e);return this.logApiCall("DELETE",t),this.handleResponse(this.client.delete(t))}}let X=new Z;class J{constructor(){this.get=H,this.post=K,this.update=Y,this.delete=X}}let ee=new J;class et extends a.d{async createPaymentIntent(e){return this.logApiCall("POST",`${s.FB.Payments}/stripe/create-intent`,e),this.handleResponse(this.client.post(`${s.FB.Payments}/stripe/create-intent`,e))}async createLegacyIntent(e,t="usd"){return this.logApiCall("POST",`${s.FB.Payments}/create-intent`,{amount:e,currency:t}),this.handleResponse(this.client.post(`${s.FB.Payments}/create-intent`,{amount:e,currency:t}))}async confirmPayment(e){return this.logApiCall("POST",`${s.FB.Payments}/stripe/confirm`,e),this.handleResponse(this.client.post(`${s.FB.Payments}/stripe/confirm`,e))}async createAffirmIntent(e,t="usd",r){return this.createPaymentIntent({amount:e,currency:t,paymentMethodType:"affirm",confirmationMethod:!1,metadata:r})}async createApplePayIntent(e,t="usd",r){return this.createPaymentIntent({amount:e,currency:t,paymentMethodType:"apple_pay",confirmationMethod:!1,metadata:r})}async createApplePaySession(e){return this.logApiCall("POST",`${s.FB.Payments}/apple-pay/session`,e),this.handleResponse(this.client.post(`${s.FB.Payments}/apple-pay/session`,e))}async validateApplePayMerchant(e,t){return this.createApplePaySession(t)}async processApplePayPayment(e,t,r="usd"){let s=await this.createApplePayIntent(t,r,{apple_pay_payment:e});if(!s.success||!s.paymentIntentId)throw Error(s.message||"Failed to create Apple Pay payment intent");return this.confirmPayment({paymentIntentId:s.paymentIntentId,paymentMethod:"apple_pay",orderId:0})}async processAffirmPayment(e,t="usd",r,s){return this.createAffirmIntent(e,t,{...s,order_id:r})}async handleWebhookEvent(e){switch(console.log("Stripe webhook event received:",e),e.type){case"payment_intent.succeeded":console.log("Payment succeeded:",e.data.object);break;case"payment_intent.payment_failed":console.log("Payment failed:",e.data.object);break;default:console.log("Unhandled event type:",e.type)}}getPaymentMethodCapabilities(){return{card:!0,affirm:!0,applePay:this.isApplePayAvailable()}}isApplePayAvailable(){return!1}formatAmountForStripe(e){return Math.round(100*e)}formatAmountFromStripe(e){return e/100}}let er=new et;class es extends a.d{async createOrder(e){return this.logApiCall("POST",`${s.FB.Payments}/paypal/create-order`,e),this.handleResponse(this.client.post(`${s.FB.Payments}/paypal/create-order`,e))}async captureOrder(e){return this.logApiCall("POST",`${s.FB.Payments}/paypal/capture/${e}`,{}),this.handleResponse(this.client.post(`${s.FB.Payments}/paypal/capture/${e}`,{}))}async getOrder(e){return this.logApiCall("GET",`${s.FB.Payments}/paypal/order/${e}`,{}),this.handleResponse(this.client.get(`${s.FB.Payments}/paypal/order/${e}`))}async createCheckoutOrder(e,t="USD",r,s,a){let n={amount:e,currency:t,intent:"CAPTURE",returnUrl:s,cancelUrl:a,purchaseUnit:{description:r||"Cast Stone Purchase",amount:{currencyCode:t,value:e.toFixed(2)}}};return this.createOrder(n)}generateButtonsConfig(e,t="USD",r,s,a,n){return{createOrder:async(r,s)=>{try{let r=await this.createCheckoutOrder(e,t,n);if(!r.success||!r.orderId)throw Error(r.message||"Failed to create PayPal order");return r.orderId}catch(e){throw console.error("Error creating PayPal order:",e),e}},onApprove:async(e,t)=>{try{let t=await this.captureOrder(e.orderID);if(t.success)console.log("PayPal payment captured successfully:",t),r?.(t);else throw Error(t.message||"Failed to capture PayPal payment")}catch(e){console.error("Error capturing PayPal payment:",e),s?.(e)}},onError:e=>{console.error("PayPal error:",e),s?.(e)},onCancel:e=>{console.log("PayPal payment cancelled:",e),a?.(e)},style:{layout:"vertical",color:"gold",shape:"rect",label:"paypal",tagline:!1,height:40}}}async loadPayPalSDK(e,t="USD"){return new Promise((r,s)=>{if(window.paypal)return void r();let a=document.createElement("script");a.src=`https://www.paypal.com/sdk/js?client-id=${e}&currency=${t}&intent=capture`,a.async=!0,a.onload=()=>{window.paypal?r():s(Error("PayPal SDK failed to load"))},a.onerror=()=>{s(Error("Failed to load PayPal SDK script"))},document.head.appendChild(a)})}async renderPayPalButtons(e,t){if(!window.paypal)throw Error("PayPal SDK not loaded");let r=document.getElementById(e);if(!r)throw Error(`Container with ID '${e}' not found`);r.innerHTML="",window.paypal.Buttons(t).render(`#${e}`)}validateOrderAmount(e,t="USD"){return!(e<=0)&&e>=(({USD:.01,EUR:.01,GBP:.01,CAD:.01,AUD:.01,JPY:1})[t]||.01)}formatAmountForPayPal(e){return e.toFixed(2)}getSupportedCurrencies(){return["USD","EUR","GBP","CAD","AUD","JPY","CHF","NOK","SEK","DKK","PLN","CZK","HUF","ILS","MXN","BRL","MYR","PHP","THB","TWD","NZD","HKD","SGD","RUB"]}isCurrencySupported(e){return this.getSupportedCurrencies().includes(e.toUpperCase())}}let ea=new es;class en extends a.d{async confirmPaymentAndNotify(e){return this.logApiCall("POST",`${s.FB.Payments}/confirm-and-notify`,e),this.handleResponse(this.client.post(`${s.FB.Payments}/confirm-and-notify`,e))}async processPayment(e,t,r="USD",s,a){switch(e){case"stripe":return this.stripe.createPaymentIntent({amount:this.stripe.formatAmountForStripe(t),currency:r.toLowerCase(),paymentMethodType:"card",confirmationMethod:!1,metadata:{order_id:s,...a}});case"affirm":return this.stripe.createAffirmIntent(this.stripe.formatAmountForStripe(t),r.toLowerCase(),{order_id:s,...a});case"apple_pay":return this.stripe.createApplePayIntent(this.stripe.formatAmountForStripe(t),r.toLowerCase(),{order_id:s,...a});case"paypal":return this.paypal.createCheckoutOrder(t,r.toUpperCase(),a?.description||"Cast Stone Purchase",a?.returnUrl,a?.cancelUrl);default:throw Error(`Unsupported payment method: ${e}`)}}async completePayment(e,t,r){return this.confirmPaymentAndNotify({paymentIntentId:t,paymentMethod:e,orderId:r})}getAvailablePaymentMethods(){let e=this.stripe.getPaymentMethodCapabilities();return[{method:"stripe",name:"Credit Card",description:"Visa, Mastercard, American Express",available:e.card,icon:"\uD83D\uDCB3"},{method:"paypal",name:"PayPal",description:"Pay securely with your PayPal account",available:!0,icon:"\uD83C\uDD7F️"},{method:"affirm",name:"Affirm",description:"Buy now, pay later with Affirm",available:e.affirm,icon:"\uD83D\uDCC5"},{method:"apple_pay",name:"Apple Pay",description:"Pay with Touch ID or Face ID",available:e.applePay,icon:"\uD83C\uDF4E"}]}validatePaymentAmount(e,t="USD"){let r=[];e<=0&&r.push("Amount must be greater than zero"),this.paypal.validateOrderAmount(e,t)||r.push(`Amount is below minimum for ${t}`);let s=(t.toLowerCase(),.5);return e<s&&r.push(`Amount is below Stripe minimum of ${s} ${t}`),{valid:0===r.length,errors:r}}formatCurrency(e,t="USD"){return new Intl.NumberFormat("en-US",{style:"currency",currency:t.toUpperCase()}).format(e)}getPaymentMethodConfig(e){switch(e){case"stripe":case"affirm":case"apple_pay":return{publishableKey:process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,appearance:{theme:"stripe",variables:{colorPrimary:"#1e3a8a",colorBackground:"#ffffff",colorText:"#1f2937",colorDanger:"#dc2626",fontFamily:"system-ui, sans-serif",spacingUnit:"4px",borderRadius:"6px"}}};case"paypal":return{clientId:process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,currency:"USD",intent:"capture",style:{layout:"vertical",color:"gold",shape:"rect",label:"paypal",tagline:!1,height:40}};default:return{}}}handlePaymentError(e,t){return(console.error(`Payment error (${t}):`,e),e.message?.includes("insufficient_funds"))?{userMessage:"Your payment method has insufficient funds. Please try a different payment method.",technicalMessage:e.message,shouldRetry:!0}:e.message?.includes("card_declined")?{userMessage:"Your card was declined. Please check your card details or try a different payment method.",technicalMessage:e.message,shouldRetry:!0}:e.message?.includes("network")?{userMessage:"Network error. Please check your connection and try again.",technicalMessage:e.message,shouldRetry:!0}:{userMessage:"An error occurred while processing your payment. Please try again.",technicalMessage:e.message||"Unknown payment error",shouldRetry:!0}}get stripeService(){return this.stripe}get paypalService(){return this.paypal}constructor(...e){super(...e),this.stripe=er,this.paypal=ea}}let eo=new en;class ei{async processPayment(e,t,r="USD",s,a){return this.unified.processPayment(e,t,r,s,a)}async completePayment(e,t,r){return this.unified.completePayment(e,t,r)}getAvailablePaymentMethods(){return this.unified.getAvailablePaymentMethods()}validatePaymentAmount(e,t="USD"){return this.unified.validatePaymentAmount(e,t)}formatCurrency(e,t="USD"){return this.unified.formatCurrency(e,t)}getPaymentMethodConfig(e){return this.unified.getPaymentMethodConfig(e)}handlePaymentError(e,t){return this.unified.handlePaymentError(e,t)}constructor(){this.stripe=er,this.paypal=ea,this.unified=eo}}let el=new ei;class ec extends a.d{async seedAll(){this.logApiCall("POST",s.FB.Seed.All);try{let e=await this.client.post(s.FB.Seed.All);return{success:e.success,message:e.message||"All data seeded successfully"}}catch(e){return console.error("Error seeding all data:",e),{success:!1,message:e instanceof Error?e.message:"Failed to seed data"}}}async seedStatuses(){this.logApiCall("POST",s.FB.Seed.Statuses);try{let e=await this.client.post(s.FB.Seed.Statuses);return{success:e.success,message:e.message||"Status data seeded successfully"}}catch(e){return console.error("Error seeding statuses:",e),{success:!1,message:e instanceof Error?e.message:"Failed to seed statuses"}}}async seedAdminUser(){this.logApiCall("POST",s.FB.Seed.AdminUser);try{let e=await this.client.post(s.FB.Seed.AdminUser);return{success:e.success,message:e.message||"Admin user seeded successfully"}}catch(e){return console.error("Error seeding admin user:",e),{success:!1,message:e instanceof Error?e.message:"Failed to seed admin user"}}}async seedCollections(){this.logApiCall("POST",s.FB.Seed.Collections);try{let e=await this.client.post(s.FB.Seed.Collections);return{success:e.success,message:e.message||"Sample collections seeded successfully"}}catch(e){return console.error("Error seeding collections:",e),{success:!1,message:e instanceof Error?e.message:"Failed to seed collections"}}}async seedProducts(){this.logApiCall("POST",s.FB.Seed.Products);try{let e=await this.client.post(s.FB.Seed.Products);return{success:e.success,message:e.message||"Sample products seeded successfully"}}catch(e){return console.error("Error seeding products:",e),{success:!1,message:e instanceof Error?e.message:"Failed to seed products"}}}async initializeDatabase(){let e=[];try{let t=await this.seedStatuses();e.push({step:"Seed Statuses",success:t.success,message:t.message});let r=await this.seedAdminUser();e.push({step:"Seed Admin User",success:r.success,message:r.message});let s=await this.seedCollections();e.push({step:"Seed Collections",success:s.success,message:s.message});let a=await this.seedProducts();e.push({step:"Seed Products",success:a.success,message:a.message});let n=e.every(e=>e.success);return{success:n,message:n?"Database initialized successfully":"Database initialization completed with some errors",steps:e}}catch(t){return console.error("Error initializing database:",t),{success:!1,message:t instanceof Error?t.message:"Failed to initialize database",steps:e}}}async checkSeedingStatus(){try{return{needsSeeding:!1,missingData:[]}}catch(e){return console.error("Error checking seeding status:",e),{needsSeeding:!0,missingData:["Unable to check status"]}}}async resetAndReseed(){return{success:!1,message:"Reset and reseed is not allowed in production"}}}let ed=new ec;r(21152),r(93112);let eu={get:()=>Promise.resolve().then(r.bind(r,21152)).then(e=>e.contactFormGetService),post:()=>Promise.resolve().then(r.bind(r,93112)).then(e=>e.contactFormPostService)};class eh extends a.d{async getAll(){return this.get("/api/wholesalebuyers")}async getById(e){return this.get(`/api/wholesalebuyers/${e}`)}async getByEmail(e){return this.get(`/api/wholesalebuyers/email/${encodeURIComponent(e)}`)}async getByStatus(e){return this.get(`/api/wholesalebuyers/status/${e}`)}async getPending(){return this.get("/api/wholesalebuyers/pending")}async getApproved(){return this.get("/api/wholesalebuyers/approved")}async getRecent(e=10){return this.get(`/api/wholesalebuyers/recent?count=${e}`)}async checkApproval(e){return this.get(`/api/wholesalebuyers/check-approval/${encodeURIComponent(e)}`)}}let ep=new eh;class em extends a.d{async submitApplication(e){return this.post("/api/wholesalebuyers/apply",e)}async approveApplication(e,t){return this.put(`/api/wholesalebuyers/${e}/approve`,t)}async rejectApplication(e,t){return this.put(`/api/wholesalebuyers/${e}/reject`,t)}}let eg=new em;class ey extends a.d{async deleteApplication(e){return this.delete(`/api/wholesalebuyers/${e}`)}}let ef=new ey;class eC{constructor(){this.get=ep,this.post=eg,this.delete=ef}}let ew=new eC;class ev extends a.d{async login(e){return this.post("/api/auth/login",e)}async checkWholesaleStatus(e){return this.get(`/api/auth/check-wholesale-status/${encodeURIComponent(e)}`)}async getUserByEmail(e){return this.get(`/api/auth/user/${encodeURIComponent(e)}`)}}let eB=new ev;class eP{constructor(){this.collections=n.Yn,this.products=p,this.productSpecifications=C,this.productDetails=E,this.downloadableContent=A,this.orders=$,this.users=V,this.cart=ee,this.payments=el,this.seed=ed,this.contactForm=eu,this.wholesaleBuyers=ew,this.auth=eB}}new eP},68653:(e,t,r)=>{"use strict";r.d(t,{WholesaleAuthProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call WholesaleAuthProvider() from the server but WholesaleAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\WholesaleAuthContext.tsx","WholesaleAuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useWholesaleAuth() from the server but useWholesaleAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\WholesaleAuthContext.tsx","useWholesaleAuth")},69999:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},70569:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\components\\\\shared\\\\Footer\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Footer\\Footer.tsx","default")},78501:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\components\\\\shared\\\\Header\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\components\\shared\\Header\\Header.tsx","default")},80614:e=>{e.exports={footer:"footer_footer__UGvN0",container:"footer_container__x9CeZ",content:"footer_content__3aG4z",brand:"footer_brand__ffOQv",brandName:"footer_brandName__m4Txr",brandDescription:"footer_brandDescription__ZQmxR",socialLinks:"footer_socialLinks__EvEri",socialLink:"footer_socialLink__yIRWY",linkGroup:"footer_linkGroup__xbYXp",linkGroupTitle:"footer_linkGroupTitle__VjPGq",linkList:"footer_linkList__1JHqS",link:"footer_link__39sDb",contactInfo:"footer_contactInfo__6zB7H",contactTitle:"footer_contactTitle__btJnn",contactDetails:"footer_contactDetails__Hjp7k",contactItem:"footer_contactItem__TyXHd",contactLabel:"footer_contactLabel__NMVq1",contactValue:"footer_contactValue__D85h3",bottom:"footer_bottom__4T5SF",copyright:"footer_copyright__CZ_QD",legalLinks:"footer_legalLinks__ReZIE",legalLink:"footer_legalLink___uEbK"}},81093:(e,t,r)=>{"use strict";r.d(t,{e:()=>s});var s=function(e){return e[e.ProductInquiry=1]="ProductInquiry",e[e.RequestDesignConsultation=2]="RequestDesignConsultation",e[e.CustomOrders=3]="CustomOrders",e[e.TradePartnerships=4]="TradePartnerships",e[e.InstallationSupport=5]="InstallationSupport",e[e.ShippingAndLeadTimes=6]="ShippingAndLeadTimes",e[e.RequestCatalogPriceList=7]="RequestCatalogPriceList",e[e.MediaPressInquiry=8]="MediaPressInquiry",e[e.GeneralQuestions=9]="GeneralQuestions",e}({})},83974:(e,t,r)=>{"use strict";r.d(t,{productGetService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async getAll(){return this.logApiCall("GET",a.FB.Products.Base),this.handleResponse(this.client.get(a.FB.Products.Base))}async getById(e){return this.logApiCall("GET",a.FB.Products.ById(e)),this.handleResponse(this.client.get(a.FB.Products.ById(e)))}async getByCollection(e){return this.logApiCall("GET",a.FB.Products.ByCollection(e)),this.handleResponse(this.client.get(a.FB.Products.ByCollection(e)))}async getInStock(){return this.logApiCall("GET",a.FB.Products.InStock),this.handleResponse(this.client.get(a.FB.Products.InStock))}async getFeatured(e=10){return this.logApiCall("GET",a.FB.Products.Featured,{count:e}),this.handleResponse(this.client.get(a.FB.Products.Featured,{count:e}))}async getLatest(e=10){return this.logApiCall("GET",a.FB.Products.Latest,{count:e}),this.handleResponse(this.client.get(a.FB.Products.Latest,{count:e}))}async search(e){return this.logApiCall("GET",a.FB.Products.Search,{name:e}),this.handleResponse(this.client.get(a.FB.Products.Search,{name:e}))}async getByPriceRange(e,t){return this.logApiCall("GET",a.FB.Products.PriceRange,{minPrice:e,maxPrice:t}),this.handleResponse(this.client.get(a.FB.Products.PriceRange,{minPrice:e,maxPrice:t}))}async getFiltered(e){let t=s.F.cleanObject(e);return this.logApiCall("GET",a.FB.Products.Filter,t),this.handlePaginatedResponse(this.client.get(a.FB.Products.Filter,t))}async getPaginated(e=1,t=10,r="createdAt",s="desc"){return this.getFiltered({pageNumber:e,pageSize:t,sortBy:r,sortDirection:s})}async getByTag(e){return(await this.getFiltered({tag:e,pageSize:100})).data}async getLowStock(e=10){return(await this.getFiltered({minStock:1,maxStock:e,pageSize:100})).data}async getOutOfStock(){return(await this.getFiltered({inStock:!1,pageSize:100})).data}async getByPriceRangePaginated(e,t,r=1,s=10){return this.getFiltered({minPrice:e,maxPrice:t,pageNumber:r,pageSize:s,sortBy:"price",sortDirection:"asc"})}async getByDateRange(e,t){let r={createdAfter:s.F.formatDate(e),createdBefore:s.F.formatDate(t),pageSize:100};return(await this.getFiltered(r)).data}async getRecommendations(e,t=5){try{let r=await this.getById(e);return(await this.getByCollection(r.collectionId)).filter(t=>t.id!==e).slice(0,t)}catch(e){return console.error("Error getting product recommendations:",e),[]}}}let o=new n},86537:(e,t,r)=>{"use strict";r.d(t,{productUpdateService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async update(e,t){return this.logApiCall("PUT",a.FB.Products.ById(e),t),this.validateUpdateRequest(t),this.handleResponse(this.client.put(a.FB.Products.ById(e),t))}async updateStock(e,t){if(this.logApiCall("PATCH",a.FB.Products.UpdateStock(e),{stock:t}),t<0)throw Error("Stock cannot be negative");return this.handleVoidResponse(this.client.patch(a.FB.Products.UpdateStock(e),t))}async updatePrice(e,t){if(t<=0)throw Error("Price must be greater than 0");let r=await this.getCurrentProduct(e),s={...this.mapProductToUpdateRequest(r),price:t};return this.update(e,s)}async updateBasicInfo(e,t,r){let s=await this.getCurrentProduct(e),a={...this.mapProductToUpdateRequest(s),name:t,description:r};return this.update(e,a)}async updateImages(e,t){let r=await this.getCurrentProduct(e),s={...this.mapProductToUpdateRequest(r),images:t};return this.update(e,s)}async addImages(e,t){let r=[...(await this.getCurrentProduct(e)).images||[],...t];return this.updateImages(e,r)}async removeImages(e,t){let r=((await this.getCurrentProduct(e)).images||[]).filter(e=>!t.includes(e));return this.updateImages(e,r)}async updateTags(e,t){let r=await this.getCurrentProduct(e),s={...this.mapProductToUpdateRequest(r),tags:t};return this.update(e,s)}async addTags(e,t){let r=[...new Set([...(await this.getCurrentProduct(e)).tags||[],...t])];return this.updateTags(e,r)}async removeTags(e,t){let r=((await this.getCurrentProduct(e)).tags||[]).filter(e=>!t.includes(e));return this.updateTags(e,r)}async moveToCollection(e,t){let r=await this.getCurrentProduct(e),s={...this.mapProductToUpdateRequest(r),collectionId:t};return this.update(e,s)}async adjustStock(e,t){let r=(await this.getCurrentProduct(e)).stock+t;if(r<0)throw Error("Stock adjustment would result in negative stock");return this.updateStock(e,r)}async increaseStock(e,t){if(t<=0)throw Error("Amount must be positive");return this.adjustStock(e,t)}async decreaseStock(e,t){if(t<=0)throw Error("Amount must be positive");return this.adjustStock(e,-t)}async applyDiscount(e,t){if(t<0||t>100)throw Error("Discount percentage must be between 0 and 100");let r=(await this.getCurrentProduct(e)).price*(1-t/100);return this.updatePrice(e,Math.round(100*r)/100)}async getCurrentProduct(e){let t=await this.client.get(a.FB.Products.ById(e));if(!t.success||!t.data)throw Error("Product not found");return t.data}mapProductToUpdateRequest(e){return{name:e.name,description:e.description,price:e.price,stock:e.stock,collectionId:e.collectionId,images:e.images,tags:e.tags}}validateUpdateRequest(e){if(!e.name||0===e.name.trim().length)throw Error("Product name is required");if(e.name.length>200)throw Error("Product name must be 200 characters or less");if(e.price<=0)throw Error("Product price must be greater than 0");if(e.stock<0)throw Error("Product stock cannot be negative");if(!e.collectionId)throw Error("Collection ID is required");if(e.description&&e.description.length>1e3)throw Error("Description must be 1000 characters or less")}}let o=new n},89690:(e,t,r)=>{"use strict";r.d(t,{collectionUpdateService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async update(e,t){return this.logApiCall("PUT",a.FB.Collections.ById(e),t),this.validateUpdateRequest(t),this.handleResponse(this.client.put(a.FB.Collections.ById(e),t))}async updateBasicInfo(e,t,r,s){let n=await this.client.get(a.FB.Collections.ById(e));if(!n.success||!n.data)throw Error("Collection not found");let o={name:t,description:r,level:n.data.level,parentCollectionId:n.data.parentCollectionId,childCollectionIds:n.data.childCollectionIds,tags:n.data.tags,published:n.data.published,updatedBy:s,images:[]};return this.update(e,o)}async updateTags(e,t,r){let s=await this.client.get(a.FB.Collections.ById(e));if(!s.success||!s.data)throw Error("Collection not found");let n={name:s.data.name,description:s.data.description,level:s.data.level,parentCollectionId:s.data.parentCollectionId,childCollectionIds:s.data.childCollectionIds,tags:t,published:s.data.published,updatedBy:r,images:[]};return this.update(e,n)}async updatePublishStatus(e,t,r){let s=await this.client.get(a.FB.Collections.ById(e));if(!s.success||!s.data)throw Error("Collection not found");let n={name:s.data.name,description:s.data.description,level:s.data.level,parentCollectionId:s.data.parentCollectionId,childCollectionIds:s.data.childCollectionIds,tags:s.data.tags,published:t,updatedBy:r,images:[]};return this.update(e,n)}async moveToParent(e,t,r,s){let n=await this.client.get(a.FB.Collections.ById(e));if(!n.success||!n.data)throw Error("Collection not found");let o={name:n.data.name,description:n.data.description,level:r,parentCollectionId:t,childCollectionIds:n.data.childCollectionIds,tags:n.data.tags,published:n.data.published,updatedBy:s,images:[]};return this.update(e,o)}async addTags(e,t,r){let s=await this.client.get(a.FB.Collections.ById(e));if(!s.success||!s.data)throw Error("Collection not found");let n=[...new Set([...s.data.tags||[],...t])];return this.updateTags(e,n,r)}async removeTags(e,t,r){let s=await this.client.get(a.FB.Collections.ById(e));if(!s.success||!s.data)throw Error("Collection not found");let n=(s.data.tags||[]).filter(e=>!t.includes(e));return this.updateTags(e,n,r)}validateUpdateRequest(e){if(!e.name||0===e.name.trim().length)throw Error("Collection name is required");if(e.name.length>200)throw Error("Collection name must be 200 characters or less");if(!e.level||e.level<1||e.level>3)throw Error("Collection level must be 1, 2, or 3");if(1===e.level&&e.parentCollectionId)throw Error("Root collections (level 1) cannot have a parent");if(e.level>1&&!e.parentCollectionId)throw Error("Sub-collections (level 2-3) must have a parent");if(!e.updatedBy||0===e.updatedBy.trim().length)throw Error("UpdatedBy is required");if(e.updatedBy.length>100)throw Error("UpdatedBy must be 100 characters or less");if(e.description&&e.description.length>1e3)throw Error("Description must be 1000 characters or less")}}let o=new n},91791:(e,t,r)=>{"use strict";r.d(t,{collectionGetService:()=>o});var s=r(22805),a=r(41459);class n extends s.d{async getAll(){return this.logApiCall("GET",a.FB.Collections.Base),this.handleResponse(this.client.get(a.FB.Collections.Base))}async getById(e){return this.logApiCall("GET",a.FB.Collections.ById(e)),this.handleResponse(this.client.get(a.FB.Collections.ById(e)))}async getByLevel(e){return this.logApiCall("GET",a.FB.Collections.ByLevel(e)),this.handleResponse(this.client.get(a.FB.Collections.ByLevel(e)))}async getChildren(e){return this.logApiCall("GET",a.FB.Collections.Children(e)),this.handleResponse(this.client.get(a.FB.Collections.Children(e)))}async getHierarchy(){return this.logApiCall("GET",a.FB.Collections.Hierarchy),this.handleResponse(this.client.get(a.FB.Collections.Hierarchy))}async getPublished(){return this.logApiCall("GET",a.FB.Collections.Published),this.handleResponse(this.client.get(a.FB.Collections.Published))}async search(e){return this.logApiCall("GET",a.FB.Collections.Search,{name:e}),this.handleResponse(this.client.get(a.FB.Collections.Search,{name:e}))}async getFiltered(e){let t=s.F.cleanObject(e);return this.logApiCall("GET",a.FB.Collections.Filter,t),this.handlePaginatedResponse(this.client.get(a.FB.Collections.Filter,t))}async getPaginated(e=1,t=10,r="createdAt",s="desc"){return this.getFiltered({pageNumber:e,pageSize:t,sortBy:r,sortDirection:s})}async getRootCollections(){return this.getByLevel(1)}async getByTag(e){return(await this.getFiltered({tag:e,pageSize:100})).data}async getByCreatedBy(e){return(await this.getFiltered({createdBy:e,pageSize:100})).data}async getByDateRange(e,t){let r={createdAfter:s.F.formatDate(e),createdBefore:s.F.formatDate(t),pageSize:100};return(await this.getFiltered(r)).data}}let o=new n},93112:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ContactFormPostService:()=>a,contactFormPostService:()=>n});var s=r(22805);class a extends s.d{async create(e){return this.logApiCall("POST","/contactform",e),this.validateCreateRequest(e),this.handleResponse(this.client.post("/contactform",e))}async submit(e){let t={name:e.name.trim(),email:e.email.trim().toLowerCase(),phoneNumber:e.phoneNumber.trim(),company:e.company?.trim()||void 0,state:e.state.trim(),inquiry:e.inquiry,message:e.message.trim()};return this.create(t)}validateCreateRequest(e){if(!e.name||0===e.name.trim().length)throw Error("Name is required");if(e.name.length>100)throw Error("Name must be 100 characters or less");if(!e.email||!s.F.isValidEmail(e.email))throw Error("Valid email is required");if(!e.phoneNumber||0===e.phoneNumber.trim().length)throw Error("Phone number is required");if(!this.isValidPhoneNumber(e.phoneNumber))throw Error("Please enter a valid phone number");if(e.company&&e.company.length>200)throw Error("Company name must be 200 characters or less");if(!e.state||0===e.state.trim().length)throw Error("State is required");if(e.state.length>100)throw Error("State must be 100 characters or less");if(!e.inquiry||![1,2,3,4,5,6,7,8,9].includes(e.inquiry))throw Error("Please select a valid inquiry type");if(!e.message||0===e.message.trim().length)throw Error("Message is required");if(e.message.length<10)throw Error("Message must be at least 10 characters long");if(e.message.length>2e3)throw Error("Message must be 2000 characters or less")}isValidPhoneNumber(e){return/^[\+]?[1-9][\d]{0,15}$/.test(e.replace(/[\s\-\(\)]/g,""))}}let n=new a},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>h});var s=r(37413),a=r(22376),n=r.n(a),o=r(68726),i=r.n(o);r(61135);var l=r(78501),c=r(70569),d=r(37043),u=r(68653);let h={title:"Cast Stone - Modern Web Application",description:"A modern web application built with Next.js and .NET Core"};function p({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,children:(0,s.jsx)(u.WholesaleAuthProvider,{children:(0,s.jsxs)(d.CartProvider,{children:[(0,s.jsx)(l.default,{}),(0,s.jsx)("main",{className:"min-h-screen",children:e}),(0,s.jsx)(c.default,{})]})})})})}}};