{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductCard/productCard.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButtons\": \"productCard-module__UIKE7W__actionButtons\",\n  \"addToCartBtn\": \"productCard-module__UIKE7W__addToCartBtn\",\n  \"addToCartSection\": \"productCard-module__UIKE7W__addToCartSection\",\n  \"cartIcon\": \"productCard-module__UIKE7W__cartIcon\",\n  \"collection\": \"productCard-module__UIKE7W__collection\",\n  \"imageContainer\": \"productCard-module__UIKE7W__imageContainer\",\n  \"inStock\": \"productCard-module__UIKE7W__inStock\",\n  \"loading\": \"productCard-module__UIKE7W__loading\",\n  \"outOfStock\": \"productCard-module__UIKE7W__outOfStock\",\n  \"outOfStockOverlay\": \"productCard-module__UIKE7W__outOfStockOverlay\",\n  \"price\": \"productCard-module__UIKE7W__price\",\n  \"priceContainer\": \"productCard-module__UIKE7W__priceContainer\",\n  \"priceSection\": \"productCard-module__UIKE7W__priceSection\",\n  \"productCard\": \"productCard-module__UIKE7W__productCard\",\n  \"productDescription\": \"productCard-module__UIKE7W__productDescription\",\n  \"productImage\": \"productCard-module__UIKE7W__productImage\",\n  \"productInfo\": \"productCard-module__UIKE7W__productInfo\",\n  \"productName\": \"productCard-module__UIKE7W__productName\",\n  \"quantity\": \"productCard-module__UIKE7W__quantity\",\n  \"quantityBtn\": \"productCard-module__UIKE7W__quantityBtn\",\n  \"quantitySelector\": \"productCard-module__UIKE7W__quantitySelector\",\n  \"retailPrice\": \"productCard-module__UIKE7W__retailPrice\",\n  \"spin\": \"productCard-module__UIKE7W__spin\",\n  \"stockInfo\": \"productCard-module__UIKE7W__stockInfo\",\n  \"viewDetailsBtn\": \"productCard-module__UIKE7W__viewDetailsBtn\",\n  \"wholesaleLabel\": \"productCard-module__UIKE7W__wholesaleLabel\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductCard/ProductCard.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\n'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Product } from '@/services/types/entities';\nimport { useCart } from '@/contexts/CartContext';\nimport { useWholesaleAuth } from '@/contexts/WholesaleAuthContext';\nimport styles from './productCard.module.css';\n\ninterface ProductCardProps {\n  product: Product;\n  showAddToCart?: boolean;\n  showViewDetails?: boolean;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({\n  product,\n  showAddToCart = true,\n  showViewDetails = true,\n}) => {\n  const { addToCart, state } = useCart();\n  const { isApprovedWholesaleBuyer } = useWholesaleAuth();\n  const [isAddingToCart, setIsAddingToCart] = useState(false);\n  const [quantity, setQuantity] = useState(1);\n\n  const handleAddToCart = async () => {\n    try {\n      setIsAddingToCart(true);\n      await addToCart(product.id, quantity);\n      // You could add a toast notification here\n    } catch (error) {\n      console.error('Error adding to cart:', error);\n      // You could add error notification here\n    } finally {\n      setIsAddingToCart(false);\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  // Determine which price to display\n  const displayPrice = isApprovedWholesaleBuyer && product.wholeSalePrice\n    ? product.wholeSalePrice\n    : product.price;\n\n  const showWholesaleLabel = isApprovedWholesaleBuyer && product.wholeSalePrice;\n\n  const mainImage = product.images && product.images.length > 0 \n    ? product.images[0] \n    : '/images/placeholder-product.jpg';\n\n  const isInStock = product.stock > 0;\n\n  return (\n    <div className={styles.productCard}>\n      {/* Product Image */}\n      <div className={styles.imageContainer}>\n        <img\n          src={mainImage}\n          alt={product.name}\n          className={styles.productImage}\n        />\n        {!isInStock && (\n          <div className={styles.outOfStockOverlay}>\n            <span>Out of Stock</span>\n          </div>\n        )}\n      </div>\n\n      {/* Product Info */}\n      <div className={styles.productInfo}>\n        <h3 className={styles.productName}>{product.name}</h3>\n        \n        {product.description && (\n          <p className={styles.productDescription}>\n            {product.description.length > 100 \n              ? `${product.description.substring(0, 100)}...` \n              : product.description}\n          </p>\n        )}\n\n        <div className={styles.priceContainer}>\n          <div className={styles.priceSection}>\n            <span className={styles.price}>{formatPrice(displayPrice)}</span>\n            {showWholesaleLabel && (\n              <span className={styles.wholesaleLabel}>Wholesale Price</span>\n            )}\n            {isApprovedWholesaleBuyer && product.wholeSalePrice && (\n              <span className={styles.retailPrice}>\n                Retail: {formatPrice(product.price)}\n              </span>\n            )}\n          </div>\n          {product.collection && (\n            <span className={styles.collection}>{product.collection.name}</span>\n          )}\n        </div>\n\n        {/* Stock Info */}\n        <div className={styles.stockInfo}>\n          {isInStock ? (\n            <span className={styles.inStock}>\n              {product.stock > 10 ? 'In Stock' : `Only ${product.stock} left`}\n            </span>\n          ) : (\n            <span className={styles.outOfStock}>Out of Stock</span>\n          )}\n        </div>\n\n        {/* Action Buttons */}\n        <div className={styles.actionButtons}>\n          {showViewDetails && (\n            <Link href={`/products/${product.id}`} className={styles.viewDetailsBtn}>\n              View Details\n            </Link>\n          )}\n          \n          {showAddToCart && isInStock && (\n            <div className={styles.addToCartSection}>\n              <div className={styles.quantitySelector}>\n                <button\n                  type=\"button\"\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  className={styles.quantityBtn}\n                  disabled={quantity <= 1}\n                >\n                  -\n                </button>\n                <span className={styles.quantity}>{quantity}</span>\n                <button\n                  type=\"button\"\n                  onClick={() => setQuantity(Math.min(product.stock, quantity + 1))}\n                  className={styles.quantityBtn}\n                  disabled={quantity >= product.stock}\n                >\n                  +\n                </button>\n              </div>\n              \n              <button\n                onClick={handleAddToCart}\n                disabled={isAddingToCart || state.isLoading}\n                className={styles.addToCartBtn}\n              >\n                {isAddingToCart ? (\n                  <span className={styles.loading}>Adding...</span>\n                ) : (\n                  <>\n                    <svg className={styles.cartIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                      <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n                    </svg>\n                    Add to Cart\n                  </>\n                )}\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AACA;AAEA;AACA;AACA;AAPA;;;;;;;AAeA,MAAM,cAA0C,CAAC,EAC/C,OAAO,EACP,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACvB;IACC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnC,MAAM,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,kBAAkB;QACtB,IAAI;YACF,kBAAkB;YAClB,MAAM,UAAU,QAAQ,EAAE,EAAE;QAC5B,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,wCAAwC;QAC1C,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,mCAAmC;IACnC,MAAM,eAAe,4BAA4B,QAAQ,cAAc,GACnE,QAAQ,cAAc,GACtB,QAAQ,KAAK;IAEjB,MAAM,qBAAqB,4BAA4B,QAAQ,cAAc;IAE7E,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,QAAQ,MAAM,CAAC,EAAE,GACjB;IAEJ,MAAM,YAAY,QAAQ,KAAK,GAAG;IAElC,qBACE,8OAAC;QAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;0BAEhC,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,8OAAC;wBACC,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;oBAE/B,CAAC,2BACA,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,iBAAiB;kCACtC,cAAA,8OAAC;sCAAK;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;kCAChC,8OAAC;wBAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;kCAAG,QAAQ,IAAI;;;;;;oBAE/C,QAAQ,WAAW,kBAClB,8OAAC;wBAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,kBAAkB;kCACpC,QAAQ,WAAW,CAAC,MAAM,GAAG,MAC1B,GAAG,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC7C,QAAQ,WAAW;;;;;;kCAI3B,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;kDACjC,8OAAC;wCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,KAAK;kDAAG,YAAY;;;;;;oCAC3C,oCACC,8OAAC;wCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;kDAAE;;;;;;oCAEzC,4BAA4B,QAAQ,cAAc,kBACjD,8OAAC;wCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;4CAAE;4CAC1B,YAAY,QAAQ,KAAK;;;;;;;;;;;;;4BAIvC,QAAQ,UAAU,kBACjB,8OAAC;gCAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,QAAQ,UAAU,CAAC,IAAI;;;;;;;;;;;;kCAKhE,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,0BACC,8OAAC;4BAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sCAC5B,QAAQ,KAAK,GAAG,KAAK,aAAa,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;;;;;iDAGjE,8OAAC;4BAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;sCAAE;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,aAAa;;4BACjC,iCACC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;0CAAE;;;;;;4BAK1E,iBAAiB,2BAChB,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;;kDACrC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;;0DACrC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;gDAClD,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,YAAY;0DACvB;;;;;;0DAGD,8OAAC;gDAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;0DAAG;;;;;;0DACnC,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,QAAQ,KAAK,EAAE,WAAW;gDAC9D,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;gDAC7B,UAAU,YAAY,QAAQ,KAAK;0DACpC;;;;;;;;;;;;kDAKH,8OAAC;wCACC,SAAS;wCACT,UAAU,kBAAkB,MAAM,SAAS;wCAC3C,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;kDAE7B,+BACC,8OAAC;4CAAK,WAAW,uKAAA,CAAA,UAAM,CAAC,OAAO;sDAAE;;;;;iEAEjC;;8DACE,8OAAC;oDAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,QAAQ;oDAAE,SAAQ;oDAAY,MAAK;oDAAO,QAAO;8DACtE,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;gDACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;uCAEe", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductGrid/productGrid.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"emptyContainer\": \"productGrid-module__d3162q__emptyContainer\",\n  \"emptyIcon\": \"productGrid-module__d3162q__emptyIcon\",\n  \"emptyMessage\": \"productGrid-module__d3162q__emptyMessage\",\n  \"emptyTitle\": \"productGrid-module__d3162q__emptyTitle\",\n  \"loadingButton\": \"productGrid-module__d3162q__loadingButton\",\n  \"loadingCard\": \"productGrid-module__d3162q__loadingCard\",\n  \"loadingContainer\": \"productGrid-module__d3162q__loadingContainer\",\n  \"loadingContent\": \"productGrid-module__d3162q__loadingContent\",\n  \"loadingDescription\": \"productGrid-module__d3162q__loadingDescription\",\n  \"loadingGrid\": \"productGrid-module__d3162q__loadingGrid\",\n  \"loadingImage\": \"productGrid-module__d3162q__loadingImage\",\n  \"loadingPrice\": \"productGrid-module__d3162q__loadingPrice\",\n  \"loadingTitle\": \"productGrid-module__d3162q__loadingTitle\",\n  \"productGrid\": \"productGrid-module__d3162q__productGrid\",\n  \"pulse\": \"productGrid-module__d3162q__pulse\",\n  \"shimmer\": \"productGrid-module__d3162q__shimmer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductGrid/ProductGrid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Product } from '@/services/types/entities';\nimport ProductCard from '../ProductCard/ProductCard';\nimport styles from './productGrid.module.css';\n\ninterface ProductGridProps {\n  products: Product[];\n  isLoading?: boolean;\n  showAddToCart?: boolean;\n  showViewDetails?: boolean;\n  emptyMessage?: string;\n}\n\nconst ProductGrid: React.FC<ProductGridProps> = ({\n  products,\n  isLoading = false,\n  showAddToCart = true,\n  showViewDetails = true,\n  emptyMessage = 'No products found.',\n}) => {\n  if (isLoading) {\n    return (\n      <div className={styles.loadingContainer}>\n        <div className={styles.loadingGrid}>\n          {Array.from({ length: 6 }).map((_, index) => (\n            <div key={index} className={styles.loadingCard}>\n              <div className={styles.loadingImage}></div>\n              <div className={styles.loadingContent}>\n                <div className={styles.loadingTitle}></div>\n                <div className={styles.loadingDescription}></div>\n                <div className={styles.loadingPrice}></div>\n                <div className={styles.loadingButton}></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (products.length === 0) {\n    return (\n      <div className={styles.emptyContainer}>\n        <div className={styles.emptyIcon}>\n          <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <path d=\"M16 16s-1.5-2-4-2-4 2-4 2\"/>\n            <line x1=\"9\" y1=\"9\" x2=\"9.01\" y2=\"9\"/>\n            <line x1=\"15\" y1=\"9\" x2=\"15.01\" y2=\"9\"/>\n          </svg>\n        </div>\n        <h3 className={styles.emptyTitle}>No Products Found</h3>\n        <p className={styles.emptyMessage}>{emptyMessage}</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.productGrid}>\n      {products.map((product) => (\n        <ProductCard\n          key={product.id}\n          product={product}\n          showAddToCart={showAddToCart}\n          showViewDetails={showViewDetails}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default ProductGrid;\n"], "names": [], "mappings": ";;;;AAIA;AACA;AALA;;;;AAeA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,EACR,YAAY,KAAK,EACjB,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACtB,eAAe,oBAAoB,EACpC;IACC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,gBAAgB;sBACrC,cAAA,8OAAC;gBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;0BAC/B,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wBAAgB,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;;0CAC5C,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;0CACnC,8OAAC;gCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;kDACnC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,kBAAkB;;;;;;kDACzC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;kDACnC,8OAAC;wCAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,aAAa;;;;;;;;;;;;;uBAN9B;;;;;;;;;;;;;;;IAapB;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,cAAc;;8BACnC,8OAAC;oBAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,SAAS;8BAC9B,cAAA,8OAAC;wBAAI,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1C,8OAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,8OAAC;gCAAK,GAAE;;;;;;0CACR,8OAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAO,IAAG;;;;;;0CACjC,8OAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAQ,IAAG;;;;;;;;;;;;;;;;;8BAGvC,8OAAC;oBAAG,WAAW,uKAAA,CAAA,UAAM,CAAC,UAAU;8BAAE;;;;;;8BAClC,8OAAC;oBAAE,WAAW,uKAAA,CAAA,UAAM,CAAC,YAAY;8BAAG;;;;;;;;;;;;IAG1C;IAEA,qBACE,8OAAC;QAAI,WAAW,uKAAA,CAAA,UAAM,CAAC,WAAW;kBAC/B,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,4JAAA,CAAA,UAAW;gBAEV,SAAS;gBACT,eAAe;gBACf,iBAAiB;eAHZ,QAAQ,EAAE;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductImageGallery/productImageGallery.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"activeThumbnail\": \"productImageGallery-module__AcqG6a__activeThumbnail\",\n  \"closeZoomButton\": \"productImageGallery-module__AcqG6a__closeZoomButton\",\n  \"imageCounter\": \"productImageGallery-module__AcqG6a__imageCounter\",\n  \"imageGallery\": \"productImageGallery-module__AcqG6a__imageGallery\",\n  \"mainImage\": \"productImageGallery-module__AcqG6a__mainImage\",\n  \"mainImageContainer\": \"productImageGallery-module__AcqG6a__mainImageContainer\",\n  \"navButton\": \"productImageGallery-module__AcqG6a__navButton\",\n  \"nextButton\": \"productImageGallery-module__AcqG6a__nextButton\",\n  \"prevButton\": \"productImageGallery-module__AcqG6a__prevButton\",\n  \"thumbnail\": \"productImageGallery-module__AcqG6a__thumbnail\",\n  \"thumbnailContainer\": \"productImageGallery-module__AcqG6a__thumbnailContainer\",\n  \"thumbnailGrid\": \"productImageGallery-module__AcqG6a__thumbnailGrid\",\n  \"thumbnailImage\": \"productImageGallery-module__AcqG6a__thumbnailImage\",\n  \"zoomIndicator\": \"productImageGallery-module__AcqG6a__zoomIndicator\",\n  \"zoomOverlay\": \"productImageGallery-module__AcqG6a__zoomOverlay\",\n  \"zoomed\": \"productImageGallery-module__AcqG6a__zoomed\",\n  \"zoomedImage\": \"productImageGallery-module__AcqG6a__zoomedImage\",\n  \"zoomedImageContainer\": \"productImageGallery-module__AcqG6a__zoomedImageContainer\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductImageGallery/ProductImageGallery.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\n'use client';\n\nimport React, { useState } from 'react';\nimport styles from './productImageGallery.module.css';\n\ninterface ProductImageGalleryProps {\n  images: string[];\n  productName: string;\n}\n\nconst ProductImageGallery: React.FC<ProductImageGalleryProps> = ({ \n  images, \n  productName \n}) => {\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n  const [isZoomed, setIsZoomed] = useState(false);\n\n  // Use placeholder if no images provided\n  const galleryImages = images.length > 0 \n    ? images \n    : ['/images/placeholder-product.jpg'];\n\n  const currentImage = galleryImages[selectedImageIndex];\n\n  const handleThumbnailClick = (index: number) => {\n    setSelectedImageIndex(index);\n    setIsZoomed(false);\n  };\n\n  const handleMainImageClick = () => {\n    setIsZoomed(!isZoomed);\n  };\n\n  const handlePrevImage = () => {\n    setSelectedImageIndex((prev) => \n      prev === 0 ? galleryImages.length - 1 : prev - 1\n    );\n    setIsZoomed(false);\n  };\n\n  const handleNextImage = () => {\n    setSelectedImageIndex((prev) => \n      prev === galleryImages.length - 1 ? 0 : prev + 1\n    );\n    setIsZoomed(false);\n  };\n\n  return (\n    <div className={styles.imageGallery}>\n      {/* Main Image Display */}\n      <div className={styles.mainImageContainer}>\n        <img\n          src={currentImage}\n          alt={`${productName} - Image ${selectedImageIndex + 1}`}\n          className={`${styles.mainImage} ${isZoomed ? styles.zoomed : ''}`}\n          onClick={handleMainImageClick}\n        />\n        \n        {/* Navigation Arrows */}\n        {galleryImages.length > 1 && (\n          <>\n            <button\n              className={`${styles.navButton} ${styles.prevButton}`}\n              onClick={handlePrevImage}\n              aria-label=\"Previous image\"\n            >\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path \n                  d=\"M15 18L9 12L15 6\" \n                  stroke=\"currentColor\" \n                  strokeWidth=\"2\" \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n            </button>\n            \n            <button\n              className={`${styles.navButton} ${styles.nextButton}`}\n              onClick={handleNextImage}\n              aria-label=\"Next image\"\n            >\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path \n                  d=\"M9 18L15 12L9 6\" \n                  stroke=\"currentColor\" \n                  strokeWidth=\"2\" \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n            </button>\n          </>\n        )}\n\n        {/* Image Counter */}\n        {galleryImages.length > 1 && (\n          <div className={styles.imageCounter}>\n            {selectedImageIndex + 1} / {galleryImages.length}\n          </div>\n        )}\n\n        {/* Zoom Indicator */}\n        <div className={styles.zoomIndicator}>\n          <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n            <circle cx=\"11\" cy=\"11\" r=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n            <path d=\"M21 21L16.65 16.65\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n            <line x1=\"11\" y1=\"8\" x2=\"11\" y2=\"14\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n            <line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n          </svg>\n          Click to zoom\n        </div>\n      </div>\n\n      {/* Thumbnail Gallery */}\n      {galleryImages.length > 1 && (\n        <div className={styles.thumbnailContainer}>\n          <div className={styles.thumbnailGrid}>\n            {galleryImages.map((image, index) => (\n              <button\n                key={index}\n                className={`${styles.thumbnail} ${\n                  index === selectedImageIndex ? styles.activeThumbnail : ''\n                }`}\n                onClick={() => handleThumbnailClick(index)}\n                aria-label={`View image ${index + 1}`}\n              >\n                <img\n                  src={image}\n                  alt={`${productName} thumbnail ${index + 1}`}\n                  className={styles.thumbnailImage}\n                />\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Zoom Overlay */}\n      {isZoomed && (\n        <div className={styles.zoomOverlay} onClick={() => setIsZoomed(false)}>\n          <div className={styles.zoomedImageContainer}>\n            <img\n              src={currentImage}\n              alt={`${productName} - Zoomed view`}\n              className={styles.zoomedImage}\n            />\n            <button\n              className={styles.closeZoomButton}\n              onClick={() => setIsZoomed(false)}\n              aria-label=\"Close zoom view\"\n            >\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ProductImageGallery;\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AACA;AAHA;;;;AAUA,MAAM,sBAA0D,CAAC,EAC/D,MAAM,EACN,WAAW,EACZ;IACC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wCAAwC;IACxC,MAAM,gBAAgB,OAAO,MAAM,GAAG,IAClC,SACA;QAAC;KAAkC;IAEvC,MAAM,eAAe,aAAa,CAAC,mBAAmB;IAEtD,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,YAAY;IACd;IAEA,MAAM,uBAAuB;QAC3B,YAAY,CAAC;IACf;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OACrB,SAAS,IAAI,cAAc,MAAM,GAAG,IAAI,OAAO;QAEjD,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OACrB,SAAS,cAAc,MAAM,GAAG,IAAI,IAAI,OAAO;QAEjD,YAAY;IACd;IAEA,qBACE,8OAAC;QAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,YAAY;;0BAEjC,8OAAC;gBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,kBAAkB;;kCACvC,8OAAC;wBACC,KAAK;wBACL,KAAK,GAAG,YAAY,SAAS,EAAE,qBAAqB,GAAG;wBACvD,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,WAAW,uLAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAAI;wBACjE,SAAS;;;;;;oBAIV,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC;gCACC,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,uLAAA,CAAA,UAAM,CAAC,UAAU,EAAE;gCACrD,SAAS;gCACT,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCACC,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAAE,uLAAA,CAAA,UAAM,CAAC,UAAU,EAAE;gCACrD,SAAS;gCACT,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;oBAQxB,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,YAAY;;4BAChC,qBAAqB;4BAAE;4BAAI,cAAc,MAAM;;;;;;;kCAKpD,8OAAC;wBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;;kDACnD,8OAAC;wCAAO,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAI,QAAO;wCAAe,aAAY;;;;;;kDAChE,8OAAC;wCAAK,GAAE;wCAAqB,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACjF,8OAAC;wCAAK,IAAG;wCAAK,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACzF,8OAAC;wCAAK,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;;;;;;;4BACrF;;;;;;;;;;;;;YAMT,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,kBAAkB;0BACvC,cAAA,8OAAC;oBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,aAAa;8BACjC,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;4BAEC,WAAW,GAAG,uLAAA,CAAA,UAAM,CAAC,SAAS,CAAC,CAAC,EAC9B,UAAU,qBAAqB,uLAAA,CAAA,UAAM,CAAC,eAAe,GAAG,IACxD;4BACF,SAAS,IAAM,qBAAqB;4BACpC,cAAY,CAAC,WAAW,EAAE,QAAQ,GAAG;sCAErC,cAAA,8OAAC;gCACC,KAAK;gCACL,KAAK,GAAG,YAAY,WAAW,EAAE,QAAQ,GAAG;gCAC5C,WAAW,uLAAA,CAAA,UAAM,CAAC,cAAc;;;;;;2BAV7B;;;;;;;;;;;;;;;YAmBd,0BACC,8OAAC;gBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,WAAW;gBAAE,SAAS,IAAM,YAAY;0BAC7D,cAAA,8OAAC;oBAAI,WAAW,uLAAA,CAAA,UAAM,CAAC,oBAAoB;;sCACzC,8OAAC;4BACC,KAAK;4BACL,KAAK,GAAG,YAAY,cAAc,CAAC;4BACnC,WAAW,uLAAA,CAAA,UAAM,CAAC,WAAW;;;;;;sCAE/B,8OAAC;4BACC,WAAW,uLAAA,CAAA,UAAM,CAAC,eAAe;4BACjC,SAAS,IAAM,YAAY;4BAC3B,cAAW;sCAEX,cAAA,8OAAC;gCAAI,OAAM;gCAAK,QAAO;gCAAK,SAAQ;gCAAY,MAAK;;kDACnD,8OAAC;wCAAK,IAAG;wCAAK,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;kDACxF,8OAAC;wCAAK,IAAG;wCAAI,IAAG;wCAAI,IAAG;wCAAK,IAAG;wCAAK,QAAO;wCAAe,aAAY;wCAAI,eAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG;uCAEe", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/ProductSpecifications/productSpecifications.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"productSpecifications-module__J4NrIa__active\",\n  \"careContent\": \"productSpecifications-module__J4NrIa__careContent\",\n  \"careSection\": \"productSpecifications-module__J4NrIa__careSection\",\n  \"description\": \"productSpecifications-module__J4NrIa__description\",\n  \"detailsContent\": \"productSpecifications-module__J4NrIa__detailsContent\",\n  \"downloadLink\": \"productSpecifications-module__J4NrIa__downloadLink\",\n  \"downloadLinks\": \"productSpecifications-module__J4NrIa__downloadLinks\",\n  \"downloadSection\": \"productSpecifications-module__J4NrIa__downloadSection\",\n  \"featureList\": \"productSpecifications-module__J4NrIa__featureList\",\n  \"inStock\": \"productSpecifications-module__J4NrIa__inStock\",\n  \"keySpecsTable\": \"productSpecifications-module__J4NrIa__keySpecsTable\",\n  \"outOfStock\": \"productSpecifications-module__J4NrIa__outOfStock\",\n  \"section\": \"productSpecifications-module__J4NrIa__section\",\n  \"sectionContent\": \"productSpecifications-module__J4NrIa__sectionContent\",\n  \"sectionHeader\": \"productSpecifications-module__J4NrIa__sectionHeader\",\n  \"shareButton\": \"productSpecifications-module__J4NrIa__shareButton\",\n  \"shareButtons\": \"productSpecifications-module__J4NrIa__shareButtons\",\n  \"shareLabel\": \"productSpecifications-module__J4NrIa__shareLabel\",\n  \"shareSection\": \"productSpecifications-module__J4NrIa__shareSection\",\n  \"specGrid\": \"productSpecifications-module__J4NrIa__specGrid\",\n  \"specLabel\": \"productSpecifications-module__J4NrIa__specLabel\",\n  \"specRow\": \"productSpecifications-module__J4NrIa__specRow\",\n  \"specValue\": \"productSpecifications-module__J4NrIa__specValue\",\n  \"specificationsContainer\": \"productSpecifications-module__J4NrIa__specificationsContainer\",\n  \"tag\": \"productSpecifications-module__J4NrIa__tag\",\n  \"tagContainer\": \"productSpecifications-module__J4NrIa__tagContainer\",\n  \"toggleIcon\": \"productSpecifications-module__J4NrIa__toggleIcon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductSpecifications/ProductSpecifications.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { Product } from '@/services/types/entities';\r\nimport styles from './productSpecifications.module.css';\r\n\r\ninterface ProductSpecificationsProps {\r\n  product: Product;\r\n}\r\n\r\nconst ProductSpecifications: React.FC<ProductSpecificationsProps> = ({ product }) => {\r\n  const [activeSection, setActiveSection] = useState<string | null>('specifications');\r\n\r\n  const toggleSection = (section: string) => {\r\n    setActiveSection(activeSection === section ? null : section);\r\n  };\r\n\r\nconst hasSpecifications = product.productSpecifications &&\r\n  Object.values(product.productSpecifications).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\nconst hasDetails = product.productDetails &&\r\n  Object.values(product.productDetails).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\nconst hasDownloadableContent = product.downloadableContent &&\r\n  Object.values(product.downloadableContent).some(\r\n    value => typeof value === 'string' ? value.trim() !== '' : value !== null && value !== undefined\r\n  );\r\n\r\n\r\n  return (\r\n    <div className={styles.specificationsContainer}>\r\n      {/* Product Specifications Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'specifications' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('specifications')}\r\n        >\r\n          <span>Product Specifications</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'specifications' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'specifications' && hasSpecifications && (\r\n\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.specGrid}>\r\n              {/* Availability - Show stock status */}\r\n              <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Availability:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.stock > 0\r\n                    ? `Limited Inventory. (Ships within 13 weeks of order placement.)`\r\n                    : 'Out of Stock'\r\n                  }\r\n                </span>\r\n              </div>\r\n\r\n               {product.productSpecifications?.pieces && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Pieces:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.pieces}</span>\r\n                </div>\r\n              )} \r\n\r\n              {product.productSpecifications?.material && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Material:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.material}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.dimensions && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Dimensions:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.dimensions}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.totalWeight && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Total Weight:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.totalWeight}</span>\r\n                </div>\r\n              )}\r\n\r\n              {product.productSpecifications?.weightWithWater && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Weight With Water:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.weightWithWater}</span>\r\n                </div>\r\n              )}\r\n{/* ---- -------------------------------------------------------------------------------------------------- */}\r\n\r\n               {product.productSpecifications?.base_Dimensions && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Base Dimensions:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.base_Dimensions}</span>\r\n                </div>\r\n              )} \r\n\r\n               {product.productSpecifications?.photographed_In && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Photographed In:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.photographed_In}</span>\r\n                </div>\r\n              )} \r\n\r\n\r\n              {product.productSpecifications?.waterVolume && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Water Volume:</span>\r\n                  <span className={styles.specValue}>{product.productSpecifications.waterVolume}</span>\r\n                </div>\r\n              )}\r\n\r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Collection:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.collection?.name || 'Not specified'}\r\n                </span>\r\n              </div> */}\r\n\r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Product Code:</span>\r\n                <span className={styles.specValue}>\r\n                  {product.productCode || `P-${product.id.toString().padStart(3, '0')}-AS`}\r\n                </span>\r\n              </div> */}\r\n              \r\n              {/* <div className={styles.specRow}>\r\n                <span className={styles.specLabel}>Stock Status:</span>\r\n                <span className={`${styles.specValue} ${\r\n                  product.stock > 0 ? styles.inStock : styles.outOfStock\r\n                }`}>\r\n                  {product.stock > 0 ? `In Stock (${product.stock} available)` : 'Out of Stock'}\r\n                </span>\r\n              </div> */}\r\n              \r\n              {product.tags && product.tags.length > 0 && (\r\n                <div className={styles.specRow}>\r\n                  <span className={styles.specLabel}>Tags:</span>\r\n                  <span className={styles.specValue}>\r\n                    <div className={styles.tagContainer}>\r\n                      {product.tags.map((tag, index) => (\r\n                        <span key={index} className={styles.tag}>\r\n                          {tag}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Details Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'details' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('details')}\r\n        >\r\n          <span>Product Details</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'details' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'details' && (\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.detailsContent}>\r\n              {product.description && (\r\n                <p className={styles.description}>{product.description}</p>\r\n              )}\r\n\r\n              {hasDetails && (\r\n                <div className={styles.specGrid}>\r\n                  {product.productDetails?.upc && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>UPC:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.upc}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.indoorUseOnly && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Indoor Use Only:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.indoorUseOnly}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.assemblyRequired && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Assembly Required:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.assemblyRequired}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.easeOfAssembly && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Ease of Assembly:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.easeOfAssembly}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.assistanceRequired && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Assistance Required:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.assistanceRequired}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.splashLevel && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Splash Level:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.splashLevel}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.soundLevel && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Sound Level:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.soundLevel}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.soundType && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Sound Type:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.soundType}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.replacementPumpKit && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Replacement Pump Kit:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.replacementPumpKit}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.electricalCordLength && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Electrical Cord Length:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.electricalCordLength}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.pumpSize && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Pump Size:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.pumpSize}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.shipMethod && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Ship Method:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.shipMethod}</span>\r\n                    </div>\r\n                  )}\r\n                  {product.productDetails?.drainage_Info && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Drainage Info:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.drainage_Info}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Top && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Top:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Top}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Bottom && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Bottom:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Bottom}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.inside_Height && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Inside Height:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.inside_Height}</span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {product.productDetails?.factory_Code && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Factory Code:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.factory_Code}</span>\r\n                    </div>\r\n                  )}\r\n                  {product.productDetails?.catalogPage && (\r\n                    <div className={styles.specRow}>\r\n                      <span className={styles.specLabel}>Catalog Page:</span>\r\n                      <span className={styles.specValue}>{product.productDetails.catalogPage}</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Care Section */}\r\n      <div className={styles.section}>\r\n        <button\r\n          className={`${styles.sectionHeader} ${\r\n            activeSection === 'care' ? styles.active : ''\r\n          }`}\r\n          onClick={() => toggleSection('care')}\r\n        >\r\n          <span>Product Care and Downloadable Content</span>\r\n          <span className={styles.toggleIcon}>\r\n            {activeSection === 'care' ? '−' : '+'}\r\n          </span>\r\n        </button>\r\n        \r\n        {activeSection === 'care' && (\r\n          <div className={styles.sectionContent}>\r\n            <div className={styles.careContent}>\r\n              {hasDownloadableContent && (\r\n                <div className={styles.downloadSection}>\r\n                  <h4>Downloadable Content:</h4>\r\n                  <div className={styles.downloadLinks}>\r\n                    {product.downloadableContent?.care && (\r\n                      <a\r\n                        href={product.downloadableContent.care}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📄 Care Instructions\r\n                      </a>\r\n                    )}\r\n\r\n                    {product.downloadableContent?.productInstructions && (\r\n                      <a\r\n                        href={product.downloadableContent.productInstructions}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📋 Product Instructions\r\n                      </a>\r\n                    )}\r\n\r\n                    {product.downloadableContent?.cad && (\r\n                      <a\r\n                        href={product.downloadableContent.cad}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className={styles.downloadLink}\r\n                      >\r\n                        📐 CAD Files\r\n                      </a>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}              \r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Share Section */}\r\n      <div className={styles.shareSection}>\r\n        <span className={styles.shareLabel}>Share</span>\r\n        <div className={styles.shareButtons}>\r\n          <button className={styles.shareButton} aria-label=\"Share on Facebook\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n              <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\r\n            </svg>\r\n          </button>\r\n          \r\n          <button className={styles.shareButton} aria-label=\"Share on Pinterest\">\r\n            <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n              <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-12.014C24.007 5.36 18.641.001 12.017.001z\"/>\r\n            </svg>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductSpecifications;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAUA,MAAM,wBAA8D,CAAC,EAAE,OAAO,EAAE;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,gBAAgB,CAAC;QACrB,iBAAiB,kBAAkB,UAAU,OAAO;IACtD;IAEF,MAAM,oBAAoB,QAAQ,qBAAqB,IACrD,OAAO,MAAM,CAAC,QAAQ,qBAAqB,EAAE,IAAI,CAC/C,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAG3F,MAAM,aAAa,QAAQ,cAAc,IACvC,OAAO,MAAM,CAAC,QAAQ,cAAc,EAAE,IAAI,CACxC,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAG3F,MAAM,yBAAyB,QAAQ,mBAAmB,IACxD,OAAO,MAAM,CAAC,QAAQ,mBAAmB,EAAE,IAAI,CAC7C,CAAA,QAAS,OAAO,UAAU,WAAW,MAAM,IAAI,OAAO,KAAK,UAAU,QAAQ,UAAU;IAIzF,qBACE,8OAAC;QAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,uBAAuB;;0BAE5C,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBACC,WAAW,GAAG,2LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,mBAAmB,2LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IACrD;wBACF,SAAS,IAAM,cAAc;;0CAE7B,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,mBAAmB,MAAM;;;;;;;;;;;;oBAI/C,kBAAkB,oBAAoB,mCAErC,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,QAAQ;;8CAE7B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAC9B,QAAQ,KAAK,GAAG,IACb,CAAC,8DAA8D,CAAC,GAChE;;;;;;;;;;;;gCAKN,QAAQ,qBAAqB,EAAE,wBAC/B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,MAAM;;;;;;;;;;;;gCAI3E,QAAQ,qBAAqB,EAAE,0BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,QAAQ;;;;;;;;;;;;gCAI7E,QAAQ,qBAAqB,EAAE,4BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,UAAU;;;;;;;;;;;;gCAI/E,QAAQ,qBAAqB,EAAE,6BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gCAIhF,QAAQ,qBAAqB,EAAE,iCAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;gCAKnF,QAAQ,qBAAqB,EAAE,iCAC/B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;gCAInF,QAAQ,qBAAqB,EAAE,iCAC/B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,eAAe;;;;;;;;;;;;gCAKpF,QAAQ,qBAAqB,EAAE,6BAC9B,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAG,QAAQ,qBAAqB,CAAC,WAAW;;;;;;;;;;;;gCA2BhF,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;sDAC5B,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAAE;;;;;;sDACnC,8OAAC;4CAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;sDAC/B,cAAA,8OAAC;gDAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAChC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;wDAAiB,WAAW,2LAAA,CAAA,UAAM,CAAC,GAAG;kEACpC;uDADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc7B,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBACC,WAAW,GAAG,2LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,YAAY,2LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC9C;wBACF,SAAS,IAAM,cAAc;;0CAE7B,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,YAAY,MAAM;;;;;;;;;;;;oBAIxC,kBAAkB,2BACjB,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;;gCAClC,QAAQ,WAAW,kBAClB,8OAAC;oCAAE,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;8CAAG,QAAQ,WAAW;;;;;;gCAGvD,4BACC,8OAAC;oCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,QAAQ;;wCAC5B,QAAQ,cAAc,EAAE,qBACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,GAAG;;;;;;;;;;;;wCAIjE,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,kCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,gBAAgB;;;;;;;;;;;;wCAI9E,QAAQ,cAAc,EAAE,gCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,cAAc;;;;;;;;;;;;wCAI5E,QAAQ,cAAc,EAAE,oCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,kBAAkB;;;;;;;;;;;;wCAIhF,QAAQ,cAAc,EAAE,6BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,WAAW;;;;;;;;;;;;wCAIzE,QAAQ,cAAc,EAAE,4BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAIxE,QAAQ,cAAc,EAAE,2BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,SAAS;;;;;;;;;;;;wCAIvE,QAAQ,cAAc,EAAE,oCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,kBAAkB;;;;;;;;;;;;wCAIhF,QAAQ,cAAc,EAAE,sCACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,oBAAoB;;;;;;;;;;;;wCAIlF,QAAQ,cAAc,EAAE,0BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,QAAQ;;;;;;;;;;;;wCAItE,QAAQ,cAAc,EAAE,4BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAGxE,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,4BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,UAAU;;;;;;;;;;;;wCAIxE,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,+BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,aAAa;;;;;;;;;;;;wCAI3E,QAAQ,cAAc,EAAE,8BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,YAAY;;;;;;;;;;;;wCAG1E,QAAQ,cAAc,EAAE,6BACvB,8OAAC;4CAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;8DAC5B,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAE;;;;;;8DACnC,8OAAC;oDAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,SAAS;8DAAG,QAAQ,cAAc,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtF,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBACC,WAAW,GAAG,2LAAA,CAAA,UAAM,CAAC,aAAa,CAAC,CAAC,EAClC,kBAAkB,SAAS,2LAAA,CAAA,UAAM,CAAC,MAAM,GAAG,IAC3C;wBACF,SAAS,IAAM,cAAc;;0CAE7B,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,kBAAkB,SAAS,MAAM;;;;;;;;;;;;oBAIrC,kBAAkB,wBACjB,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC;4BAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;sCAC/B,wCACC,8OAAC;gCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,eAAe;;kDACpC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,aAAa;;4CACjC,QAAQ,mBAAmB,EAAE,sBAC5B,8OAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,IAAI;gDACtC,QAAO;gDACP,KAAI;gDACJ,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;4CAKF,QAAQ,mBAAmB,EAAE,qCAC5B,8OAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,mBAAmB;gDACrD,QAAO;gDACP,KAAI;gDACJ,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;4CAKF,QAAQ,mBAAmB,EAAE,qBAC5B,8OAAC;gDACC,MAAM,QAAQ,mBAAmB,CAAC,GAAG;gDACrC,QAAO;gDACP,KAAI;gDACJ,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;0DAC/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajB,8OAAC;gBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,8OAAC;wBAAK,WAAW,2LAAA,CAAA,UAAM,CAAC,UAAU;kCAAE;;;;;;kCACpC,8OAAC;wBAAI,WAAW,2LAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,8OAAC;gCAAO,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;gCAAE,cAAW;0CAChD,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAIZ,8OAAC;gCAAO,WAAW,2LAAA,CAAA,UAAM,CAAC,WAAW;gCAAE,cAAW;0CAChD,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;uCAEe", "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/RelatedProducts/relatedProducts.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"availability\": \"relatedProducts-module__zj5_XW__availability\",\n  \"disabled\": \"relatedProducts-module__zj5_XW__disabled\",\n  \"imageContainer\": \"relatedProducts-module__zj5_XW__imageContainer\",\n  \"inStock\": \"relatedProducts-module__zj5_XW__inStock\",\n  \"outOfStock\": \"relatedProducts-module__zj5_XW__outOfStock\",\n  \"outOfStockOverlay\": \"relatedProducts-module__zj5_XW__outOfStockOverlay\",\n  \"price\": \"relatedProducts-module__zj5_XW__price\",\n  \"priceContainer\": \"relatedProducts-module__zj5_XW__priceContainer\",\n  \"productCard\": \"relatedProducts-module__zj5_XW__productCard\",\n  \"productCode\": \"relatedProducts-module__zj5_XW__productCode\",\n  \"productDetails\": \"relatedProducts-module__zj5_XW__productDetails\",\n  \"productImage\": \"relatedProducts-module__zj5_XW__productImage\",\n  \"productInfo\": \"relatedProducts-module__zj5_XW__productInfo\",\n  \"productLink\": \"relatedProducts-module__zj5_XW__productLink\",\n  \"productName\": \"relatedProducts-module__zj5_XW__productName\",\n  \"productsContainer\": \"relatedProducts-module__zj5_XW__productsContainer\",\n  \"productsGrid\": \"relatedProducts-module__zj5_XW__productsGrid\",\n  \"relatedProducts\": \"relatedProducts-module__zj5_XW__relatedProducts\",\n  \"scrollButton\": \"relatedProducts-module__zj5_XW__scrollButton\",\n  \"scrollControls\": \"relatedProducts-module__zj5_XW__scrollControls\",\n  \"sectionHeader\": \"relatedProducts-module__zj5_XW__sectionHeader\",\n  \"sectionTitle\": \"relatedProducts-module__zj5_XW__sectionTitle\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/RelatedProducts/RelatedProducts.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @next/next/no-img-element */\n'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Product } from '@/services/types/entities';\nimport styles from './relatedProducts.module.css';\n\ninterface RelatedProductsProps {\n  products: Product[];\n}\n\nconst RelatedProducts: React.FC<RelatedProductsProps> = ({ products }) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  const updateScrollButtons = () => {\n    if (scrollContainerRef.current) {\n      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;\n      setCanScrollLeft(scrollLeft > 0);\n      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);\n    }\n  };\n\n  const scrollLeft = () => {\n    if (scrollContainerRef.current) {\n      const cardWidth = 320; // Card width + gap\n      scrollContainerRef.current.scrollBy({ left: -cardWidth, behavior: 'smooth' });\n    }\n  };\n\n  const scrollRight = () => {\n    if (scrollContainerRef.current) {\n      const cardWidth = 320; // Card width + gap\n      scrollContainerRef.current.scrollBy({ left: cardWidth, behavior: 'smooth' });\n    }\n  };\n\n  useEffect(() => {\n    const scrollContainer = scrollContainerRef.current;\n    if (scrollContainer) {\n      updateScrollButtons();\n      scrollContainer.addEventListener('scroll', updateScrollButtons);\n      return () => scrollContainer.removeEventListener('scroll', updateScrollButtons);\n    }\n  }, [products]);\n\n  if (!products || products.length === 0) {\n    return null;\n  }\n\n  return (\n    <div className={styles.relatedProducts}>\n      <div className={styles.sectionHeader}>\n        <h2 className={styles.sectionTitle}>You May Also Like</h2>\n        \n        {products.length > 3 && (\n          <div className={styles.scrollControls}>\n            <button\n              className={`${styles.scrollButton} ${!canScrollLeft ? styles.disabled : ''}`}\n              onClick={scrollLeft}\n              disabled={!canScrollLeft}\n              aria-label=\"Scroll left\"\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path \n                  d=\"M15 18L9 12L15 6\" \n                  stroke=\"currentColor\" \n                  strokeWidth=\"2\" \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n            </button>\n            \n            <button\n              className={`${styles.scrollButton} ${!canScrollRight ? styles.disabled : ''}`}\n              onClick={scrollRight}\n              disabled={!canScrollRight}\n              aria-label=\"Scroll right\"\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path \n                  d=\"M9 18L15 12L9 6\" \n                  stroke=\"currentColor\" \n                  strokeWidth=\"2\" \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n            </button>\n          </div>\n        )}\n      </div>\n\n      <div \n        className={styles.productsContainer}\n        ref={scrollContainerRef}\n      >\n        <div className={styles.productsGrid}>\n          {products.map((product) => {\n            const mainImage = product.images && product.images.length > 0 \n              ? product.images[0] \n              : '/images/placeholder-product.jpg';\n            \n            const isInStock = product.stock > 0;\n\n            return (\n              <div key={product.id} className={styles.productCard}>\n                <Link href={`/products/${product.id}`} className={styles.productLink}>\n                  <div className={styles.imageContainer}>\n                    <img\n                      src={mainImage}\n                      alt={product.name}\n                      className={styles.productImage}\n                    />\n                    {!isInStock && (\n                      <div className={styles.outOfStockOverlay}>\n                        <span>Out of Stock</span>\n                      </div>\n                    )}\n                  </div>\n                  \n                  <div className={styles.productInfo}>\n                    <h3 className={styles.productName}>{product.name}</h3>\n                    \n                    <div className={styles.productDetails}>\n                      <span className={styles.productCode}>\n                        P-{product.id.toString().padStart(3, '0')}-AS\n                      </span>\n                      \n                      <div className={styles.availability}>\n                        {isInStock ? (\n                          <span className={styles.inStock}>\n                            Available in 14 Colors And 3 sizes\n                          </span>\n                        ) : (\n                          <span className={styles.outOfStock}>\n                            Currently Out of Stock\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                    \n                    <div className={styles.priceContainer}>\n                      <span className={styles.price}>{formatPrice(product.price)}</span>\n                    </div>\n                  </div>\n                </Link>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RelatedProducts;\n"], "names": [], "mappings": "AAAA,oDAAoD,GACpD,4CAA4C;;;;AAG5C;AACA;AAEA;AALA;;;;;AAWA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAElD,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,sBAAsB;QAC1B,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,mBAAmB,OAAO;YAC3E,iBAAiB,aAAa;YAC9B,kBAAkB,aAAa,cAAc,cAAc;QAC7D;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,YAAY,KAAK,mBAAmB;YAC1C,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM,CAAC;gBAAW,UAAU;YAAS;QAC7E;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,MAAM,YAAY,KAAK,mBAAmB;YAC1C,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAW,UAAU;YAAS;QAC5E;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,mBAAmB,OAAO;QAClD,IAAI,iBAAiB;YACnB;YACA,gBAAgB,gBAAgB,CAAC,UAAU;YAC3C,OAAO,IAAM,gBAAgB,mBAAmB,CAAC,UAAU;QAC7D;IACF,GAAG;QAAC;KAAS;IAEb,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,eAAe;;0BACpC,8OAAC;gBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,aAAa;;kCAClC,8OAAC;wBAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;kCAAE;;;;;;oBAEnC,SAAS,MAAM,GAAG,mBACjB,8OAAC;wBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,8OAAC;gCACC,WAAW,GAAG,+KAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,gBAAgB,+KAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;gCAC5E,SAAS;gCACT,UAAU,CAAC;gCACX,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;0CAKrB,8OAAC;gCACC,WAAW,GAAG,+KAAA,CAAA,UAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,iBAAiB,+KAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,IAAI;gCAC7E,SAAS;gCACT,UAAU,CAAC;gCACX,cAAW;0CAEX,cAAA,8OAAC;oCAAI,OAAM;oCAAK,QAAO;oCAAK,SAAQ;oCAAY,MAAK;8CACnD,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,8OAAC;gBACC,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;gBACnC,KAAK;0BAEL,cAAA,8OAAC;oBAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;8BAChC,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,YAAY,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IACxD,QAAQ,MAAM,CAAC,EAAE,GACjB;wBAEJ,MAAM,YAAY,QAAQ,KAAK,GAAG;wBAElC,qBACE,8OAAC;4BAAqB,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;sCACjD,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gCAAE,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;kDAClE,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;0DACnC,8OAAC;gDACC,KAAK;gDACL,KAAK,QAAQ,IAAI;gDACjB,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;;;;;;4CAE/B,CAAC,2BACA,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,iBAAiB;0DACtC,cAAA,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;0DAChC,8OAAC;gDAAG,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;0DAAG,QAAQ,IAAI;;;;;;0DAEhD,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;;kEACnC,8OAAC;wDAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,WAAW;;4DAAE;4DAChC,QAAQ,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG;4DAAK;;;;;;;kEAG5C,8OAAC;wDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,YAAY;kEAChC,0BACC,8OAAC;4DAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,OAAO;sEAAE;;;;;iFAIjC,8OAAC;4DAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,UAAU;sEAAE;;;;;;;;;;;;;;;;;0DAO1C,8OAAC;gDAAI,WAAW,+KAAA,CAAA,UAAM,CAAC,cAAc;0DACnC,cAAA,8OAAC;oDAAK,WAAW,+KAAA,CAAA,UAAM,CAAC,KAAK;8DAAG,YAAY,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;2BArCvD,QAAQ,EAAE;;;;;oBA2CxB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/index.ts"], "sourcesContent": ["// Product Components\r\nexport { default as ProductCard } from './ProductCard/ProductCard';\r\nexport { default as ProductGrid } from './ProductGrid/ProductGrid';\r\nexport { default as ProductImageGallery } from './ProductImageGallery/ProductImageGallery';\r\nexport { default as ProductSpecifications } from './ProductSpecifications/ProductSpecifications';\r\n// export { default as PatinaSelector } from './PatinaSelector/PatinaSelector';\r\nexport { default as RelatedProducts } from './RelatedProducts/RelatedProducts';\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;AACrB;AACA;AACA;AACA;AACA,+EAA+E;AAC/E", "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/products/products.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"checkboxLabel\": \"products-module__E8alaG__checkboxLabel\",\n  \"clearFilters\": \"products-module__E8alaG__clearFilters\",\n  \"container\": \"products-module__E8alaG__container\",\n  \"content\": \"products-module__E8alaG__content\",\n  \"filterGroup\": \"products-module__E8alaG__filterGroup\",\n  \"filterIcon\": \"products-module__E8alaG__filterIcon\",\n  \"filterSelect\": \"products-module__E8alaG__filterSelect\",\n  \"filterToggle\": \"products-module__E8alaG__filterToggle\",\n  \"filtersHeader\": \"products-module__E8alaG__filtersHeader\",\n  \"filtersSidebar\": \"products-module__E8alaG__filtersSidebar\",\n  \"header\": \"products-module__E8alaG__header\",\n  \"headerActions\": \"products-module__E8alaG__headerActions\",\n  \"headerContent\": \"products-module__E8alaG__headerContent\",\n  \"priceInput\": \"products-module__E8alaG__priceInput\",\n  \"priceRange\": \"products-module__E8alaG__priceRange\",\n  \"productsSection\": \"products-module__E8alaG__productsSection\",\n  \"resultsCount\": \"products-module__E8alaG__resultsCount\",\n  \"searchInput\": \"products-module__E8alaG__searchInput\",\n  \"showFilters\": \"products-module__E8alaG__showFilters\",\n  \"subtitle\": \"products-module__E8alaG__subtitle\",\n  \"title\": \"products-module__E8alaG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/products/page.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/* eslint-disable react-hooks/exhaustive-deps */\r\n'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Product, Collection } from '@/services/types/entities';\r\nimport { productService, collectionService } from '@/services';\r\nimport { ProductGrid } from '@/components/products';\r\nimport styles from './products.module.css';\r\n\r\ninterface FilterState {\r\n  search: string;\r\n  collectionId: number | '';\r\n  priceRange: {\r\n    min: number;\r\n    max: number;\r\n  };\r\n  inStockOnly: boolean;\r\n  sortBy: 'name' | 'price' | 'newest';\r\n  sortDirection: 'asc' | 'desc';\r\n}\r\n\r\nexport default function ProductsPage() {\r\n  const [products, setProducts] = useState<Product[]>([]);\r\n  const [collections, setCollections] = useState<Collection[]>([]);\r\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [showFilters, setShowFilters] = useState(false);\r\n  \r\n  const [filters, setFilters] = useState<FilterState>({\r\n    search: '',\r\n    collectionId: '',\r\n    priceRange: { min: 0, max: 10000 },\r\n    inStockOnly: false,\r\n    sortBy: 'name',\r\n    sortDirection: 'asc'\r\n  });\r\n\r\n  // Fetch data on component mount\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  // Apply filters when products or filters change\r\n  useEffect(() => {\r\n    applyFilters();\r\n  }, [products, filters]);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const [productsData, collectionsData] = await Promise.all([\r\n        productService.get.getAll(),\r\n        collectionService.get.getAll(),\r\n      ]);\r\n      setProducts(productsData);\r\n      setCollections(collectionsData);\r\n    } catch (error) {\r\n      console.error('Error fetching data:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const applyFilters = () => {\r\n    let filtered = [...products];\r\n\r\n    // Search filter\r\n    if (filters.search) {\r\n      const searchLower = filters.search.toLowerCase();\r\n      filtered = filtered.filter(product =>\r\n        product.name.toLowerCase().includes(searchLower) ||\r\n        product.description?.toLowerCase().includes(searchLower) ||\r\n        (product.tags && Array.isArray(product.tags) && product.tags.some(tag =>\r\n          typeof tag === 'string' && tag.toLowerCase().includes(searchLower)\r\n        ))\r\n      );\r\n    }\r\n\r\n    // Collection filter\r\n    if (filters.collectionId) {\r\n      filtered = filtered.filter(product => product.collectionId === filters.collectionId);\r\n    }\r\n\r\n    // Price range filter\r\n    filtered = filtered.filter(product =>\r\n      product.price >= filters.priceRange.min && product.price <= filters.priceRange.max\r\n    );\r\n\r\n    // Stock filter\r\n    if (filters.inStockOnly) {\r\n      filtered = filtered.filter(product => product.stock > 0);\r\n    }\r\n\r\n    // Sorting\r\n    filtered.sort((a, b) => {\r\n      let comparison = 0;\r\n      \r\n      switch (filters.sortBy) {\r\n        case 'name':\r\n          comparison = a.name.localeCompare(b.name);\r\n          break;\r\n        case 'price':\r\n          comparison = a.price - b.price;\r\n          break;\r\n        case 'newest':\r\n          comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\r\n          break;\r\n      }\r\n      \r\n      return filters.sortDirection === 'desc' ? -comparison : comparison;\r\n    });\r\n\r\n    setFilteredProducts(filtered);\r\n  };\r\n\r\n  const handleFilterChange = (key: keyof FilterState, value: any) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [key]: value\r\n    }));\r\n  };\r\n\r\n  const handlePriceRangeChange = (min: number, max: number) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      priceRange: { min, max }\r\n    }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({\r\n      search: '',\r\n      collectionId: '',\r\n      priceRange: { min: 0, max: 10000 },\r\n      inStockOnly: false,\r\n      sortBy: 'name',\r\n      sortDirection: 'asc'\r\n    });\r\n  };\r\n\r\n  const formatPrice = (price: number) => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n    }).format(price);\r\n  };\r\n\r\n  return (\r\n    <div className={styles.container}>\r\n      {/* Header */}\r\n      <div className={styles.header}>\r\n        <div className={styles.headerContent}>\r\n          <h1 className={styles.title}>Our Products</h1>\r\n          <p className={styles.subtitle}>\r\n            Discover our exquisite collection of handcrafted cast stone pieces\r\n          </p>\r\n        </div>\r\n        \r\n        <div className={styles.headerActions}>\r\n          <button\r\n            onClick={() => setShowFilters(!showFilters)}\r\n            className={styles.filterToggle}\r\n          >\r\n            <svg className={styles.filterIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <polygon points=\"22,3 2,3 10,12.46 10,19 14,21 14,12.46\"/>\r\n            </svg>\r\n            Filters\r\n          </button>\r\n          \r\n          <div className={styles.resultsCount}>\r\n            {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className={styles.content}>\r\n        {/* Filters Sidebar */}\r\n        <div className={`${styles.filtersSidebar} ${showFilters ? styles.showFilters : ''}`}>\r\n          <div className={styles.filtersHeader}>\r\n            <h3>Filters</h3>\r\n            <button onClick={clearFilters} className={styles.clearFilters}>\r\n              Clear All\r\n            </button>\r\n          </div>\r\n\r\n          {/* Search */}\r\n          <div className={styles.filterGroup}>\r\n            <label>Search</label>\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search products...\"\r\n              value={filters.search}\r\n              onChange={(e) => handleFilterChange('search', e.target.value)}\r\n              className={styles.searchInput}\r\n            />\r\n          </div>\r\n\r\n          {/* Collection Filter */}\r\n          <div className={styles.filterGroup}>\r\n            <label>Collection</label>\r\n            <select\r\n              value={filters.collectionId}\r\n              onChange={(e) => handleFilterChange('collectionId', e.target.value ? parseInt(e.target.value) : '')}\r\n              className={styles.filterSelect}\r\n            >\r\n              <option value=\"\">All Collections</option>\r\n              {collections.map(collection => (\r\n                <option key={collection.id} value={collection.id}>\r\n                  {collection.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* Price Range */}\r\n          <div className={styles.filterGroup}>\r\n            <label>Price Range</label>\r\n            <div className={styles.priceRange}>\r\n              <input\r\n                type=\"number\"\r\n                placeholder=\"Min\"\r\n                value={filters.priceRange.min}\r\n                onChange={(e) => handlePriceRangeChange(parseInt(e.target.value) || 0, filters.priceRange.max)}\r\n                className={styles.priceInput}\r\n              />\r\n              <span>to</span>\r\n              <input\r\n                type=\"number\"\r\n                placeholder=\"Max\"\r\n                value={filters.priceRange.max}\r\n                onChange={(e) => handlePriceRangeChange(filters.priceRange.min, parseInt(e.target.value) || 10000)}\r\n                className={styles.priceInput}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Stock Filter */}\r\n          <div className={styles.filterGroup}>\r\n            <label className={styles.checkboxLabel}>\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={filters.inStockOnly}\r\n                onChange={(e) => handleFilterChange('inStockOnly', e.target.checked)}\r\n              />\r\n              In Stock Only\r\n            </label>\r\n          </div>\r\n\r\n          {/* Sort Options */}\r\n          <div className={styles.filterGroup}>\r\n            <label>Sort By</label>\r\n            <select\r\n              value={`${filters.sortBy}-${filters.sortDirection}`}\r\n              onChange={(e) => {\r\n                const [sortBy, sortDirection] = e.target.value.split('-');\r\n                handleFilterChange('sortBy', sortBy);\r\n                handleFilterChange('sortDirection', sortDirection);\r\n              }}\r\n              className={styles.filterSelect}\r\n            >\r\n              <option value=\"name-asc\">Name (A-Z)</option>\r\n              <option value=\"name-desc\">Name (Z-A)</option>\r\n              <option value=\"price-asc\">Price (Low to High)</option>\r\n              <option value=\"price-desc\">Price (High to Low)</option>\r\n              <option value=\"newest-desc\">Newest First</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Products Grid */}\r\n        <div className={styles.productsSection}>\r\n          <ProductGrid\r\n            products={filteredProducts}\r\n            isLoading={isLoading}\r\n            emptyMessage=\"No products match your current filters. Try adjusting your search criteria.\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GACpD,qDAAqD,GACrD,8CAA8C;;;;AAG9C;AAEA;AAAA;AAAA;AACA;AAAA;AACA;AANA;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,QAAQ;QACR,cAAc;QACd,YAAY;YAAE,KAAK;YAAG,KAAK;QAAM;QACjC,aAAa;QACb,QAAQ;QACR,eAAe;IACjB;IAEA,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACxD,2JAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;gBACzB,8JAAA,CAAA,oBAAiB,CAAC,GAAG,CAAC,MAAM;aAC7B;YACD,YAAY;YACZ,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,WAAW;eAAI;SAAS;QAE5B,gBAAgB;QAChB,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACpC,QAAQ,WAAW,EAAE,cAAc,SAAS,gBAC3C,QAAQ,IAAI,IAAI,MAAM,OAAO,CAAC,QAAQ,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAA,MAChE,OAAO,QAAQ,YAAY,IAAI,WAAW,GAAG,QAAQ,CAAC;QAG5D;QAEA,oBAAoB;QACpB,IAAI,QAAQ,YAAY,EAAE;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK,QAAQ,YAAY;QACrF;QAEA,qBAAqB;QACrB,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG,IAAI,QAAQ,KAAK,IAAI,QAAQ,UAAU,CAAC,GAAG;QAGpF,eAAe;QACf,IAAI,QAAQ,WAAW,EAAE;YACvB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,GAAG;QACxD;QAEA,UAAU;QACV,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI,aAAa;YAEjB,OAAQ,QAAQ,MAAM;gBACpB,KAAK;oBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACxC;gBACF,KAAK;oBACH,aAAa,EAAE,KAAK,GAAG,EAAE,KAAK;oBAC9B;gBACF,KAAK;oBACH,aAAa,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;oBAC5E;YACJ;YAEA,OAAO,QAAQ,aAAa,KAAK,SAAS,CAAC,aAAa;QAC1D;QAEA,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,yBAAyB,CAAC,KAAa;QAC3C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,YAAY;oBAAE;oBAAK;gBAAI;YACzB,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,QAAQ;YACR,cAAc;YACd,YAAY;gBAAE,KAAK;gBAAG,KAAK;YAAM;YACjC,aAAa;YACb,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,8OAAC;gBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCAAG,WAAW,8IAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAC7B,8OAAC;gCAAE,WAAW,8IAAA,CAAA,UAAM,CAAC,QAAQ;0CAAE;;;;;;;;;;;;kCAKjC,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,aAAa;;0CAClC,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;;kDAE9B,8OAAC;wCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;wCAAE,SAAQ;wCAAY,MAAK;wCAAO,QAAO;kDACxE,cAAA,8OAAC;4CAAQ,QAAO;;;;;;;;;;;oCACZ;;;;;;;0CAIR,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;;oCAChC,iBAAiB,MAAM;oCAAC;oCAAE,iBAAiB,MAAM,KAAK,IAAI,YAAY;;;;;;;;;;;;;;;;;;;0BAK7E,8OAAC;gBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,OAAO;;kCAE5B,8OAAC;wBAAI,WAAW,GAAG,8IAAA,CAAA,UAAM,CAAC,cAAc,CAAC,CAAC,EAAE,cAAc,8IAAA,CAAA,UAAM,CAAC,WAAW,GAAG,IAAI;;0CACjF,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,aAAa;;kDAClC,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;wCAAO,SAAS;wCAAc,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;;;;;;;0CAMjE,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;;;;;;;;;;;;0CAKjC,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCACC,OAAO,QAAQ,YAAY;wCAC3B,UAAU,CAAC,IAAM,mBAAmB,gBAAgB,EAAE,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI;wCAChG,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;;0DAE9B,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,YAAY,GAAG,CAAC,CAAA,2BACf,8OAAC;oDAA2B,OAAO,WAAW,EAAE;8DAC7C,WAAW,IAAI;mDADL,WAAW,EAAE;;;;;;;;;;;;;;;;;0CAQhC,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;0DAC/B,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;gDAC7B,UAAU,CAAC,IAAM,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK,GAAG,QAAQ,UAAU,CAAC,GAAG;gDAC7F,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;;;;;0DAE9B,8OAAC;0DAAK;;;;;;0DACN,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO,QAAQ,UAAU,CAAC,GAAG;gDAC7B,UAAU,CAAC,IAAM,uBAAuB,QAAQ,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC5F,WAAW,8IAAA,CAAA,UAAM,CAAC,UAAU;;;;;;;;;;;;;;;;;;0CAMlC,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;0CAChC,cAAA,8OAAC;oCAAM,WAAW,8IAAA,CAAA,UAAM,CAAC,aAAa;;sDACpC,8OAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,WAAW;4CAC5B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,OAAO;;;;;;wCACnE;;;;;;;;;;;;0CAMN,8OAAC;gCAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCACC,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,EAAE;wCACnD,UAAU,CAAC;4CACT,MAAM,CAAC,QAAQ,cAAc,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;4CACrD,mBAAmB,UAAU;4CAC7B,mBAAmB,iBAAiB;wCACtC;wCACA,WAAW,8IAAA,CAAA,UAAM,CAAC,YAAY;;0DAE9B,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,8OAAC;wBAAI,WAAW,8IAAA,CAAA,UAAM,CAAC,eAAe;kCACpC,cAAA,8OAAC,sMAAA,CAAA,cAAW;4BACV,UAAU;4BACV,WAAW;4BACX,cAAa;;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}]}