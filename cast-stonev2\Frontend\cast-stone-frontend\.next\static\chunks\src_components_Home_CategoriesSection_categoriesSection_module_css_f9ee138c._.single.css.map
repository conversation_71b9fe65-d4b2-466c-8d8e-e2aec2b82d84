{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CategoriesSection/categoriesSection.module.css"], "sourcesContent": ["/* Categories Section Styles */\r\n.categoriesSection {\r\n  padding: 6rem 0;\r\n  background: linear-gradient(135deg, #faf9f7 0%, #ffffff 100%);\r\n  position: relative;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Header Styles */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 4rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.sectionTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  color: #1e3a8a;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.sectionSubtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  color:rgb(54, 76, 137);\r\n  line-height: 1.6;\r\n  font-weight: 400;\r\n}\r\n\r\n/* Grid Layout */\r\n.grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* Loading States */\r\n.loadingGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(2, 1fr);\r\n  gap: 2rem;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.loadingCard {\r\n  background: #ffffff;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n.loadingImage {\r\n  width: 100%;\r\n  height: 300px;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 2s infinite;\r\n}\r\n\r\n.loadingContent {\r\n  padding: 2rem;\r\n}\r\n\r\n.loadingText {\r\n  height: 1rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 2s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.loadingText:nth-child(1) {\r\n  width: 60%;\r\n}\r\n\r\n.loadingText:nth-child(2) {\r\n  width: 80%;\r\n}\r\n\r\n.loadingText:nth-child(3) {\r\n  width: 40%;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* Error State */\r\n.errorMessage {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #6b7280;\r\n  font-size: 1.125rem;\r\n}\r\n\r\n/* Category Card Styles */\r\n.categoryCard {\r\n  position: relative;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  box-shadow: 0 8px 32px rgba(37, 99, 235, 0.1);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  height: 400px;\r\n}\r\n\r\n.categoryCard:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 20px 60px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.cardLink {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n  text-decoration: none;\r\n  color: inherit;\r\n  position: relative;\r\n}\r\n\r\n/* Image Styles */\r\n.imageContainer {\r\n  position: relative;\r\n  height: 60%;\r\n  overflow: hidden;\r\n}\r\n\r\n.categoryImage {\r\n  object-fit: cover;\r\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.categoryCard:hover .categoryImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.imageOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n  );\r\n  z-index: 1;\r\n}\r\n\r\n.noImagePlaceholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.noImageText {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Card Content */\r\n.cardContent {\r\n  position: relative;\r\n  height: 40%;\r\n  padding: 1.5rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  z-index: 2;\r\n}\r\n\r\n.cardHeader {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.stats {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: 0.5rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.statsNumber {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: #1e3a8a;\r\n  line-height: 1;\r\n}\r\n\r\n.statsLabel {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  color: #1e3a8a;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n.categoryTitle {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #1e3a8a;\r\n  margin: 0.25rem 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.categorySubtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.8rem;\r\n  font-weight: 600;\r\n  color:rgb(48, 69, 126);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.15em;\r\n  margin: 0;\r\n}\r\n\r\n.categoryDescription {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: #1e3a8a;\r\n  line-height: 1.5;\r\n  margin-bottom: 1rem;\r\n  flex-grow: 1;\r\n}\r\n\r\n/* Card Actions */\r\n.cardActions {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n  align-items: center;\r\n}\r\n\r\n.actionButton,\r\n.secondaryButton {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.8rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  border: none;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.actionButton {\r\n  background: #1e3a8a;\r\n  color: #ffffff;\r\n  padding: 0.5rem 1rem;\r\n}\r\n\r\n.actionButton:hover {\r\n  background: #1e3a8a;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.secondaryButton {\r\n  background: transparent;\r\n  color:rgb(51, 73, 134);\r\n  padding: 0.5rem 0.75rem;\r\n  border: 1px solid rgba(105, 120, 161, 0.3);\r\n}\r\n\r\n.secondaryButton:hover {\r\n  background: rgba(69, 66, 153, 0.1);\r\n  color: #1e3a8a;\r\n}\r\n\r\n/* Hover Effect */\r\n.hoverEffect {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\n    rgba(37, 99, 235, 0.05) 0%,\r\n    transparent 50%,\r\n    rgba(29, 78, 216, 0.05) 100%\r\n  );\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  z-index: 1;\r\n}\r\n\r\n.categoryCard:hover .hoverEffect {\r\n  opacity: 1;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .categoriesSection {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .grid {\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .categoryCard {\r\n    height: 350px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .categoryCard {\r\n    height: 300px;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .sectionSubtitle {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1.25rem;\r\n  }\r\n  \r\n  .statsNumber {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .categoryTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .categoriesSection {\r\n    padding: 3rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 2.5rem;\r\n  }\r\n  \r\n  .categoryCard {\r\n    height: 280px;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .cardActions {\r\n    flex-direction: column;\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .actionButton,\r\n  .secondaryButton {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;AAiBA;;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;AAeA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA"}}]}