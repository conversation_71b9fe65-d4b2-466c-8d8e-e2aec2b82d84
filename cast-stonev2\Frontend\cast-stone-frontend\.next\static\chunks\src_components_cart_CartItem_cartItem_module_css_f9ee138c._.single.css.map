{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartItem/cartItem.module.css"], "sourcesContent": ["/* Cart Item Styles - Magazine/Editorial Theme */\r\n.cartItem {\r\n  display: grid;\r\n  grid-template-columns: 120px 1fr auto auto auto;\r\n  gap: 1.5rem;\r\n  padding: 1.5rem;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  align-items: start;\r\n  transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.cartItem:hover {\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* Product Image */\r\n.imageContainer {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n/* Product Details */\r\n.productDetails {\r\n  min-width: 0; /* Allow text truncation */\r\n}\r\n\r\n.productName {\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.5rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.productDescription {\r\n  color: #4b5563;\r\n  font-size: 0.9rem;\r\n  line-height: 1.5;\r\n  margin: 0 0 0.75rem 0;\r\n}\r\n\r\n.productMeta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.unitPrice {\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.collection {\r\n  color: #2563eb;\r\n  font-size: 0.85rem;\r\n  font-style: italic;\r\n}\r\n\r\n.stockStatus {\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n.inStock {\r\n  color: #059669;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.outOfStock {\r\n  color: #dc2626;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Quantity Section */\r\n.quantitySection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  min-width: 120px;\r\n}\r\n\r\n.quantityLabel {\r\n  color: #1f2937;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.quantityControls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  padding: 0.25rem;\r\n}\r\n\r\n.quantityBtn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: 1px solid #d1d5db;\r\n  background: white;\r\n  color: #1f2937;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.quantityBtn:hover:not(:disabled) {\r\n  background: #2563eb;\r\n  color: white;\r\n  border-color: #2563eb;\r\n}\r\n\r\n.quantityBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.quantity {\r\n  min-width: 40px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n}\r\n\r\n.updating {\r\n  color: #2563eb;\r\n  font-size: 0.75rem;\r\n  font-style: italic;\r\n}\r\n\r\n/* Price Section */\r\n.priceSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 0.25rem;\r\n  min-width: 120px;\r\n}\r\n\r\n.itemTotal {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.priceBreakdown {\r\n  color: #4b5563;\r\n  font-size: 0.85rem;\r\n}\r\n\r\n/* Remove Section */\r\n.removeSection {\r\n  display: flex;\r\n  align-items: flex-start;\r\n}\r\n\r\n.removeBtn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: none;\r\n  background: transparent;\r\n  color: #dc2626;\r\n  border-radius: 6px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.removeBtn:hover:not(:disabled) {\r\n  background: #fee2e2;\r\n  color: #b91c1c;\r\n}\r\n\r\n.removeBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.removeBtn svg {\r\n  width: 20px;\r\n  height: 20px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.removing {\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .cartItem {\r\n    grid-template-columns: 80px 1fr;\r\n    grid-template-areas: \r\n      \"image details\"\r\n      \"image quantity\"\r\n      \"image price\"\r\n      \"remove remove\";\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n  }\r\n\r\n  .imageContainer {\r\n    grid-area: image;\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n\r\n  .productDetails {\r\n    grid-area: details;\r\n  }\r\n\r\n  .quantitySection {\r\n    grid-area: quantity;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: flex-start;\r\n    min-width: auto;\r\n  }\r\n\r\n  .quantityLabel {\r\n    margin-right: 0.5rem;\r\n  }\r\n\r\n  .priceSection {\r\n    grid-area: price;\r\n    align-items: flex-start;\r\n    min-width: auto;\r\n  }\r\n\r\n  .removeSection {\r\n    grid-area: remove;\r\n    justify-content: center;\r\n    margin-top: 0.5rem;\r\n  }\r\n\r\n  .productName {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .itemTotal {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .cartItem {\r\n    grid-template-columns: 1fr;\r\n    grid-template-areas: \r\n      \"image\"\r\n      \"details\"\r\n      \"quantity\"\r\n      \"price\"\r\n      \"remove\";\r\n    text-align: center;\r\n  }\r\n\r\n  .imageContainer {\r\n    width: 120px;\r\n    height: 120px;\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .quantitySection {\r\n    justify-content: center;\r\n  }\r\n\r\n  .priceSection {\r\n    align-items: center;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAYA;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;;;;;;AAeA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAMA;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA;;;;;;;;EAQA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;;AAKF;EACE;;;;;;;;;;EAWA;;;;;;EAMA;;;;EAIA"}}]}