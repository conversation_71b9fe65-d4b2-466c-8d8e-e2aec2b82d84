{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/Home/CollectionsCarousel/collectionsCarousel.module.css"], "sourcesContent": ["/* Collections Carousel Styles */\r\n.collectionsSection {\r\n  padding: 6rem 0;\r\n  background: linear-gradient(135deg, #ffffff 0%, #faf9f7 100%);\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* Header Styles */\r\n.header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-end;\r\n  margin-bottom: 3rem;\r\n  gap: 2rem;\r\n}\r\n\r\n.headerContent {\r\n  flex: 1;\r\n}\r\n\r\n.title {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  color: #1e3a8a;\r\n  margin-bottom: 1rem;\r\n  letter-spacing: -0.02em;\r\n}\r\n\r\n.subtitle {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 1.125rem;\r\n  color: #1e40af;\r\n  line-height: 1.6;\r\n  font-weight: 400;\r\n  max-width: 600px;\r\n}\r\n\r\n/* Navigation */\r\n.navigation {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.navButton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 48px;\r\n  height: 48px;\r\n  background: #ffffff;\r\n  border: 2px solid rgba(37, 99, 235, 0.1);\r\n  border-radius: 50%;\r\n  color: #1e3a8a;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);\r\n}\r\n\r\n.navButton:hover {\r\n  background: #1e3a8a;\r\n  color: #ffffff;\r\n  border-color: #1e3a8a;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);\r\n}\r\n\r\n/* Carousel Container */\r\n.carouselContainer {\r\n  position: relative;\r\n  margin-bottom: 3rem;\r\n}\r\n\r\n.carousel {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  overflow-x: auto;\r\n  scroll-behavior: smooth;\r\n  padding: 1rem 0;\r\n  scrollbar-width: none;\r\n  -ms-overflow-style: none;\r\n}\r\n\r\n.carousel::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n/* Collection Card */\r\n.collectionCard {\r\n  flex: 0 0 320px;\r\n  height: 400px;\r\n  position: relative;\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n  background: #ffffff;\r\n  box-shadow: 0 8px 32px rgba(30, 11, 172, 0.1);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.collectionCard:hover {\r\n  transform: translateY(-8px);\r\n  box-shadow: 0 20px 60px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.cardLink {\r\n  display: block;\r\n  width: 100%;\r\n  height: 100%;\r\n  text-decoration: none;\r\n  color: inherit;\r\n}\r\n\r\n/* Image Styles */\r\n.imageContainer {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.collectionImage {\r\n  object-fit: cover;\r\n  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.collectionCard:hover .collectionImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.imageOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(\r\n    135deg,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n  );\r\n  z-index: 1;\r\n}\r\n\r\n.noImagePlaceholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.noImageText {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n}\r\n\r\n/* Card Content */\r\n.cardContent {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  padding: 2rem;\r\n  color: #ffffff;\r\n  z-index: 2;\r\n  background: linear-gradient(\r\n    to top,\r\nrgba(25, 35, 65, 0.4) 0%,\r\n    rgba(20, 30, 60, 0.35) 50%,\r\n    rgba(30, 40, 70, 0.4) 100%\r\n    transparent 100%\r\n  );\r\n}\r\n\r\n.productCount {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.8rem;\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.1em;\r\n  color: #1e3a8a;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.collectionName {\r\n  font-family: 'Georgia', 'Times New Roman', serif;\r\n  font-size: 1.5rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 0.75rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.collectionDescription {\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  line-height: 1.4;\r\n  margin-bottom: 1rem;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.cardAction {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  color: #1e3a8a;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.collectionCard:hover .cardAction {\r\n  color: #ffffff;\r\n  transform: translateX(4px);\r\n}\r\n\r\n.actionText {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n/* View All Button */\r\n.viewAllContainer {\r\n  text-align: center;\r\n}\r\n\r\n.viewAllButton {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1rem 2rem;\r\n  background: transparent;\r\n  color: #1e3a8a;\r\n  text-decoration: none;\r\n  border: 2px solid #1e3a8a;\r\n  border-radius: 50px;\r\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n}\r\n\r\n.viewAllButton:hover {\r\n  background: #1e3a8a;\r\n  color: #ffffff;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);\r\n}\r\n\r\n/* Loading and Error States */\r\n.loadingContainer,\r\n.errorContainer {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #4b5563;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid rgba(37, 99, 235, 0.1);\r\n  border-top: 3px solid #1e3a8a;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .collectionsSection {\r\n    padding: 4rem 0;\r\n  }\r\n  \r\n  .container {\r\n    padding: 0 1.5rem;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .collectionCard {\r\n    flex: 0 0 280px;\r\n    height: 350px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 1.5rem;\r\n  }\r\n  \r\n  .navigation {\r\n    align-self: flex-end;\r\n  }\r\n  \r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .subtitle {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .collectionCard {\r\n    flex: 0 0 260px;\r\n    height: 320px;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 0 1rem;\r\n  }\r\n  \r\n  .header {\r\n    margin-bottom: 2rem;\r\n  }\r\n  \r\n  .navigation {\r\n    display: none;\r\n  }\r\n  \r\n  .collectionCard {\r\n    flex: 0 0 240px;\r\n    height: 300px;\r\n  }\r\n  \r\n  .cardContent {\r\n    padding: 1.25rem;\r\n  }\r\n  \r\n  .collectionName {\r\n    font-size: 1.25rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;AAeA;;;;;;;;;AASA;;;;;;;;AASA;;;;;;;;;;;AAiBA;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;AAQA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;AAMA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA"}}]}