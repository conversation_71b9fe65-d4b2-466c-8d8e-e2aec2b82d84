{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/catalog/catalog.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cardAction\": \"catalog-module__ZyI-Aq__cardAction\",\n  \"cardIcon\": \"catalog-module__ZyI-Aq__cardIcon\",\n  \"catalogCard\": \"catalog-module__ZyI-Aq__catalogCard\",\n  \"catalogOptions\": \"catalog-module__ZyI-Aq__catalogOptions\",\n  \"container\": \"catalog-module__ZyI-Aq__container\",\n  \"content\": \"catalog-module__ZyI-Aq__content\",\n  \"feature\": \"catalog-module__ZyI-Aq__feature\",\n  \"featureGrid\": \"catalog-module__ZyI-Aq__featureGrid\",\n  \"features\": \"catalog-module__ZyI-Aq__features\",\n  \"header\": \"catalog-module__ZyI-Aq__header\",\n  \"subtitle\": \"catalog-module__ZyI-Aq__subtitle\",\n  \"title\": \"catalog-module__ZyI-Aq__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/catalog/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport styles from './catalog.module.css';\r\n\r\nexport default function CatalogPage() {\r\n  return (\r\n    <div className={styles.container}>\r\n      <div className={styles.header}>\r\n        <h1 className={styles.title}>Product Catalog</h1>\r\n        <p className={styles.subtitle}>\r\n          Browse our complete collection of cast stone products and collections\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.content}>\r\n        <div className={styles.catalogOptions}>\r\n          <Link href=\"/products\" className={styles.catalogCard}>\r\n            <div className={styles.cardIcon}>\r\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\" />\r\n              </svg>\r\n            </div>\r\n            <h3>All Products</h3>\r\n            <p>Browse our complete product catalog with advanced filtering and search</p>\r\n            <span className={styles.cardAction}>View Products →</span>\r\n          </Link>\r\n\r\n          <Link href=\"/collections\" className={styles.catalogCard}>\r\n            <div className={styles.cardIcon}>\r\n              <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\r\n              </svg>\r\n            </div>\r\n            <h3>Collections</h3>\r\n            <p>Explore our curated collections organized by style and application</p>\r\n            <span className={styles.cardAction}>View Collections →</span>\r\n          </Link>\r\n        </div>\r\n\r\n        <div className={styles.features}>\r\n          <h2>Catalog Features</h2>\r\n          <div className={styles.featureGrid}>\r\n            <div className={styles.feature}>\r\n              <h4>Advanced Search</h4>\r\n              <p>Find products by name, description, or tags with powerful search functionality</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Filter by Collection</h4>\r\n              <p>Browse products within specific collections to find matching pieces</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Price Range Filtering</h4>\r\n              <p>Set your budget and find products within your price range</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Stock Availability</h4>\r\n              <p>See real-time stock levels and filter for in-stock items only</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>Detailed Specifications</h4>\r\n              <p>Access comprehensive product details, dimensions, and technical specifications</p>\r\n            </div>\r\n            <div className={styles.feature}>\r\n              <h4>High-Quality Images</h4>\r\n              <p>View multiple high-resolution images for each product</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,8OAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,8OAAC;wBAAG,WAAW,4IAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,8OAAC;wBAAE,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC;wBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,cAAc;;0CACnC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;;kDAClD,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;kDAC7B,cAAA,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;4CAAO,QAAO;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAK,WAAW,4IAAA,CAAA,UAAM,CAAC,UAAU;kDAAE;;;;;;;;;;;;0CAGtC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAe,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;;kDACrD,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;kDAC7B,cAAA,8OAAC;4CAAI,OAAM;4CAAK,QAAO;4CAAK,SAAQ;4CAAY,MAAK;4CAAO,QAAO;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAK,WAAW,4IAAA,CAAA,UAAM,CAAC,UAAU;kDAAE;;;;;;;;;;;;;;;;;;kCAIxC,8OAAC;wBAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,QAAQ;;0CAC7B,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;gCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;kDAEL,8OAAC;wCAAI,WAAW,4IAAA,CAAA,UAAM,CAAC,OAAO;;0DAC5B,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}