{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/contexts/AdminAuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { User } from '@/services/types/entities';\nimport { userGetService } from '@/services/api/users/get';\n\ninterface AdminAuthContextType {\n  admin: User | null;\n  isLoading: boolean;\n  login: (email: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  isAuthenticated: boolean;\n}\n\nconst AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);\n\ninterface AdminAuthProviderProps {\n  children: ReactNode;\n}\n\nexport function AdminAuthProvider({ children }: AdminAuthProviderProps) {\n  const [admin, setAdmin] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkExistingSession = () => {\n      try {\n        const storedAdmin = localStorage.getItem('admin_session');\n        if (storedAdmin) {\n          const adminData = JSON.parse(storedAdmin);\n          // Verify the session is still valid (simple check)\n          if (adminData.email && adminData.role === 'admin') {\n            setAdmin(adminData);\n          } else {\n            localStorage.removeItem('admin_session');\n          }\n        }\n      } catch (error) {\n        console.error('Error checking existing session:', error);\n        localStorage.removeItem('admin_session');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkExistingSession();\n  }, []);\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true);\n    try {\n      const adminUser = await userGetService.validateAdminCredentials(email, password);\n      \n      if (adminUser) {\n        setAdmin(adminUser);\n        // Store session in localStorage (in production, use secure httpOnly cookies)\n        localStorage.setItem('admin_session', JSON.stringify(adminUser));\n        return true;\n      }\n      \n      return false;\n    } catch (error) {\n      console.error('Login error:', error);\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setAdmin(null);\n    localStorage.removeItem('admin_session');\n  };\n\n  const value: AdminAuthContextType = {\n    admin,\n    isLoading,\n    login,\n    logout,\n    isAuthenticated: !!admin\n  };\n\n  return (\n    <AdminAuthContext.Provider value={value}>\n      {children}\n    </AdminAuthContext.Provider>\n  );\n}\n\nexport function useAdminAuth() {\n  const context = useContext(AdminAuthContext);\n  if (context === undefined) {\n    throw new Error('useAdminAuth must be used within an AdminAuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAcA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,SAAS,kBAAkB,EAAE,QAAQ,EAA0B;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,mDAAmD;oBACnD,IAAI,UAAU,KAAK,IAAI,UAAU,IAAI,KAAK,SAAS;wBACjD,SAAS;oBACX,OAAO;wBACL,aAAa,UAAU,CAAC;oBAC1B;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,aAAa,UAAU,CAAC;YAC1B,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QACb,IAAI;YACF,MAAM,YAAY,MAAM,sIAAA,CAAA,iBAAc,CAAC,wBAAwB,CAAC,OAAO;YAEvE,IAAI,WAAW;gBACb,SAAS;gBACT,6EAA6E;gBAC7E,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBACrD,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,SAAS;QACT,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,QAA8B;QAClC;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}