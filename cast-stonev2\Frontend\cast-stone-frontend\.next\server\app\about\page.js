(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413);function n(){return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"About Cast Stone"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"Building the future with modern technology and innovative solutions"})]}),(0,r.jsxs)("div",{className:"prose prose-lg mx-auto",children:[(0,r.jsx)("h2",{children:"Our Mission"}),(0,r.jsx)("p",{children:"Cast Stone is dedicated to creating robust, scalable, and modern web applications that meet the evolving needs of businesses and users alike. We leverage cutting-edge technologies to deliver exceptional digital experiences."}),(0,r.jsx)("h2",{children:"Technology Stack"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 not-prose",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Frontend"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,r.jsx)("li",{children:"• Next.js 14 with App Router"}),(0,r.jsx)("li",{children:"• TypeScript"}),(0,r.jsx)("li",{children:"• Tailwind CSS"}),(0,r.jsx)("li",{children:"• Component-based Architecture"})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Backend"}),(0,r.jsxs)("ul",{className:"space-y-2 text-gray-600",children:[(0,r.jsx)("li",{children:"• .NET 8 Web API"}),(0,r.jsx)("li",{children:"• MongoDB with MongoDB.Driver"}),(0,r.jsx)("li",{children:"• Repository Pattern"}),(0,r.jsx)("li",{children:"• Clean Architecture"})]})]})]}),(0,r.jsx)("h2",{children:"Features"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Modern, responsive design"}),(0,r.jsx)("li",{children:"RESTful API architecture"}),(0,r.jsx)("li",{children:"Type-safe development"}),(0,r.jsx)("li",{children:"Scalable database design"}),(0,r.jsx)("li",{children:"Component-based UI"})]})]})]})}s(61120)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32664:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),n=s(48088),a=s(88170),i=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28770)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\about\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,72,658,913],()=>s(32664));module.exports=r})();