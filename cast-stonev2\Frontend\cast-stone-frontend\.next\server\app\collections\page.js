(()=>{var e={};e.id=735,e.ids=[735],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28842:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>s});let s=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\collections\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33656:(e,t,o)=>{Promise.resolve().then(o.bind(o,28842))},33873:e=>{"use strict";e.exports=require("path")},34328:(e,t,o)=>{Promise.resolve().then(o.bind(o,88824))},56008:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=o(65239),r=o(48088),n=o(88170),i=o.n(n),l=o(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);o.d(t,a);let c={children:["",{children:["collections",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,28842)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\collections\\page.tsx"],p={require:o,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/collections/page",pathname:"/collections",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>r});var s=o(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76961:e=>{e.exports={container:"collections_container__y7rE4",header:"collections_header__p_9Wr",title:"collections_title__ocSpK",subtitle:"collections_subtitle__0F74o",loading:"collections_loading__G0ebM",error:"collections_error__ll0Kg",collectionsGrid:"collections_collectionsGrid__CJFFL",collectionCard:"collections_collectionCard__NlgTZ",imageContainer:"collections_imageContainer__0rtAG",collectionImage:"collections_collectionImage__wLMV2",placeholderImage:"collections_placeholderImage__VZu4o",cardContent:"collections_cardContent__IeUpJ",collectionName:"collections_collectionName__Lbyf9",collectionDescription:"collections_collectionDescription__NE8IZ",cardFooter:"collections_cardFooter__EewT9",viewCollection:"collections_viewCollection__eyRCt",emptyState:"collections_emptyState__oZQ1Q"}},79551:e=>{"use strict";e.exports=require("url")},88824:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>d});var s=o(60687),r=o(43210),n=o(85814),i=o.n(n),l=o(30474);o(63968);var a=o(76961),c=o.n(a);function d(){let[e,t]=(0,r.useState)([]),[o,n]=(0,r.useState)(!0),[a,d]=(0,r.useState)(null);return o?(0,s.jsx)("div",{className:c().container,children:(0,s.jsx)("div",{className:c().loading,children:"Loading collections..."})}):a?(0,s.jsx)("div",{className:c().container,children:(0,s.jsx)("div",{className:c().error,children:a})}):(0,s.jsxs)("div",{className:c().container,children:[(0,s.jsxs)("div",{className:c().header,children:[(0,s.jsx)("h1",{className:c().title,children:"Our Collections"}),(0,s.jsx)("p",{className:c().subtitle,children:"Explore our curated collections of handcrafted cast stone pieces"})]}),(0,s.jsx)("div",{className:c().collectionsGrid,children:e.map(e=>(0,s.jsxs)(i(),{href:`/collections/${e.id}`,className:c().collectionCard,children:[(0,s.jsx)("div",{className:c().imageContainer,children:e.images&&e.images.length>0?(0,s.jsx)(l.default,{src:e.images[0],alt:e.name,className:c().collectionImage,width:350,height:250,style:{objectFit:"cover"}}):(0,s.jsx)("div",{className:c().placeholderImage,children:(0,s.jsx)("span",{children:"No Image"})})}),(0,s.jsxs)("div",{className:c().cardContent,children:[(0,s.jsx)("h3",{className:c().collectionName,children:e.name}),e.description&&(0,s.jsx)("p",{className:c().collectionDescription,children:e.description}),(0,s.jsx)("div",{className:c().cardFooter,children:(0,s.jsx)("span",{className:c().viewCollection,children:"View Collection →"})})]})]},e.id))}),0===e.length&&(0,s.jsxs)("div",{className:c().emptyState,children:[(0,s.jsx)("h3",{children:"No collections available"}),(0,s.jsx)("p",{children:"Check back soon for new collections."})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),s=t.X(0,[447,72,658,474,913],()=>o(56008));module.exports=s})();