.pageContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 3rem 0;
  text-align: center;
}

.headerContent h1 {
  margin: 0 0 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.headerContent p {
  margin: 0;
  font-size: 1.125rem;
  opacity: 0.9;
}

.errorBanner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  margin: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.errorBanner p {
  margin: 0;
  font-weight: 500;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contentContainer {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.mainContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sidebar {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  height: fit-content;
  position: sticky;
  top: 2rem;
}

.benefits h3 {
  margin: 0 0 1.5rem;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.benefits ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.benefits li {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
  position: relative;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

.benefits li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

.formHeader {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.formHeader h2 {
  margin: 0 0 0.5rem;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.formHeader p {
  margin: 0 0 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.backToLogin {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: underline;
  padding: 0;
}

.backToLogin:hover {
  color: #2563eb;
}

.messageContainer {
  max-width: 600px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.successMessage,
.pendingMessage {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  text-align: center;
}

.successIcon,
.pendingIcon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.successIcon {
  background: #d1fae5;
  color: #10b981;
}

.pendingIcon {
  background: #fef3c7;
  color: #f59e0b;
}

.successMessage h2,
.pendingMessage h2 {
  margin: 0 0 1rem;
  color: #1f2937;
  font-size: 1.75rem;
  font-weight: 600;
}

.successMessage p,
.pendingMessage p {
  margin: 0 0 2rem;
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
}

.nextSteps {
  background: #f9fafb;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
  text-align: left;
}

.nextSteps h3 {
  margin: 0 0 1rem;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
}

.nextSteps ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #374151;
}

.nextSteps li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.contactInfo {
  background: #f0f9ff;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
}

.contactInfo p {
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
}

.contactInfo a {
  color: #3b82f6;
  text-decoration: none;
}

.contactInfo a:hover {
  text-decoration: underline;
}

.primaryButton {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.primaryButton:hover {
  background: #2563eb;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentContainer {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .sidebar {
    position: static;
    order: -1;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 2rem 1rem;
  }
  
  .headerContent h1 {
    font-size: 2rem;
  }
  
  .contentContainer {
    padding: 1rem;
  }
  
  .sidebar {
    padding: 1.5rem;
  }
  
  .formHeader {
    padding: 1.5rem 1.5rem 1rem;
  }
  
  .successMessage,
  .pendingMessage {
    padding: 2rem;
  }
  
  .messageContainer {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .headerContent h1 {
    font-size: 1.75rem;
  }
  
  .headerContent p {
    font-size: 1rem;
  }
  
  .successMessage,
  .pendingMessage {
    padding: 1.5rem;
  }
  
  .successIcon,
  .pendingIcon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}
