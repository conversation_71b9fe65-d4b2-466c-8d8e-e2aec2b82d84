(()=>{var e={};e.id=594,e.ids=[594],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14268:(e,t,r)=>{Promise.resolve().then(r.bind(r,30657))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30657:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\products\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},51682:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(43210),o=r(83645),l=r(73441),n=r(63968);function d({product:e,collections:t,onClose:r,onSuccess:o}){let[l,d]=(0,a.useState)(!1),[i,c]=(0,a.useState)({}),[m,u]=(0,a.useState)([]),[x,p]=(0,a.useState)(!1),[b,h]=(0,a.useState)({name:"",productCode:"",description:"",price:0,stock:0,collectionId:0,images:[],tags:[]}),[g,f]=(0,a.useState)({material:"",dimensions:"",totalWeight:"",weightWithWater:"",waterVolume:"",Base_Dimensions:"",photographed_In:"",Pieces:""}),[y,j]=(0,a.useState)({upc:"",indoorUseOnly:"",assemblyRequired:"",easeOfAssembly:"",assistanceRequired:"",splashLevel:"",soundLevel:"",soundType:"",replacementPumpKit:"",electricalCordLength:"",pumpSize:"",shipMethod:"",catalogPage:"",factory_Code:"",drainage_Info:"",inside_Top:"",inside_Bottom:"",inside_Height:""}),[v,N]=(0,a.useState)({care:"",productInstructions:"",cad:""}),[w,k]=(0,a.useState)(""),[C,I]=(0,a.useState)(""),S=()=>{let e={};return b.name.trim()?b.name.length>200&&(e.name="Name must be less than 200 characters"):e.name="Name is required",b.productCode&&b.productCode.length>50&&(e.productCode="Product code must be less than 50 characters"),b.description&&b.description.length>1e3&&(e.description="Description must be less than 1000 characters"),b.price<=0&&(e.price="Price must be greater than 0"),b.stock<0&&(e.stock="Stock cannot be negative"),b.collectionId||(e.collectionId="Collection is required"),c(e),0===Object.keys(e).length},P=()=>Object.values(g).some(e=>""!==e.trim()),D=()=>Object.values(y).some(e=>""!==e.trim()),L=()=>Object.values(v).some(e=>""!==e.trim()),U=async t=>{if(t.preventDefault(),S()){d(!0);try{if(e){let t={...b,productSpecifications:P()?g:void 0,productDetails:D()?y:void 0,downloadableContent:L()?v:void 0};await n.jU.update.update(e.id,t)}else{let e={...b,productSpecifications:P()?{...g,productId:0}:void 0,productDetails:D()?{...y,productId:0}:void 0,downloadableContent:L()?{...v,productId:0}:void 0};await n.jU.post.create(e)}o()}catch(e){console.error("Error saving product:",e),c({submit:"Failed to save product. Please try again."})}finally{d(!1)}}},_=()=>{w.trim()&&!b.tags.includes(w.trim())&&(h(e=>({...e,tags:[...e.tags,w.trim()]})),k(""))},A=e=>{h(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},M=async()=>{C.trim()&&!b.images.includes(C.trim())&&(await W(C.trim())?(h(e=>({...e,images:[...e.images,C.trim()]})),I(""),c(e=>({...e,imageInput:""}))):c(e=>({...e,imageInput:"Invalid image URL or image not found in uploaded images"})))},F=e=>{b.images.includes(e)||h(t=>({...t,images:[...t.images,e]}))},W=async e=>!!m.some(t=>t.secureUrl===e)||new Promise(t=>{let r=new Image;r.onload=()=>t(!0),r.onerror=()=>t(!1),r.src=e,setTimeout(()=>t(!1),5e3)}),R=e=>{h(t=>({...t,images:t.images.filter(t=>t!==e)}))};return(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,s.jsxs)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e?"Edit Product":"Add New Product"}),(0,s.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("form",{onSubmit:U,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"Name *"}),(0,s.jsx)("input",{type:"text",id:"name",value:b.name,onChange:e=>h(t=>({...t,name:e.target.value})),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.name?"border-red-500":"border-gray-300"}`,placeholder:"Enter product name"}),i.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"productCode",className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Code"}),(0,s.jsx)("input",{type:"text",id:"productCode",value:b.productCode,onChange:e=>h(t=>({...t,productCode:e.target.value})),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.productCode?"border-red-500":"border-gray-300"}`,placeholder:"Enter product code"}),i.productCode&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.productCode})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,s.jsx)("textarea",{id:"description",value:b.description,onChange:e=>h(t=>({...t,description:e.target.value})),rows:3,className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.description?"border-red-500":"border-gray-300"}`,placeholder:"Enter product description"}),i.description&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.description})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"price",className:"block text-sm font-medium text-gray-700 mb-1",children:"Price *"}),(0,s.jsx)("input",{type:"number",id:"price",step:"0.01",min:"0",value:b.price,onChange:e=>h(t=>({...t,price:parseFloat(e.target.value)||0})),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.price?"border-red-500":"border-gray-300"}`,placeholder:"0.00"}),i.price&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.price})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"stock",className:"block text-sm font-medium text-gray-700 mb-1",children:"Stock *"}),(0,s.jsx)("input",{type:"number",id:"stock",min:"0",value:b.stock,onChange:e=>h(t=>({...t,stock:parseInt(e.target.value)||0})),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.stock?"border-red-500":"border-gray-300"}`,placeholder:"0"}),i.stock&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.stock})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"collection",className:"block text-sm font-medium text-gray-700 mb-1",children:"Collection *"}),(0,s.jsxs)("select",{id:"collection",value:b.collectionId,onChange:e=>h(t=>({...t,collectionId:parseInt(e.target.value)||0})),className:`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.collectionId?"border-red-500":"border-gray-300"}`,children:[(0,s.jsx)("option",{value:0,children:"Select a collection"}),t.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," (Level ",e.level,")"]},e.id))]}),i.collectionId&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.collectionId})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Images"}),(0,s.jsx)("div",{className:"space-y-3 mb-4",children:b.images.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border",children:[(0,s.jsx)("img",{src:e,alt:`Product image ${t+1}`,className:"w-16 h-16 object-cover rounded-md border border-gray-200",onError:e=>{e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K"}}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900 truncate",children:["Image ",t+1]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 truncate",title:e,children:e})]}),(0,s.jsx)("button",{type:"button",onClick:()=>R(e),className:"flex-shrink-0 p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded",title:"Remove image",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},t))}),(0,s.jsxs)("div",{className:"mb-3",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:"Choose from uploaded images:"}),(0,s.jsxs)("select",{onChange:e=>e.target.value&&F(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",disabled:x,value:"",children:[(0,s.jsx)("option",{value:"",children:x?"Loading images...":"Select an uploaded image"}),m.map(e=>(0,s.jsx)("option",{value:e.secureUrl,children:e.fileName},e.publicId))]}),0===m.length&&!x&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["No uploaded images found. ",(0,s.jsx)("a",{href:"/admin/dashboard/images",target:"_blank",className:"text-amber-600 hover:text-amber-800",children:"Upload images here"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:"Or enter image URL manually:"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("input",{type:"url",value:C,onChange:e=>{I(e.target.value),i.imageInput&&c(e=>({...e,imageInput:""}))},onKeyDown:e=>"Enter"===e.key&&(e.preventDefault(),M()),className:`flex-1 px-3 py-2 border rounded-l-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${i.imageInput?"border-red-500":"border-gray-300"}`,placeholder:"Paste image URL here or copy from Images section"}),(0,s.jsx)("button",{type:"button",onClick:M,className:"px-4 py-2 bg-amber-900 text-white rounded-r-md hover:bg-amber-800 disabled:opacity-50",disabled:!C.trim(),children:"Add"})]}),i.imageInput&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i.imageInput}),(0,s.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Tip: You can copy image URLs from the ",(0,s.jsx)("a",{href:"/admin/dashboard/images",target:"_blank",className:"text-amber-600 hover:text-amber-800",children:"Images section"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-2",children:b.tags.map((e,t)=>(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800",children:[e,(0,s.jsx)("button",{type:"button",onClick:()=>A(e),className:"ml-1 text-amber-600 hover:text-amber-800",children:"\xd7"})]},t))}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("input",{type:"text",value:w,onChange:e=>k(e.target.value),onKeyDown:e=>"Enter"===e.key&&(e.preventDefault(),_()),className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"Add a tag"}),(0,s.jsx)("button",{type:"button",onClick:_,className:"px-4 py-2 bg-amber-900 text-white rounded-r-md hover:bg-amber-800",children:"Add"})]})]}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Product Specifications (Optional)"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Material"}),(0,s.jsx)("input",{type:"text",value:g.material,onChange:e=>f(t=>({...t,material:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Cast Stone"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Dimensions"}),(0,s.jsx)("input",{type:"text",value:g.dimensions,onChange:e=>f(t=>({...t,dimensions:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:'e.g., 24" L x 18" W x 36" H'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Base Dimensions"}),(0,s.jsx)("input",{type:"text",value:g.Base_Dimensions,onChange:e=>f(t=>({...t,Base_Dimensions:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:'e.g., 24" L x 18" W x 36" H'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Photographed In"}),(0,s.jsx)("input",{type:"text",value:g.photographed_In,onChange:e=>f(t=>({...t,photographed_In:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:'e.g., 24" L x 18" W x 36" H'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pieces"}),(0,s.jsx)("input",{type:"text",value:g.Pieces,onChange:e=>f(t=>({...t,Pieces:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:'e.g., 24" L x 18" W x 36" H'})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Weight"}),(0,s.jsx)("input",{type:"text",value:g.totalWeight,onChange:e=>f(t=>({...t,totalWeight:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., 150 lbs"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Weight With Water"}),(0,s.jsx)("input",{type:"text",value:g.weightWithWater,onChange:e=>f(t=>({...t,weightWithWater:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., 200 lbs"})]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Water Volume"}),(0,s.jsx)("input",{type:"text",value:g.waterVolume,onChange:e=>f(t=>({...t,waterVolume:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., 50 gallons"})]})]})]}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Product Details (Optional)"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"UPC"}),(0,s.jsx)("input",{type:"text",value:y.upc,onChange:e=>j(t=>({...t,upc:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., 615973253195"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Indoor Use Only"}),(0,s.jsxs)("select",{value:y.indoorUseOnly,onChange:e=>j(t=>({...t,indoorUseOnly:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",children:[(0,s.jsx)("option",{value:"",children:"Select..."}),(0,s.jsx)("option",{value:"Yes",children:"Yes"}),(0,s.jsx)("option",{value:"No",children:"No"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assembly Required"}),(0,s.jsxs)("select",{value:y.assemblyRequired,onChange:e=>j(t=>({...t,assemblyRequired:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",children:[(0,s.jsx)("option",{value:"",children:"Select..."}),(0,s.jsx)("option",{value:"Yes",children:"Yes"}),(0,s.jsx)("option",{value:"No",children:"No"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ease of Assembly"}),(0,s.jsx)("input",{type:"text",value:y.easeOfAssembly,onChange:e=>j(t=>({...t,easeOfAssembly:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Assistance Required"}),(0,s.jsx)("input",{type:"text",value:y.assistanceRequired,onChange:e=>j(t=>({...t,assistanceRequired:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Splash level"}),(0,s.jsx)("input",{type:"text",value:y.splashLevel,onChange:e=>j(t=>({...t,splashLevel:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sound level"}),(0,s.jsx)("input",{type:"text",value:y.soundLevel,onChange:e=>j(t=>({...t,soundLevel:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Sound Type"}),(0,s.jsx)("input",{type:"text",value:y.soundType,onChange:e=>j(t=>({...t,soundType:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Replacement Pump kit"}),(0,s.jsx)("input",{type:"text",value:y.replacementPumpKit,onChange:e=>j(t=>({...t,replacementPumpKit:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Electrical Cord Length"}),(0,s.jsx)("input",{type:"text",value:y.electricalCordLength,onChange:e=>j(t=>({...t,electricalCordLength:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Pump Size"}),(0,s.jsx)("input",{type:"text",value:y.pumpSize,onChange:e=>j(t=>({...t,pumpSize:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ship Method"}),(0,s.jsx)("input",{type:"text",value:y.shipMethod,onChange:e=>j(t=>({...t,shipMethod:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Drainage Info"}),(0,s.jsx)("input",{type:"text",value:y.drainage_Info,onChange:e=>j(t=>({...t,drainage_Info:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Inside Top"}),(0,s.jsx)("input",{type:"text",value:y.inside_Top,onChange:e=>j(t=>({...t,inside_Top:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Inside Bottom"}),(0,s.jsx)("input",{type:"text",value:y.inside_Bottom,onChange:e=>j(t=>({...t,inside_Bottom:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Inside Height"}),(0,s.jsx)("input",{type:"text",value:y.inside_Height,onChange:e=>j(t=>({...t,inside_Height:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Catalog Page"}),(0,s.jsx)("input",{type:"text",value:y.catalogPage,onChange:e=>j(t=>({...t,catalogPage:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Factory Code"}),(0,s.jsx)("input",{type:"text",value:y.factory_Code,onChange:e=>j(t=>({...t,factory_Code:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"e.g., Difficult assembly"})]})]})]}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Downloadable Content (Optional)"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Care Instructions URL"}),(0,s.jsx)("input",{type:"url",value:v.care,onChange:e=>N(t=>({...t,care:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"https://example.com/care-instructions.pdf"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Instructions URL"}),(0,s.jsx)("input",{type:"url",value:v.productInstructions,onChange:e=>N(t=>({...t,productInstructions:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"https://example.com/product-instructions.pdf"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CAD Files URL"}),(0,s.jsx)("input",{type:"url",value:v.cad,onChange:e=>N(t=>({...t,cad:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"https://example.com/cad-files.zip"})]})]})]}),i.submit&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:i.submit}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:l,className:"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"Saving...":e?"Update":"Create"})]})]})]})})}function i({product:e,onClose:t,onSuccess:r}){let[o,l]=(0,a.useState)(!1),[d,i]=(0,a.useState)(""),[c,m]=(0,a.useState)("set"),[u,x]=(0,a.useState)(0),p=async t=>{let s;switch(t.preventDefault(),i(""),c){case"set":s=u;break;case"increase":s=e.stock+u;break;case"decrease":s=e.stock-u;break;default:s=e.stock}if(s<0)return void i("Stock cannot be negative");l(!0);try{await n.jU.update.updateStock(e.id,s),r()}catch(e){console.error("Error updating stock:",e),i("Failed to update stock. Please try again.")}finally{l(!1)}};return(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,s.jsxs)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Update Stock"}),(0,s.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,s.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Current Stock: ",(0,s.jsx)("span",{className:"font-medium",children:e.stock})]})]}),(0,s.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Update Type"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",value:"set",checked:"set"===c,onChange:e=>m(e.target.value),className:"h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-900",children:"Set to specific amount"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",value:"increase",checked:"increase"===c,onChange:e=>m(e.target.value),className:"h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-900",children:"Increase by amount"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",value:"decrease",checked:"decrease"===c,onChange:e=>m(e.target.value),className:"h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-900",children:"Decrease by amount"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 mb-1",children:"set"===c?"New Stock Amount":"Amount to "+c}),(0,s.jsx)("input",{type:"number",id:"amount",min:"0",value:u,onChange:e=>x(parseInt(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"Enter amount",required:!0})]}),(0,s.jsx)("div",{className:"p-3 bg-blue-50 rounded-md",children:(0,s.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,s.jsx)("span",{className:"font-medium",children:"New stock will be: "}),(0,s.jsx)("span",{className:"font-bold",children:(()=>{switch(c){case"set":return u;case"increase":return e.stock+u;case"decrease":return Math.max(0,e.stock-u);default:return e.stock}})()})]})}),d&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:d}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,s.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:o,className:"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Updating...":"Update Stock"})]})]})]})})}function c(){let[e,t]=(0,a.useState)([]),[r,c]=(0,a.useState)([]),[m,u]=(0,a.useState)(!0),[x,p]=(0,a.useState)(!1),[b,h]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),[y,j]=(0,a.useState)(null),[v,N]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[C,I]=(0,a.useState)("all"),S=async()=>{try{u(!0);let[e,r]=await Promise.all([n.jU.get.getAll(),n.Yn.get.getAll()]);t(e),c(r)}catch(e){console.error("Error fetching data:",e)}finally{u(!1)}},P=e=>{f(e),p(!0)},D=e=>{j(e),h(!0)},L=async e=>{if(window.confirm("Are you sure you want to delete this product?"))try{await n.jU.delete.delete(e),await S()}catch(e){console.error("Error deleting product:",e),alert("Error deleting product. Please try again.")}},U=e=>{let t=r.find(t=>t.id===e);return t?.name||"Unknown"},_=e=>0===e?{label:"Out of Stock",color:"bg-red-100 text-red-800"}:e<10?{label:"Low Stock",color:"bg-yellow-100 text-yellow-800"}:{label:"In Stock",color:"bg-green-100 text-green-800"},A=e.filter(e=>{let t=e.name.toLowerCase().includes(v.toLowerCase())||(e.description?.toLowerCase().includes(v.toLowerCase())??!1),r=""===w||e.collectionId===w,s=!0;switch(C){case"inStock":s=e.stock>10;break;case"lowStock":s=e.stock>0&&e.stock<=10;break;case"outOfStock":s=0===e.stock}return t&&r&&s});return(0,s.jsx)(o.A,{children:(0,s.jsxs)(l.A,{children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Products Management"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage your product catalog and inventory"})]}),(0,s.jsxs)("button",{onClick:()=>{f(null),p(!0)},className:"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 transition-colors flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Product"]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Products"}),(0,s.jsx)("input",{type:"text",id:"search",value:v,onChange:e=>N(e.target.value),placeholder:"Search by name or description...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"collection",className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Collection"}),(0,s.jsxs)("select",{id:"collection",value:w,onChange:e=>k(""===e.target.value?"":Number(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",children:[(0,s.jsx)("option",{value:"",children:"All Collections"}),r.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," (Level ",e.level,")"]},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"stock",className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Stock"}),(0,s.jsxs)("select",{id:"stock",value:C,onChange:e=>I(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",children:[(0,s.jsx)("option",{value:"all",children:"All Products"}),(0,s.jsx)("option",{value:"inStock",children:"In Stock (> 10)"}),(0,s.jsx)("option",{value:"lowStock",children:"Low Stock (1-10)"}),(0,s.jsx)("option",{value:"outOfStock",children:"Out of Stock (0)"})]})]})]})}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:m?(0,s.jsxs)("div",{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading products..."})]}):(0,s.jsxs)("div",{className:"overflow-x-auto",children:[(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Product Code"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Collection"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Stock"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:A.map(e=>{let t=_(e.stock);return(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),e.description&&(0,s.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.productCode||`P-${e.id.toString().padStart(3,"0")}-AS`}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:U(e.collectionId)}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.price.toFixed(2)]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-sm text-gray-900 mr-2",children:e.stock}),(0,s.jsx)("button",{onClick:()=>D(e),className:"text-amber-600 hover:text-amber-900 text-xs",title:"Update stock",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})})]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${t.color}`,children:t.label})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,s.jsx)("button",{onClick:()=>P(e),className:"text-amber-600 hover:text-amber-900 mr-3",children:"Edit"}),(0,s.jsx)("button",{onClick:()=>L(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id)})})]}),0===A.length&&(0,s.jsx)("div",{className:"p-8 text-center text-gray-500",children:"No products found matching your criteria."})]})})]}),x&&(0,s.jsx)(d,{product:g,collections:r,onClose:()=>{p(!1),f(null)},onSuccess:()=>{p(!1),f(null),S()}}),b&&y&&(0,s.jsx)(i,{product:y,onClose:()=>{h(!1),j(null)},onSuccess:()=>{h(!1),j(null),S()}})]})})}r(97629)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72412:(e,t,r)=>{Promise.resolve().then(r.bind(r,51682))},79551:e=>{"use strict";e.exports=require("url")},81466:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>i});var s=r(65239),a=r(48088),o=r(88170),l=r.n(o),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let i={children:["",{children:["admin",{children:["dashboard",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,30657)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\products\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\products\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/products/page",pathname:"/admin/dashboard/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,72,658,913,190],()=>r(81466));module.exports=s})();