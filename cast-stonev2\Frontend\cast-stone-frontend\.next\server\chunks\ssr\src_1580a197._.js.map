{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/contexts/AdminAuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminAuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminAuthProvider() from the server but AdminAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AdminAuthContext.tsx <module evaluation>\",\n    \"AdminAuthProvider\",\n);\nexport const useAdminAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAdminAuth() from the server but useAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AdminAuthContext.tsx <module evaluation>\",\n    \"useAdminAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,mEACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,mEACA", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/contexts/AdminAuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AdminAuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AdminAuthProvider() from the server but AdminAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AdminAuthContext.tsx\",\n    \"AdminAuthProvider\",\n);\nexport const useAdminAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAdminAuth() from the server but useAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/AdminAuthContext.tsx\",\n    \"useAdminAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+CACA;AAEG,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+CACA", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/admin/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { AdminAuthProvider } from \"@/contexts/AdminAuthContext\";\n\nexport const metadata: Metadata = {\n  title: \"Cast Stone Admin - Dashboard\",\n  description: \"Admin dashboard for Cast Stone management\",\n};\n\nexport default function AdminLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className=\"antialiased bg-gray-50\">\n        <AdminAuthProvider>\n          {children}\n        </AdminAuthProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,YAAY,EAClC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAU;sBACd,cAAA,8OAAC,oIAAA,CAAA,oBAAiB;0BACf;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}