{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/admin/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAdminAuth } from '@/contexts/AdminAuthContext';\n\nexport default function AdminLoginPage() {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  \n  const { login, isAuthenticated, isLoading } = useAdminAuth();\n  const router = useRouter();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      router.push('/admin/dashboard');\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setIsSubmitting(true);\n\n    try {\n      const success = await login(email, password);\n      \n      if (success) {\n        router.push('/admin/dashboard');\n      } else {\n        setError('Invalid email or password. Please check your credentials.');\n      }\n    } catch (error) {\n      setError('An error occurred during login. Please try again.');\n      console.error('Login error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-white\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto\"></div>\n          <p className=\"mt-4 text-black\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-white\">\n      <div className=\"max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg border border-amber-200\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-amber-900 mb-2\">Cast Stone</h1>\n          <h2 className=\"text-2xl font-semibold text-black-800\">Admin Portal</h2>\n          <p className=\"mt-3 text-sm text-amber-700\">\n            Sign in to access the admin dashboard\n          </p>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-semibold text-amber-900 mb-2\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"block w-full px-4 py-3 border border-amber-300 rounded-lg shadow-sm placeholder-amber-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-semibold text-amber-900 mb-2\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"current-password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"block w-full px-4 py-3 border border-amber-300 rounded-lg shadow-sm placeholder-amber-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900\"\n                placeholder=\"Enter your password\"\n              />\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-semibold rounded-lg text-white bg-amber-900 hover:bg-amber-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-sm\"\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Signing in...\n                </div>\n              ) : (\n                'Sign in to Dashboard'\n              )}\n            </button>\n          </div>\n\n          <div className=\"text-center bg-amber-50 p-3 rounded-lg border border-amber-200\">\n            <p className=\"text-xs text-amber-800 font-medium\">\n              Demo Credentials\n            </p>\n            <p className=\"text-xs text-amber-700 mt-1\">\n              Email: <EMAIL><br />\n              Password: 132Trent@!\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,CAAC,WAAW;YACjC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,gBAAgB;QAEhB,IAAI;YACF,MAAM,UAAU,MAAM,MAAM,OAAO;YAEnC,IAAI,SAAS;gBACX,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,SAAS;YACT,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAkB;;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAK7C,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAkD;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAkD;;;;;;sDAGtF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;wBAKjB,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAIxF;;;;;;;;;;;sCAKN,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;;wCAA8B;sDACN,8OAAC;;;;;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}]}