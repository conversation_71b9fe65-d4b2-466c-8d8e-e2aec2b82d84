(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25225:e=>{e.exports={contactPage:"contact_contactPage__1rDu5",mainContent:"contact_mainContent__npFER",container:"contact_container__cCpH8",contentGrid:"contact_contentGrid__dNXoh",infoSection:"contact_infoSection__MaY7X",heroText:"contact_heroText__t7EIN",heroTitle:"contact_heroTitle__v1l17",contactCards:"contact_contactCards__9ANh_",contactCard:"contact_contactCard__0nlF6",cardIcon:"contact_cardIcon__jLVUe",cardContent:"contact_cardContent__VYY1f",cardTitle:"contact_cardTitle__HRUF6",cardSubtitle:"contact_cardSubtitle___v_dG",companyDescription:"contact_companyDescription__J145M",descriptionText:"contact_descriptionText__L0uF3",formSection:"contact_formSection__4UMy7",formCard:"contact_formCard__c5kgd",formTitle:"contact_formTitle__Oloqn",requiredField:"contact_requiredField__Pvrxi",contactForm:"contact_contactForm__erSNo",formGroup:"contact_formGroup__p53v_",label:"contact_label__f_aeS",input:"contact_input__NXGUw",select:"contact_select__hzDyt",textarea:"contact_textarea__nz_oc",disclaimer:"contact_disclaimer__6RtaZ",disclaimerText:"contact_disclaimerText__Tv_lx",submitButton:"contact_submitButton__Pottv",submitting:"contact_submitting__6tiZL",submitMessage:"contact_submitMessage__xZqfP",success:"contact_success__UwdEQ",error:"contact_error__fEMTu"}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43839:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var s=a(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73123:(e,t,a)=>{Promise.resolve().then(a.bind(a,43839))},76276:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=a(65239),n=a(48088),r=a(88170),o=a.n(r),i=a(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(t,c);let l={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,43839)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\contact\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},79551:e=>{"use strict";e.exports=require("url")},81581:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(60687),n=a(43210),r=a(25225),o=a.n(r),i=a(93112),c=a(81093);let l=()=>{let[e,t]=(0,n.useState)({name:"",email:"",phoneNumber:"",company:"",state:"",inquiry:"",message:""}),[a,r]=(0,n.useState)(!1),[l,d]=(0,n.useState)(null),m=[{value:c.e.ProductInquiry,label:"Product Inquiry"},{value:c.e.RequestDesignConsultation,label:"Request a Design Consultation"},{value:c.e.CustomOrders,label:"Custom Orders"},{value:c.e.TradePartnerships,label:"Trade Partnerships"},{value:c.e.InstallationSupport,label:"Installation Support"},{value:c.e.ShippingAndLeadTimes,label:"Shipping & Lead Times"},{value:c.e.RequestCatalogPriceList,label:"Request a Catalog / Price List"},{value:c.e.MediaPressInquiry,label:"Media / Press Inquiry"},{value:c.e.GeneralQuestions,label:"General Questions"}],u=e=>{let{name:a,value:s}=e.target;t(e=>({...e,[a]:"inquiry"===a?""===s?"":parseInt(s):s}))},p=async a=>{a.preventDefault(),r(!0),d(null);try{if(""===e.inquiry)throw Error("Please select an inquiry type");await i.contactFormPostService.submit({name:e.name,email:e.email,phoneNumber:e.phoneNumber,company:e.company||void 0,state:e.state,inquiry:e.inquiry,message:e.message}),t({name:"",email:"",phoneNumber:"",company:"",state:"",inquiry:"",message:""}),d({type:"success",text:"Thank you for your message! We will get back to you soon."})}catch(e){d({type:"error",text:e instanceof Error?e.message:"An error occurred. Please try again."})}finally{r(!1)}};return(0,s.jsx)("div",{className:o().contactPage,children:(0,s.jsx)("section",{className:o().mainContent,children:(0,s.jsx)("div",{className:o().container,children:(0,s.jsxs)("div",{className:o().contentGrid,children:[(0,s.jsxs)("div",{className:o().infoSection,children:[(0,s.jsx)("div",{className:o().heroText,children:(0,s.jsx)("h1",{className:o().heroTitle,children:"Get in touch with our cast stone specialists, design consultants, senior craftsmen, market research team, and more!"})}),(0,s.jsxs)("div",{className:o().contactCards,children:[(0,s.jsxs)("div",{className:o().contactCard,children:[(0,s.jsx)("div",{className:o().cardIcon,children:(0,s.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[(0,s.jsx)("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),(0,s.jsx)("circle",{cx:"12",cy:"10",r:"3"})]})}),(0,s.jsxs)("div",{className:o().cardContent,children:[(0,s.jsx)("p",{className:o().cardTitle,children:"1024 Broad Street"}),(0,s.jsx)("p",{className:o().cardSubtitle,children:"Guilford CT 06437 USA"})]})]}),(0,s.jsxs)("div",{className:o().contactCard,children:[(0,s.jsx)("div",{className:o().cardIcon,children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:(0,s.jsx)("path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"})})}),(0,s.jsx)("div",{className:o().cardContent,children:(0,s.jsx)("p",{className:o().cardTitle,children:"+1 203.453.0800"})})]})]}),(0,s.jsxs)("div",{className:o().companyDescription,children:[(0,s.jsx)("p",{className:o().descriptionText,children:"At Cast Stone, we always look forward to hearing from our clients, potential clients and business aviation industry colleagues."}),(0,s.jsx)("p",{className:o().descriptionText,children:"If you have a question or comment, please use the form to the right, or the contact details listed above."}),(0,s.jsx)("p",{className:o().descriptionText,children:"Since our founding in 2002, we've worked with some of the most sophisticated business and aviation leaders around the globe to help them acquire or sell their cast stone. We've grown from a reputable cast stone brokerage firm into a powerhouse business aviation consulting firm, helping Fortune 500 clients and high-net-worth individuals manage their cast stone assets in a portfolio."}),(0,s.jsx)("p",{className:o().descriptionText,children:"We look forward to hearing from you, and helping with your cast stone needs related to finance, valuation, appraisals, capital budgeting and transactions."})]})]}),(0,s.jsx)("div",{className:o().formSection,children:(0,s.jsxs)("div",{className:o().formCard,children:[(0,s.jsx)("h2",{className:o().formTitle,children:"Contact Us"}),(0,s.jsx)("p",{className:o().requiredField,children:"*Required Field"}),l&&(0,s.jsx)("div",{className:`${o().submitMessage} ${o()[l.type]}`,children:l.text}),(0,s.jsxs)("form",{onSubmit:p,className:o().contactForm,children:[(0,s.jsxs)("div",{className:o().formGroup,children:[(0,s.jsx)("label",{htmlFor:"name",className:o().label,children:"*Name"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:u,className:o().input,required:!0,placeholder:""})]}),(0,s.jsxs)("div",{className:o().formGroup,children:[(0,s.jsx)("label",{htmlFor:"email",className:o().label,children:"*Email"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:u,className:o().input,required:!0,placeholder:""})]}),(0,s.jsxs)("div",{className:o().formGroup,children:[(0,s.jsx)("label",{htmlFor:"phoneNumber",className:o().label,children:"*Phone Number"}),(0,s.jsx)("input",{type:"tel",id:"phoneNumber",name:"phoneNumber",value:e.phoneNumber,onChange:u,className:o().input,required:!0,placeholder:""})]}),(0,s.jsxs)("div",{className:o().formGroup,children:[(0,s.jsx)("label",{htmlFor:"company",className:o().label,children:"Company"}),(0,s.jsx)("input",{type:"text",id:"company",name:"company",value:e.company,onChange:u,className:o().input,placeholder:""})]}),(0,s.jsxs)("div",{className:o().formGroup,children:[(0,s.jsx)("label",{htmlFor:"state",className:o().label,children:"United States"}),(0,s.jsxs)("select",{id:"state",name:"state",value:e.state,onChange:u,className:o().select,required:!0,children:[(0,s.jsx)("option",{value:"",children:"State..."}),["Alabama","Alaska","Arizona","Arkansas","California","Colorado","Connecticut","Delaware","Florida","Georgia","Hawaii","Idaho","Illinois","Indiana","Iowa","Kansas","Kentucky","Louisiana","Maine","Maryland","Massachusetts","Michigan","Minnesota","Mississippi","Missouri","Montana","Nebraska","Nevada","New Hampshire","New Jersey","New Mexico","New York","North Carolina","North Dakota","Ohio","Oklahoma","Oregon","Pennsylvania","Rhode Island","South Carolina","South Dakota","Tennessee","Texas","Utah","Vermont","Virginia","Washington","West Virginia","Wisconsin","Wyoming"].map(e=>(0,s.jsx)("option",{value:e,children:e},e))]})]}),(0,s.jsxs)("div",{className:o().formGroup,children:[(0,s.jsx)("label",{htmlFor:"inquiry",className:o().label,children:"I'd Like To..."}),(0,s.jsxs)("select",{id:"inquiry",name:"inquiry",value:e.inquiry,onChange:u,className:o().select,required:!0,children:[(0,s.jsx)("option",{value:"",children:"Select..."}),m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsx)("div",{className:o().formGroup,children:(0,s.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:u,className:o().textarea,rows:4,placeholder:"Type your message here...",required:!0,minLength:10,maxLength:2e3})}),(0,s.jsxs)("div",{className:o().disclaimer,children:[(0,s.jsx)("p",{className:o().disclaimerText,children:"By submitting your information, you acknowledge that you may be sent marketing material and newsletters."}),(0,s.jsx)("p",{className:o().disclaimerText,children:"Your information is secure and will never be shared with anyone. View our Privacy Policy"})]}),(0,s.jsx)("button",{type:"submit",disabled:a,className:`${o().submitButton} ${a?o().submitting:""}`,children:a?"CONNECTING...":"LET'S CONNECT"})]})]})})]})})})})}},87195:(e,t,a)=>{Promise.resolve().then(a.bind(a,81581))}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,72,658,913],()=>a(76276));module.exports=s})();