(()=>{var e={};e.id=723,e.ids=[723],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3960:(e,t,l)=>{"use strict";l.r(t),l.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=l(65239),s=l(48088),a=l(88170),o=l.n(a),n=l(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);l.d(t,i);let d={children:["",{children:["admin",{children:["dashboard",{children:["collections",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,26589)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\collections\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,99111)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(l.bind(l,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(l.t.bind(l,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(l.t.bind(l,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\collections\\page.tsx"],m={require:l,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/dashboard/collections/page",pathname:"/admin/dashboard/collections",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12181:(e,t,l)=>{Promise.resolve().then(l.bind(l,26589))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26589:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>r});let r=(0,l(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\collections\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\collections\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54037:(e,t,l)=>{Promise.resolve().then(l.bind(l,55659))},55659:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>c});var r=l(60687),s=l(43210),a=l(83645),o=l(73441),n=l(63968),i=l(3844);function d({collection:e,onClose:t,onSuccess:l}){let{admin:a}=(0,i.b)(),[o,d]=(0,s.useState)(!1),[c,m]=(0,s.useState)({}),[x,b]=(0,s.useState)([]),[u,p]=(0,s.useState)([]),[h,g]=(0,s.useState)(!1),[v,f]=(0,s.useState)({name:"",description:"",level:1,parentCollectionId:null,childCollectionIds:[],tags:[],images:[],published:!1}),[j,N]=(0,s.useState)(""),[y,w]=(0,s.useState)(""),k=()=>{let t={};if(v.name.trim()?v.name.length>200&&(t.name="Name must be less than 200 characters"):t.name="Name is required",v.description&&v.description.length>1e3&&(t.description="Description must be less than 1000 characters"),1===v.level&&v.parentCollectionId&&(t.parentCollectionId="Level 1 collections cannot have a parent"),v.level>1&&!v.parentCollectionId&&(t.parentCollectionId=`Level ${v.level} collections must have a parent`),v.parentCollectionId){let e=x.find(e=>e.id===v.parentCollectionId);if(e){let l=v.level-1;e.level!==l&&(t.parentCollectionId=`Parent collection must be Level ${l}`)}}return e&&v.parentCollectionId===e.id&&(t.parentCollectionId="Collection cannot be its own parent"),m(t),0===Object.keys(t).length},C=async t=>{if(t.preventDefault(),k()){d(!0);try{if(e){let t={...v,parentCollectionId:v.parentCollectionId??void 0,childCollectionIds:v.childCollectionIds,updatedBy:a?.email||"admin"};await n.Yn.update.update(e.id,t)}else{let e={...v,parentCollectionId:v.parentCollectionId??void 0,childCollectionIds:v.childCollectionIds,createdBy:a?.email||"admin"};await n.Yn.post.create(e)}l()}catch(e){console.error("Error saving collection:",e),m({submit:"Failed to save collection. Please try again."})}finally{d(!1)}}},I=()=>{j.trim()&&!v.tags.includes(j.trim())&&(f(e=>({...e,tags:[...e.tags,j.trim()]})),N(""))},L=e=>{f(t=>({...t,tags:t.tags.filter(t=>t!==e)}))},P=async()=>{y.trim()&&!v.images.includes(y.trim())&&(await M(y.trim())?(f(e=>({...e,images:[...e.images,y.trim()]})),w(""),m(e=>({...e,imageInput:""}))):m(e=>({...e,imageInput:"Invalid image URL or image not found in uploaded images"})))},D=e=>{v.images.includes(e)||f(t=>({...t,images:[...t.images,e]}))},S=e=>{f(t=>({...t,images:t.images.filter(t=>t!==e)}))},M=async e=>!!u.some(t=>t.secureUrl===e)||new Promise(t=>{let l=new Image;l.onload=()=>t(!0),l.onerror=()=>t(!1),l.src=e,setTimeout(()=>t(!1),5e3)}),F=()=>{let t=v.level+1;return x.filter(l=>l.level===t&&(!e||l.id!==e.id)&&(!v.parentCollectionId||l.parentCollectionId===(e?.id||null)))};return(0,r.jsx)("div",{className:"fixed inset-0 bg-amber-900 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsxs)("div",{className:"relative top-20 mx-auto p-6 border border-amber-200 w-full max-w-3xl shadow-xl rounded-lg bg-white",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-amber-900",children:e?"Edit Collection":"Add New Collection"}),(0,r.jsx)("button",{onClick:t,className:"text-amber-600 hover:text-amber-800 transition-colors",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-semibold text-amber-900 mb-2",children:"Collection Name *"}),(0,r.jsx)("input",{type:"text",id:"name",value:v.name,onChange:e=>f(t=>({...t,name:e.target.value})),className:`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900 placeholder-amber-400 ${c.name?"border-red-500":"border-amber-300"}`,placeholder:"Enter collection name"}),c.name&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-semibold text-amber-900 mb-2",children:"Description"}),(0,r.jsx)("textarea",{id:"description",value:v.description,onChange:e=>f(t=>({...t,description:e.target.value})),rows:3,className:`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900 placeholder-amber-400 ${c.description?"border-red-500":"border-amber-300"}`,placeholder:"Enter collection description"}),c.description&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c.description})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"level",className:"block text-sm font-semibold text-amber-900 mb-2",children:"Hierarchy Level *"}),(0,r.jsxs)("select",{id:"level",value:v.level,onChange:e=>{let t=Number(e.target.value);f(e=>({...e,level:t,parentCollectionId:1===t?null:e.parentCollectionId,childCollectionIds:3===t?[]:e.childCollectionIds}))},className:"w-full px-4 py-3 border border-amber-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900",children:[(0,r.jsx)("option",{value:1,children:"Level 1 (Root Category)"}),(0,r.jsx)("option",{value:2,children:"Level 2 (Sub Category)"}),(0,r.jsx)("option",{value:3,children:"Level 3 (Specific Type)"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[v.level>1&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"parentCollection",className:"block text-sm font-semibold text-amber-900 mb-2",children:"Parent Collection *"}),(0,r.jsxs)("select",{id:"parentCollection",value:v.parentCollectionId||"",onChange:e=>f(t=>({...t,parentCollectionId:e.target.value?Number(e.target.value):null})),className:`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900 ${c.parentCollectionId?"border-red-500":"border-amber-300"}`,children:[(0,r.jsx)("option",{value:"",className:"text-amber-600",children:"Select parent collection"}),(()=>{let t=v.level-1;return x.filter(l=>l.level===t&&(!e||l.id!==e.id))})().map(e=>(0,r.jsxs)("option",{value:e.id,className:"text-amber-900",children:[e.name," (Level ",e.level,")"]},e.id))]}),c.parentCollectionId&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c.parentCollectionId})]}),v.level<3&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-amber-900 mb-2",children:"Child Collections (Optional)"}),(0,r.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto border border-amber-300 rounded-lg p-3",children:0===F().length?(0,r.jsx)("p",{className:"text-amber-600 text-sm",children:"No available child collections"}):F().map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:v.childCollectionIds.includes(e.id),onChange:t=>{f(l=>({...l,childCollectionIds:t.target.checked?[...l.childCollectionIds,e.id]:l.childCollectionIds.filter(t=>t!==e.id)}))},className:"rounded border-amber-300 text-amber-600 focus:ring-amber-500"}),(0,r.jsxs)("span",{className:"text-amber-900 text-sm",children:[e.name," (Level ",e.level,")"]})]},e.id))}),(0,r.jsx)("p",{className:"mt-1 text-xs text-amber-700",children:"Select multiple child collections to link to this collection"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-amber-900 mb-2",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-3",children:v.tags.map((e,t)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-amber-100 text-amber-800 border border-amber-200",children:[e,(0,r.jsx)("button",{type:"button",onClick:()=>L(e),className:"ml-2 text-amber-600 hover:text-amber-800 font-bold",children:"\xd7"})]},t))}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"text",value:j,onChange:e=>N(e.target.value),onKeyDown:e=>"Enter"===e.key&&(e.preventDefault(),I()),className:"flex-1 px-4 py-3 border border-amber-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-900 placeholder-amber-400",placeholder:"Add a tag"}),(0,r.jsx)("button",{type:"button",onClick:I,className:"px-6 py-3 bg-amber-900 text-white rounded-r-lg hover:bg-amber-800 transition-colors font-medium",children:"Add"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-amber-900 mb-2",children:"Images"}),(0,r.jsx)("div",{className:"space-y-3 mb-4",children:v.images.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 bg-gray-50 p-3 rounded-lg border",children:[(0,r.jsx)("img",{src:e,alt:`Collection image ${t+1}`,className:"w-16 h-16 object-cover rounded-md border border-gray-200",onError:e=>{e.target.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K"}}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900 truncate",children:["Image ",t+1]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",title:e,children:e})]}),(0,r.jsx)("button",{type:"button",onClick:()=>S(e),className:"flex-shrink-0 p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded",title:"Remove image",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},t))}),(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:"Choose from uploaded images:"}),(0,r.jsxs)("select",{onChange:e=>e.target.value&&D(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",disabled:h,value:"",children:[(0,r.jsx)("option",{value:"",children:h?"Loading images...":"Select an uploaded image"}),u.map(e=>(0,r.jsx)("option",{value:e.secureUrl,children:e.fileName},e.publicId))]}),0===u.length&&!h&&(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["No uploaded images found. ",(0,r.jsx)("a",{href:"/admin/dashboard/images",target:"_blank",className:"text-amber-600 hover:text-amber-800",children:"Upload images here"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:"Or enter image URL manually:"}),(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("input",{type:"url",value:y,onChange:e=>{w(e.target.value),c.imageInput&&m(e=>({...e,imageInput:""}))},onKeyDown:e=>"Enter"===e.key&&(e.preventDefault(),P()),className:`flex-1 px-3 py-2 border rounded-l-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 ${c.imageInput?"border-red-500":"border-gray-300"}`,placeholder:"Paste image URL here or copy from Images section"}),(0,r.jsx)("button",{type:"button",onClick:P,className:"px-4 py-2 bg-amber-900 text-white rounded-r-md hover:bg-amber-800 disabled:opacity-50",disabled:!y.trim(),children:"Add"})]}),c.imageInput&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c.imageInput}),(0,r.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Tip: You can copy image URLs from the ",(0,r.jsx)("a",{href:"/admin/dashboard/images",target:"_blank",className:"text-amber-600 hover:text-amber-800",children:"Images section"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center p-4 bg-amber-50 rounded-lg border border-amber-200",children:[(0,r.jsx)("input",{type:"checkbox",id:"published",checked:v.published,onChange:e=>f(t=>({...t,published:e.target.checked})),className:"h-5 w-5 text-amber-600 focus:ring-amber-500 border-amber-300 rounded"}),(0,r.jsx)("label",{htmlFor:"published",className:"ml-3 block text-sm font-medium text-amber-900",children:"Published (visible to customers)"})]}),c.submit&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:c.submit}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t border-amber-200",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"px-6 py-3 border border-amber-300 rounded-lg text-amber-800 hover:bg-amber-50 transition-colors font-medium",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:o,className:"px-6 py-3 bg-amber-900 text-white rounded-lg hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium shadow-sm",children:o?"Saving...":e?"Update Collection":"Create Collection"})]})]})]})})}function c(){let[e,t]=(0,s.useState)([]),[l,i]=(0,s.useState)(!0),[c,m]=(0,s.useState)(!1),[x,b]=(0,s.useState)(null),[u,p]=(0,s.useState)(""),[h,g]=(0,s.useState)(""),v=async()=>{try{i(!0);let e=await n.Yn.get.getAll();t(e)}catch(e){console.error("Error fetching collections:",e)}finally{i(!1)}},f=e=>{b(e),m(!0)},j=async e=>{if(window.confirm("Are you sure you want to delete this collection?"))try{await n.Yn.delete.delete(e),await v()}catch(e){console.error("Error deleting collection:",e),alert("Error deleting collection. Please try again.")}},N=e.filter(e=>{let t=e.name.toLowerCase().includes(u.toLowerCase())||(e.description?.toLowerCase().includes(u.toLowerCase())??!1),l=""===h||e.level===h;return t&&l}),y=e=>{switch(e){case 1:return"bg-blue-100 text-blue-800";case 2:return"bg-green-100 text-green-800";case 3:return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},w=t=>{if(!t)return"-";let l=e.find(e=>e.id===t);return l?.name||"Unknown"};return(0,r.jsx)(a.A,{children:(0,r.jsxs)(o.A,{children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center bg-white p-6 rounded-lg shadow-sm border border-black-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-black",children:"Collections Management"}),(0,r.jsx)("p",{className:"text-black mt-1",children:"Manage your product collections and hierarchy"})]}),(0,r.jsxs)("button",{onClick:()=>{b(null),m(!0)},className:"px-6 py-3 bg-black text-white rounded-lg hover:bg-black transition-colors flex items-center shadow-sm font-medium",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Collection"]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-black-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-black mb-4",children:"Filter Collections"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-semibold text-black mb-2",children:"Search Collections"}),(0,r.jsx)("input",{type:"text",id:"search",value:u,onChange:e=>p(e.target.value),placeholder:"Search by name or description...",className:"w-full px-4 py-3 border border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-black text-black placeholder-black"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"level",className:"block text-sm font-semibold text-black mb-2",children:"Filter by Level"}),(0,r.jsxs)("select",{id:"level",value:h,onChange:e=>g(""===e.target.value?"":Number(e.target.value)),className:"w-full px-4 py-3 border border-black rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-black text-black",children:[(0,r.jsx)("option",{value:"",className:"text-black",children:"All Levels"}),(0,r.jsx)("option",{value:1,className:"text-black",children:"Level 1 (Root)"}),(0,r.jsx)("option",{value:2,className:"text-black",children:"Level 2 (Category)"}),(0,r.jsx)("option",{value:3,className:"text-black",children:"Level 3 (Subcategory)"})]})]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-black overflow-hidden",children:l?(0,r.jsxs)("div",{className:"p-12 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-4 border-black mx-auto"}),(0,r.jsx)("p",{className:"mt-6 text-black font-medium",children:"Loading collections..."})]}):(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-black",children:[(0,r.jsx)("thead",{className:"bg-black",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",children:"Collection Name"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",children:"Level"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",children:"Parent Collection"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",children:"Products"}),(0,r.jsx)("th",{className:"px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider",children:"Created"}),(0,r.jsx)("th",{className:"px-6 py-4 text-right text-xs font-bold text-white uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-black",children:N.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-black-25 transition-colors",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-black",children:e.name}),e.description&&(0,r.jsx)("div",{className:"text-sm text-black truncate max-w-xs mt-1",children:e.description})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${y(e.level)}`,children:["Level ",e.level]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-black",children:w(e.parentCollectionId||null)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-3 py-1 text-xs font-bold rounded-full ${e.published?"bg-green-100 text-green-800 border border-green-200":"bg-yellow-100 text-yellow-800 border border-yellow-200"}`,children:e.published?"Published":"Draft"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-semibold text-black",children:e.productCount??0}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-black",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{onClick:()=>f(e),className:"text-black hover:text-black mr-4 font-medium",children:"Edit"}),(0,r.jsx)("button",{onClick:()=>j(e.id),className:"text-red-600 hover:text-red-800 font-medium",children:"Delete"})]})]},e.id))})]}),0===N.length&&(0,r.jsxs)("div",{className:"p-12 text-center",children:[(0,r.jsx)("div",{className:"text-black mb-2",children:(0,r.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,r.jsx)("p",{className:"text-black font-medium",children:"No collections found matching your criteria."}),(0,r.jsx)("p",{className:"text-black text-sm mt-1",children:"Try adjusting your search or filters."})]})]})})]}),c&&(0,r.jsx)(d,{collection:x,onClose:()=>{m(!1),b(null)},onSuccess:()=>{m(!1),b(null),v()}})]})})}l(97629)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../../webpack-runtime.js");t.C(e);var l=e=>t(t.s=e),r=t.X(0,[447,72,658,913,190],()=>l(3960));module.exports=r})();