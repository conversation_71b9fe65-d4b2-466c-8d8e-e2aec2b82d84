(()=>{var e={};e.id=5,e.ids=[5],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7489:(e,t,r)=>{Promise.resolve().then(r.bind(r,70905))},10213:e=>{e.exports={cartSummary:"cartSummary_cartSummary__PM5yR",title:"cartSummary_title__l6YX6",summaryDetails:"cartSummary_summaryDetails__cGGHo",summaryRow:"cartSummary_summaryRow__Lop8o",label:"cartSummary_label__n6ron",value:"cartSummary_value__z2N6i",freeShipping:"cartSummary_freeShipping__eWQek",divider:"cartSummary_divider__jEcc5",totalRow:"cartSummary_totalRow__T4448",totalLabel:"cartSummary_totalLabel__qcTAD",totalValue:"cartSummary_totalValue__Si5DE",shippingNotice:"cartSummary_shippingNotice__aDQlb",infoIcon:"cartSummary_infoIcon__KcEfs",actionButtons:"cartSummary_actionButtons__LZNm3",checkoutBtn:"cartSummary_checkoutBtn__A89Dz",checkoutIcon:"cartSummary_checkoutIcon__i4QE1",clearBtn:"cartSummary_clearBtn__mM17Y",clearIcon:"cartSummary_clearIcon__3ikKz",securityNotice:"cartSummary_securityNotice__IB7af",securityIcon:"cartSummary_securityIcon__SyTAt"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12803:e=>{e.exports={container:"cart_container__mmZJa",header:"cart_header__VbCA5",title:"cart_title__wnY7m",subtitle:"cart_subtitle__gq0tF",loadingContainer:"cart_loadingContainer__3TrNS",loadingSpinner:"cart_loadingSpinner__5WUeB",spin:"cart_spin__Xpgtc",emptyCart:"cart_emptyCart__LW7EU",emptyIcon:"cart_emptyIcon__diAeW",emptyTitle:"cart_emptyTitle__yVyOl",emptyMessage:"cart_emptyMessage___j1xz",shopNowBtn:"cart_shopNowBtn__7q4K_",shopIcon:"cart_shopIcon__ks_ok",errorMessage:"cart_errorMessage__8Yd9y",errorIcon:"cart_errorIcon__dDZVh",cartContent:"cart_cartContent__U7WSv",cartItems:"cart_cartItems__BCkIF",itemsHeader:"cart_itemsHeader__ydCSW",continueShoppingLink:"cart_continueShoppingLink__bSFfe",itemsList:"cart_itemsList__3sgQN",cartSummaryContainer:"cart_cartSummaryContainer__8KdB1",additionalActions:"cart_additionalActions__PVxVt",helpSection:"cart_helpSection__NrbBI",shippingInfo:"cart_shippingInfo__htCzI",contactLink:"cart_contactLink__J9OU5"}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20641:(e,t,r)=>{Promise.resolve().then(r.bind(r,90656))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=r(65239),s=r(48088),c=r(88170),n=r.n(c),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70905)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\cart\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\cart\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},90656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(60687),s=r(43210),c=r(85814),n=r.n(c),i=r(28253),o=r(96509),l=r.n(o);let d=({item:e})=>{let{updateCartItem:t,removeFromCart:r,state:c}=(0,i._)(),[n,o]=(0,s.useState)(!1),[d,m]=(0,s.useState)(!1),u=async r=>{if(!(r<1)&&e.product){if(r>e.product.stock)return void alert(`Only ${e.product.stock} items available in stock`);try{o(!0),await t(e.productId,r)}catch(e){console.error("Error updating quantity:",e)}finally{o(!1)}}},p=async()=>{try{m(!0),await r(e.productId)}catch(e){console.error("Error removing item:",e)}finally{m(!1)}},_=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);if(!e.product)return null;let h=e.product.images&&e.product.images.length>0?e.product.images[0]:"/images/placeholder-product.jpg",x=e.quantity*e.product.price;return(0,a.jsxs)("div",{className:l().cartItem,children:[(0,a.jsx)("div",{className:l().imageContainer,children:(0,a.jsx)("img",{src:h,alt:e.product.name,className:l().productImage})}),(0,a.jsxs)("div",{className:l().productDetails,children:[(0,a.jsx)("h3",{className:l().productName,children:e.product.name}),e.product.description&&(0,a.jsx)("p",{className:l().productDescription,children:e.product.description.length>150?`${e.product.description.substring(0,150)}...`:e.product.description}),(0,a.jsxs)("div",{className:l().productMeta,children:[(0,a.jsxs)("span",{className:l().unitPrice,children:[_(e.product.price)," each"]}),e.product.collection&&(0,a.jsx)("span",{className:l().collection,children:e.product.collection.name})]}),(0,a.jsx)("div",{className:l().stockStatus,children:e.product.stock>0?(0,a.jsx)("span",{className:l().inStock,children:e.product.stock>10?"In Stock":`Only ${e.product.stock} left`}):(0,a.jsx)("span",{className:l().outOfStock,children:"Out of Stock"})})]}),(0,a.jsxs)("div",{className:l().quantitySection,children:[(0,a.jsx)("label",{className:l().quantityLabel,children:"Quantity"}),(0,a.jsxs)("div",{className:l().quantityControls,children:[(0,a.jsx)("button",{type:"button",onClick:()=>u(e.quantity-1),disabled:e.quantity<=1||n||c.isLoading,className:l().quantityBtn,children:"-"}),(0,a.jsx)("span",{className:l().quantity,children:e.quantity}),(0,a.jsx)("button",{type:"button",onClick:()=>u(e.quantity+1),disabled:e.quantity>=e.product.stock||n||c.isLoading,className:l().quantityBtn,children:"+"})]}),n&&(0,a.jsx)("span",{className:l().updating,children:"Updating..."})]}),(0,a.jsxs)("div",{className:l().priceSection,children:[(0,a.jsx)("div",{className:l().itemTotal,children:_(x)}),(0,a.jsxs)("div",{className:l().priceBreakdown,children:[e.quantity," \xd7 ",_(e.product.price)]})]}),(0,a.jsx)("div",{className:l().removeSection,children:(0,a.jsx)("button",{onClick:p,disabled:d||c.isLoading,className:l().removeBtn,title:"Remove from cart",children:d?(0,a.jsx)("span",{className:l().removing,children:"..."}):(0,a.jsxs)("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[(0,a.jsx)("path",{d:"M3 6h18"}),(0,a.jsx)("path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"}),(0,a.jsx)("path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"}),(0,a.jsx)("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),(0,a.jsx)("line",{x1:"14",y1:"11",x2:"14",y2:"17"})]})})})]})};var m=r(10213),u=r.n(m);let p=({showCheckoutButton:e=!0,showClearButton:t=!0})=>{let{state:r,clearCart:s}=(0,i._)(),c=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),o=async()=>{window.confirm("Are you sure you want to clear your cart?")&&await s()};if(!r.cart||0===r.cart.cartItems.length)return null;let l=r.cart.totalAmount,d=.08*l,m=l>100?0:15;return(0,a.jsxs)("div",{className:u().cartSummary,children:[(0,a.jsx)("h2",{className:u().title,children:"Order Summary"}),(0,a.jsxs)("div",{className:u().summaryDetails,children:[(0,a.jsxs)("div",{className:u().summaryRow,children:[(0,a.jsxs)("span",{className:u().label,children:["Items (",r.cart.totalItems,")"]}),(0,a.jsx)("span",{className:u().value,children:c(l)})]}),(0,a.jsxs)("div",{className:u().summaryRow,children:[(0,a.jsxs)("span",{className:u().label,children:["Shipping",0===m&&(0,a.jsx)("span",{className:u().freeShipping,children:" (Free)"})]}),(0,a.jsx)("span",{className:u().value,children:0===m?"Free":c(m)})]}),(0,a.jsxs)("div",{className:u().summaryRow,children:[(0,a.jsx)("span",{className:u().label,children:"Tax"}),(0,a.jsx)("span",{className:u().value,children:c(d)})]}),(0,a.jsx)("div",{className:u().divider}),(0,a.jsxs)("div",{className:u().totalRow,children:[(0,a.jsx)("span",{className:u().totalLabel,children:"Total"}),(0,a.jsx)("span",{className:u().totalValue,children:c(l+d+m)})]})]}),m>0&&(0,a.jsxs)("div",{className:u().shippingNotice,children:[(0,a.jsxs)("svg",{className:u().infoIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[(0,a.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,a.jsx)("path",{d:"M12 16v-4"}),(0,a.jsx)("path",{d:"M12 8h.01"})]}),(0,a.jsxs)("span",{children:["Add ",c(100-l)," more for free shipping"]})]}),(0,a.jsxs)("div",{className:u().actionButtons,children:[e&&(0,a.jsxs)(n(),{href:"/checkout",className:u().checkoutBtn,children:[(0,a.jsx)("svg",{className:u().checkoutIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,a.jsx)("path",{d:"M9 12l2 2 4-4"})}),"Proceed to Checkout"]}),t&&(0,a.jsxs)("button",{onClick:o,disabled:r.isLoading,className:u().clearBtn,children:[(0,a.jsxs)("svg",{className:u().clearIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[(0,a.jsx)("path",{d:"M3 6h18"}),(0,a.jsx)("path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"}),(0,a.jsx)("path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"})]}),"Clear Cart"]})]}),(0,a.jsxs)("div",{className:u().securityNotice,children:[(0,a.jsxs)("svg",{className:u().securityIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[(0,a.jsx)("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"}),(0,a.jsx)("path",{d:"M9 12l2 2 4-4"})]}),(0,a.jsx)("span",{children:"Secure Checkout"})]})]})};var _=r(12803),h=r.n(_);function x(){let{state:e}=(0,i._)();return e.isLoading?(0,a.jsx)("div",{className:h().container,children:(0,a.jsxs)("div",{className:h().loadingContainer,children:[(0,a.jsx)("div",{className:h().loadingSpinner}),(0,a.jsx)("p",{children:"Loading your cart..."})]})}):e.cart&&0!==e.cart.cartItems.length?(0,a.jsxs)("div",{className:h().container,children:[(0,a.jsxs)("div",{className:h().header,children:[(0,a.jsx)("h1",{className:h().title,children:"Shopping Cart"}),(0,a.jsxs)("p",{className:h().subtitle,children:[e.cart.totalItems," ",1===e.cart.totalItems?"item":"items"," in your cart"]})]}),e.error&&(0,a.jsxs)("div",{className:h().errorMessage,children:[(0,a.jsxs)("svg",{className:h().errorIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:[(0,a.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,a.jsx)("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),(0,a.jsx)("line",{x1:"9",y1:"9",x2:"15",y2:"15"})]}),(0,a.jsx)("span",{children:e.error})]}),(0,a.jsxs)("div",{className:h().cartContent,children:[(0,a.jsxs)("div",{className:h().cartItems,children:[(0,a.jsxs)("div",{className:h().itemsHeader,children:[(0,a.jsx)("h2",{children:"Items"}),(0,a.jsx)(n(),{href:"/products",className:h().continueShoppingLink,children:"Continue Shopping"})]}),(0,a.jsx)("div",{className:h().itemsList,children:e.cart.cartItems.map(e=>(0,a.jsx)(d,{item:e},e.id))})]}),(0,a.jsx)("div",{className:h().cartSummaryContainer,children:(0,a.jsx)(p,{})})]}),(0,a.jsxs)("div",{className:h().additionalActions,children:[(0,a.jsxs)("div",{className:h().helpSection,children:[(0,a.jsx)("h3",{children:"Need Help?"}),(0,a.jsxs)("p",{children:["Have questions about your order?",(0,a.jsx)(n(),{href:"/contact",className:h().contactLink,children:" Contact us"}),"or call (555) 123-4567"]})]}),(0,a.jsxs)("div",{className:h().shippingInfo,children:[(0,a.jsx)("h3",{children:"Shipping Information"}),(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:"Free shipping on orders over $100"}),(0,a.jsx)("li",{children:"Standard delivery: 5-7 business days"}),(0,a.jsx)("li",{children:"Express delivery: 2-3 business days"})]})]})]})]}):(0,a.jsx)("div",{className:h().container,children:(0,a.jsxs)("div",{className:h().emptyCart,children:[(0,a.jsx)("div",{className:h().emptyIcon,children:(0,a.jsx)("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,a.jsx)("path",{d:"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7"})})}),(0,a.jsx)("h1",{className:h().emptyTitle,children:"Your Cart is Empty"}),(0,a.jsx)("p",{className:h().emptyMessage,children:"Looks like you haven't added any items to your cart yet. Start shopping to fill it up!"}),(0,a.jsxs)(n(),{href:"/products",className:h().shopNowBtn,children:[(0,a.jsx)("svg",{className:h().shopIcon,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:(0,a.jsx)("path",{d:"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7"})}),"Start Shopping"]})]})})}},96509:e=>{e.exports={cartItem:"cartItem_cartItem__T4bmR",imageContainer:"cartItem_imageContainer__yb0KC",productImage:"cartItem_productImage__kAA5f",productDetails:"cartItem_productDetails__9ltPm",productName:"cartItem_productName__pSe71",productDescription:"cartItem_productDescription__c9A81",productMeta:"cartItem_productMeta__0r622",unitPrice:"cartItem_unitPrice__zQRWt",collection:"cartItem_collection__jbAg5",stockStatus:"cartItem_stockStatus__QQDuR",inStock:"cartItem_inStock__geJjN",outOfStock:"cartItem_outOfStock__52u8u",quantitySection:"cartItem_quantitySection__0FS_N",quantityLabel:"cartItem_quantityLabel__KUNoK",quantityControls:"cartItem_quantityControls__9eHsF",quantityBtn:"cartItem_quantityBtn__M9eRY",quantity:"cartItem_quantity__fPAU_",updating:"cartItem_updating__9ejaG",priceSection:"cartItem_priceSection__YFfmz",itemTotal:"cartItem_itemTotal__T_6ai",priceBreakdown:"cartItem_priceBreakdown__iM3Bb",removeSection:"cartItem_removeSection__ebq_G",removeBtn:"cartItem_removeBtn__PfY2F",removing:"cartItem_removing__Ar9Z4"}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,72,658,913],()=>r(43750));module.exports=a})();