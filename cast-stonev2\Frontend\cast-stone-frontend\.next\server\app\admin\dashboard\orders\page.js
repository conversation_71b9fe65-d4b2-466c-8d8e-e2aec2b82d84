(()=>{var e={};e.id=823,e.ids=[823],e.modules={1255:(e,t,s)=>{Promise.resolve().then(s.bind(s,41210))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3844:(e,t,s)=>{"use strict";s.d(t,{AdminAuthProvider:()=>i,b:()=>l});var r=s(60687),a=s(43210),n=s(58890);let d=(0,a.createContext)(void 0);function i({children:e}){let[t,s]=(0,a.useState)(null),[i,l]=(0,a.useState)(!0),o=async(e,t)=>{l(!0);try{let r=await n.userGetService.validateAdminCredentials(e,t);if(r)return s(r),localStorage.setItem("admin_session",JSON.stringify(r)),!0;return!1}catch(e){return console.error("Login error:",e),!1}finally{l(!1)}};return(0,r.jsx)(d.Provider,{value:{admin:t,isLoading:i,login:o,logout:()=>{s(null),localStorage.removeItem("admin_session")},isAuthenticated:!!t},children:e})}function l(){let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAdminAuth must be used within an AdminAuthProvider");return e}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17722:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=s(65239),a=s(48088),n=s(88170),d=s.n(n),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let o={children:["",{children:["admin",{children:["dashboard",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46347)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\orders\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\orders\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/orders/page",pathname:"/admin/dashboard/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40575:(e,t,s)=>{Promise.resolve().then(s.bind(s,3844))},41210:(e,t,s)=>{"use strict";s.d(t,{AdminAuthProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AdminAuthProvider() from the server but AdminAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx","AdminAuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAdminAuth() from the server but useAdminAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\contexts\\AdminAuthContext.tsx","useAdminAuth")},43911:(e,t,s)=>{Promise.resolve().then(s.bind(s,52781))},46347:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\cast-stonev2\\\\cast-stonev2\\\\Frontend\\\\cast-stone-frontend\\\\src\\\\app\\\\admin\\\\dashboard\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Frontend\\cast-stone-frontend\\src\\app\\admin\\dashboard\\orders\\page.tsx","default")},52781:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687),a=s(43210),n=s(83645),d=s(73441);function i({order:e,onClose:t}){return(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsxs)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-gray-900",children:["Order Details - #",e.id]}),(0,r.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Order Information"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Order ID:"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["#",e.id]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Status:"}),(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(e=>{switch(e){case 1:return"bg-yellow-100 text-yellow-800";case 2:return"bg-blue-100 text-blue-800";case 3:return"bg-purple-100 text-purple-800";case 4:return"bg-green-100 text-green-800";case 5:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.statusId)}`,children:{1:"Pending",2:"Processing",3:"Shipped",4:"Delivered",5:"Cancelled"}[e.statusId]||"Unknown"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Total Amount:"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:["$",e.totalAmount.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Payment Method:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.paymentMethod||"Not specified"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Order Date:"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[new Date(e.createdAt).toLocaleDateString()," at ",new Date(e.createdAt).toLocaleTimeString()]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Customer Information"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Email:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.email})]}),e.phoneNumber&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Phone:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.phoneNumber})]}),e.country&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Country:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.country})]}),e.city&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"City:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.city})]}),e.zipCode&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Zip Code:"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.zipCode})]})]})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 mb-3",children:"Order Items"}),e.orderItems&&e.orderItems.length>0?(0,r.jsxs)("div",{className:"space-y-3",children:[e.orderItems.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white p-3 rounded border",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h5",{className:"text-sm font-medium text-gray-900",children:e.product?.name||`Product ID: ${e.productId}`}),e.product?.description&&(0,r.jsx)("p",{className:"text-xs text-gray-600 mt-1 truncate",children:e.product.description})]}),(0,r.jsxs)("div",{className:"text-right ml-4",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["$",e.priceAtPurchaseTime.toFixed(2)]}),(0,r.jsxs)("div",{className:"text-xs text-gray-600",children:["Qty: ",e.quantity]})]})]}),(0,r.jsx)("div",{className:"mt-2 pt-2 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Subtotal:"}),(0,r.jsxs)("span",{className:"font-medium text-gray-900",children:["$",(e.priceAtPurchaseTime*e.quantity).toFixed(2)]})]})})]},t)),(0,r.jsx)("div",{className:"bg-white p-3 rounded border border-amber-200 bg-amber-50",children:(0,r.jsxs)("div",{className:"flex justify-between text-base font-medium",children:[(0,r.jsx)("span",{className:"text-gray-900",children:"Order Total:"}),(0,r.jsxs)("span",{className:"text-gray-900",children:["$",e.totalAmount.toFixed(2)]})]})})]}):(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"No items found for this order."})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 mt-6 border-t border-gray-200",children:[(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Close"}),(0,r.jsx)("button",{onClick:()=>window.print(),className:"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800",children:"Print Order"})]})]})})}var l=s(63968);function o({order:e,statuses:t,onClose:s,onSuccess:n}){let[d,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(""),[m,x]=(0,a.useState)(e.statusId),[u,h]=(0,a.useState)(""),p=async t=>{if(t.preventDefault(),c(""),m===e.statusId)return void c("Please select a different status to update.");i(!0);try{await l.Qo.update.updateStatus(e.id,m),n()}catch(e){console.error("Error updating order status:",e),c("Failed to update order status. Please try again.")}finally{i(!1)}},b=()=>{let s=t.find(t=>t.id===e.statusId);return s?.statusName||"Unknown"},g=e=>{switch(e){case 1:return"bg-yellow-100 text-yellow-800";case 2:return"bg-blue-100 text-blue-800";case 3:return"bg-purple-100 text-purple-800";case 4:return"bg-green-100 text-green-800";case 5:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},f=e=>({1:"Order is waiting for review and processing.",2:"Order is being prepared and processed.",3:"Order has been shipped and is on its way.",4:"Order has been successfully delivered.",5:"Order has been cancelled."})[e]||"";return(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsxs)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Update Order Status"}),(0,r.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 rounded-md",children:[(0,r.jsxs)("h4",{className:"font-medium text-gray-900",children:["Order #",e.id]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.email}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Total: $",e.totalAmount.toFixed(2)]})]}),(0,r.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Status"}),(0,r.jsxs)("div",{className:"p-3 bg-gray-50 rounded-md",children:[(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${g(e.statusId)}`,children:b()}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:f(e.statusId)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Status *"}),(0,r.jsx)("select",{id:"status",value:m,onChange:e=>x(Number(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",required:!0,children:t.map(e=>(0,r.jsx)("option",{value:e.id,children:e.statusName},e.id))}),m!==e.statusId&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:f(m)})]}),m!==e.statusId&&(0,r.jsx)("div",{className:"p-3 bg-blue-50 rounded-md",children:(0,r.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,r.jsx)("span",{className:"font-medium",children:"Status will change from: "}),(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${g(e.statusId)} mr-2`,children:b()}),(0,r.jsx)("span",{className:"font-medium",children:"to: "}),(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${g(m)}`,children:(()=>{let e=t.find(e=>e.id===m);return e?.statusName||"Unknown"})()})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700 mb-1",children:"Notes (Optional)"}),(0,r.jsx)("textarea",{id:"notes",value:u,onChange:e=>h(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",placeholder:"Add any notes about this status change..."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>x(2),className:`px-3 py-2 text-sm rounded-md border ${2===m?"bg-blue-100 border-blue-300 text-blue-800":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Approve (Processing)"}),(0,r.jsx)("button",{type:"button",onClick:()=>x(5),className:`px-3 py-2 text-sm rounded-md border ${5===m?"bg-red-100 border-red-300 text-red-800":"bg-white border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Cancel Order"})]}),o&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm",children:o}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:d||m===e.statusId,className:"px-4 py-2 bg-amber-900 text-white rounded-md hover:bg-amber-800 disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Updating...":"Update Status"})]})]})]})})}function c(){let[e,t]=(0,a.useState)([]),[s,c]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0),[u,h]=(0,a.useState)(!1),[p,b]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),[j,y]=(0,a.useState)(""),[v,N]=(0,a.useState)(""),[w,k]=(0,a.useState)("all"),C=async()=>{try{x(!0);let[e,s]=await Promise.all([l.Qo.get.getAll(),Promise.resolve([{id:1,statusName:"Pending"},{id:2,statusName:"Processing"},{id:3,statusName:"Shipped"},{id:4,statusName:"Delivered"},{id:5,statusName:"Cancelled"}])]);t(e.map(e=>({...e,statusId:e.statusId??1,status:e.status??"",orderItems:e.orderItems??[]}))),c(s)}catch(e){console.error("Error fetching data:",e)}finally{x(!1)}},A=e=>{f(e),h(!0)},S=e=>{f(e),b(!0)},P=async(e,t)=>{try{await l.Qo.update.updateStatus(e,t),await C()}catch(e){console.error("Error updating order status:",e),alert("Error updating order status. Please try again.")}},I=e=>{let t=s.find(t=>t.id===e);return t?.statusName||"Unknown"},L=e=>{switch(e){case 1:return"bg-yellow-100 text-yellow-800";case 2:return"bg-blue-100 text-blue-800";case 3:return"bg-purple-100 text-purple-800";case 4:return"bg-green-100 text-green-800";case 5:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},D=e=>{let t=new Date(e.createdAt),s=new Date;switch(w){case"today":return t.toDateString()===s.toDateString();case"week":return t>=new Date(s.getTime()-6048e5);case"month":return t>=new Date(s.getTime()-2592e6);default:return!0}},F=e.filter(e=>{let t=e.email.toLowerCase().includes(j.toLowerCase())||e.id.toString().includes(j),s=""===v||e.statusId===v,r=D(e);return t&&s&&r});return(0,r.jsx)(n.A,{children:(0,r.jsxs)(d.A,{children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Orders Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage customer orders and update their status"})]}),(0,r.jsx)("div",{className:"flex space-x-2",children:(0,r.jsx)("button",{onClick:()=>P(0,2),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",disabled:!0,children:"Bulk Actions"})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Orders"}),(0,r.jsx)("input",{type:"text",id:"search",value:j,onChange:e=>y(e.target.value),placeholder:"Search by email or order ID...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Status"}),(0,r.jsxs)("select",{id:"status",value:v,onChange:e=>N(""===e.target.value?"":Number(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",children:[(0,r.jsx)("option",{value:"",children:"All Statuses"}),s.map(e=>(0,r.jsx)("option",{value:e.id,children:e.statusName},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"date",className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Date"}),(0,r.jsxs)("select",{id:"date",value:w,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500",children:[(0,r.jsx)("option",{value:"all",children:"All Time"}),(0,r.jsx)("option",{value:"today",children:"Today"}),(0,r.jsx)("option",{value:"week",children:"Last 7 Days"}),(0,r.jsx)("option",{value:"month",children:"Last 30 Days"})]})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:m?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading orders..."})]}):(0,r.jsxs)("div",{className:"overflow-x-auto",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Order ID"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Customer"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Items"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:F.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["#",e.id]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.email}),e.phoneNumber&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.phoneNumber})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["$",e.totalAmount.toFixed(2)]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${L(e.statusId)}`,children:I(e.statusId)})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.orderItems?.length||0," items"]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,r.jsx)("button",{onClick:()=>A(e),className:"text-amber-600 hover:text-amber-900 mr-3",children:"View"}),(0,r.jsx)("button",{onClick:()=>S(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"Status"}),1===e.statusId&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>P(e.id,2),className:"text-green-600 hover:text-green-900 mr-2",children:"Approve"}),(0,r.jsx)("button",{onClick:()=>P(e.id,5),className:"text-red-600 hover:text-red-900",children:"Decline"})]})]})]},e.id))})]}),0===F.length&&(0,r.jsx)("div",{className:"p-8 text-center text-gray-500",children:"No orders found matching your criteria."})]})})]}),u&&g&&(0,r.jsx)(i,{order:g,onClose:()=>{h(!1),f(null)}}),p&&g&&(0,r.jsx)(o,{order:g,statuses:s,onClose:()=>{b(!1),f(null)},onSuccess:()=>{b(!1),f(null),C()}})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73441:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(60687),a=s(43210),n=s(16189),d=s(3844);function i({children:e}){let[t,s]=(0,a.useState)(!0),{admin:i,logout:l}=(0,d.b)(),o=(0,n.useRouter)(),c=(0,n.usePathname)(),m=[{name:"Dashboard",href:"/admin/dashboard",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"})})},{name:"Collections",href:"/admin/dashboard/collections",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})},{name:"Products",href:"/admin/dashboard/products",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"})})},{name:"Images",href:"/admin/dashboard/images",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})},{name:"Orders",href:"/admin/dashboard/orders",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{name:"Contact Submissions",href:"/admin/dashboard/contact-submissions",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})},{name:"Wholesale Buyers",href:"/admin/dashboard/wholesale-buyers",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}];return(0,r.jsxs)("div",{className:"flex h-screen bg-black",children:[(0,r.jsxs)("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${t?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 border-r border-white`,children:[(0,r.jsx)("div",{className:"flex items-center justify-center h-16 px-4 bg-black",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Cast Stone Admin"})}),(0,r.jsx)("nav",{className:"mt-8",children:(0,r.jsx)("div",{className:"px-4 space-y-2",children:m.map(e=>{let t=c===e.href;return(0,r.jsxs)("a",{href:e.href,className:`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors ${t?"bg-amber-100 text-amber-900 border-r-2 border-amber-900":"text-amber-800 hover:bg-amber-50 hover:text-amber-900"}`,children:[e.icon,(0,r.jsx)("span",{className:"ml-3",children:e.name})]},e.name)})})})]}),(0,r.jsxs)("div",{className:"lg:pl flex flex-col flex-1 h-full",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-black",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>s(!t),className:"lg:hidden p-2 rounded-md text-black hover:text-black hover:bg-black",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,r.jsx)("h2",{className:"ml-4 text-2xl font-bold text-black",children:m.find(e=>e.href===c)?.name||"Dashboard"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-sm text-black",children:["Welcome, ",(0,r.jsx)("span",{className:"font-semibold text-black",children:i?.email})]}),(0,r.jsx)("button",{onClick:()=>{l(),o.push("/admin/login")},className:"px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-black-800 transition-colors shadow-sm",children:"Logout"})]})]})}),(0,r.jsx)("main",{className:"flex-1 p-6 bg-white",children:e})]}),t&&(0,r.jsx)("div",{className:"fixed inset-0 z-40 bg-white bg-opacity-75 lg:hidden",onClick:()=>s(!1)})]})}},79551:e=>{"use strict";e.exports=require("url")},83645:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(60687);s(43210);var a=s(16189),n=s(3844);function d({children:e}){let{isAuthenticated:t,isLoading:s}=(0,n.b)();return((0,a.useRouter)(),s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-white",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-amber-900 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-amber-900",children:"Loading..."})]})}):t?(0,r.jsx)(r.Fragment,{children:e}):null}},84079:(e,t,s)=>{Promise.resolve().then(s.bind(s,46347))},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>n});var r=s(37413),a=s(41210);let n={title:"Cast Stone Admin - Dashboard",description:"Admin dashboard for Cast Stone management"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:"antialiased bg-gray-50",children:(0,r.jsx)(a.AdminAuthProvider,{children:e})})})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,72,658,913],()=>s(17722));module.exports=r})();