module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ // API Configuration
// export const BaseApiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:7069/api';
__turbopack_context__.s({
    "ApiEndpoints": (()=>ApiEndpoints),
    "ApiError": (()=>ApiError),
    "BaseApiUrl": (()=>BaseApiUrl),
    "HttpMethod": (()=>HttpMethod),
    "buildQueryString": (()=>buildQueryString),
    "defaultHeaders": (()=>defaultHeaders)
});
const BaseApiUrl = 'https://cast-stonev2.onrender.com/api';
var HttpMethod = /*#__PURE__*/ function(HttpMethod) {
    HttpMethod["GET"] = "GET";
    HttpMethod["POST"] = "POST";
    HttpMethod["PUT"] = "PUT";
    HttpMethod["PATCH"] = "PATCH";
    HttpMethod["DELETE"] = "DELETE";
    return HttpMethod;
}({});
const ApiEndpoints = {
    // Collections
    Collections: {
        Base: '/collections',
        ById: (id)=>`/collections/${id}`,
        ByLevel: (level)=>`/collections/level/${level}`,
        Children: (id)=>`/collections/${id}/children`,
        Hierarchy: '/collections/hierarchy',
        Published: '/collections/published',
        Search: '/collections/search',
        Filter: '/collections/filter',
        RefreshRelationships: '/collections/refresh-relationships'
    },
    // Products
    Products: {
        Base: '/products',
        ById: (id)=>`/products/${id}`,
        ByCollection: (collectionId)=>`/products/collection/${collectionId}`,
        InStock: '/products/in-stock',
        Featured: '/products/featured',
        Latest: '/products/latest',
        Search: '/products/search',
        PriceRange: '/products/price-range',
        UpdateStock: (id)=>`/products/${id}/stock`,
        Filter: '/products/filter'
    },
    // Product Specifications
    ProductSpecifications: {
        Base: '/productspecifications',
        ById: (id)=>`/productspecifications/${id}`,
        ByProduct: (productId)=>`/productspecifications/product/${productId}`
    },
    // Product Details
    ProductDetails: {
        Base: '/productdetails',
        ById: (id)=>`/productdetails/${id}`,
        ByProduct: (productId)=>`/productdetails/product/${productId}`
    },
    // Downloadable Content
    DownloadableContent: {
        Base: '/downloadablecontent',
        ById: (id)=>`/downloadablecontent/${id}`,
        ByProduct: (productId)=>`/downloadablecontent/product/${productId}`
    },
    // Orders
    Orders: {
        Base: '/orders',
        ById: (id)=>`/orders/${id}`,
        ByUser: (userId)=>`/orders/user/${userId}`,
        ByEmail: (email)=>`/orders/email/${email}`,
        ByStatus: (statusId)=>`/orders/status/${statusId}`,
        UpdateStatus: (id)=>`/orders/${id}/status`,
        Cancel: (id)=>`/orders/${id}/cancel`,
        Pending: '/orders/pending',
        Recent: '/orders/recent',
        Details: (id)=>`/orders/${id}/details`,
        Revenue: {
            Total: '/orders/revenue/total',
            Range: '/orders/revenue/range'
        },
        Filter: '/orders/filter'
    },
    // Users
    Users: {
        Base: '/users',
        ById: (id)=>`/users/${id}`,
        ByEmail: (email)=>`/users/email/${email}`,
        ByRole: (role)=>`/users/role/${role}`,
        Active: '/users/active',
        Recent: '/users/recent',
        Deactivate: (id)=>`/users/${id}/deactivate`,
        Activate: (id)=>`/users/${id}/activate`,
        WithOrders: (id)=>`/users/${id}/orders`,
        EmailExists: (email)=>`/users/email-exists/${email}`,
        Filter: '/users/filter'
    },
    // Cart
    Cart: {
        Base: '/cart',
        ByUserId: (userId)=>`/cart/user/${userId}`,
        BySessionId: (sessionId)=>`/cart/session/${sessionId}`,
        SummaryByUserId: (userId)=>`/cart/summary/user/${userId}`,
        SummaryBySessionId: (sessionId)=>`/cart/summary/session/${sessionId}`,
        Add: '/cart/add',
        UpdateItem: (cartId, productId)=>`/cart/${cartId}/items/${productId}`,
        RemoveItem: (cartId, productId)=>`/cart/${cartId}/items/${productId}`,
        RemoveCartItem: (cartItemId)=>`/cart/items/${cartItemId}`,
        Clear: (cartId)=>`/cart/${cartId}/clear`,
        ClearByUserId: (userId)=>`/cart/user/${userId}/clear`,
        ClearBySessionId: (sessionId)=>`/cart/session/${sessionId}/clear`,
        GetOrCreate: '/cart/get-or-create'
    },
    // Payments
    Payments: '/payments',
    // Seeding
    Seed: {
        All: '/seed/all',
        Statuses: '/seed/statuses',
        AdminUser: '/seed/admin-user',
        Collections: '/seed/collections',
        Products: '/seed/products'
    }
};
class ApiError extends Error {
    status;
    errors;
    constructor(message, status, errors){
        super(message), this.status = status, this.errors = errors;
        this.name = 'ApiError';
    }
}
const buildQueryString = (params)=>{
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value])=>{
        if (value !== undefined && value !== null && value !== '') {
            if (Array.isArray(value)) {
                value.forEach((item)=>searchParams.append(key, String(item)));
            } else {
                searchParams.append(key, String(value));
            }
        }
    });
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
};
const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
};
}}),
"[project]/src/services/config/httpClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "HttpClient": (()=>HttpClient),
    "httpClient": (()=>httpClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
class HttpClient {
    baseUrl;
    constructor(baseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiUrl"]){
        this.baseUrl = baseUrl;
    }
    async request(endpoint, method = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET, config = {}) {
        const { params, ...requestConfig } = config;
        // Build URL with query parameters
        let url = `${this.baseUrl}${endpoint}`;
        if (params) {
            url += (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["buildQueryString"])(params);
        }
        const requestOptions = {
            method,
            headers: {
                ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultHeaders"],
                ...requestConfig.headers
            },
            ...requestConfig
        };
        // Add body for non-GET requests
        if (method !== __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET && requestConfig.body) {
            if (typeof requestConfig.body === 'object') {
                requestOptions.body = JSON.stringify(requestConfig.body);
            }
        }
        try {
            const response = await fetch(url, requestOptions);
            // Handle different response types
            let responseData;
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                // Handle non-JSON responses
                const text = await response.text();
                responseData = {
                    success: response.ok,
                    message: response.ok ? 'Success' : 'Error',
                    data: text
                };
            }
            if (!response.ok) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](responseData.message || `HTTP error! status: ${response.status}`, response.status, responseData.errors);
            }
            return responseData;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]) {
                throw error;
            }
            console.error('API request failed:', error);
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"](error instanceof Error ? error.message : 'Unknown error occurred');
        }
    }
    // GET request
    async get(endpoint, params) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].GET, {
            params
        });
    }
    // POST request
    async post(endpoint, data, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].POST, {
            ...config,
            body: data
        });
    }
    // PUT request
    async put(endpoint, data, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].PUT, {
            ...config,
            body: data
        });
    }
    // PATCH request
    async patch(endpoint, data, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].PATCH, {
            ...config,
            body: data
        });
    }
    // DELETE request
    async delete(endpoint, config) {
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].DELETE, config);
    }
    // Upload file (multipart/form-data)
    async upload(endpoint, formData, config) {
        const uploadConfig = {
            ...config,
            headers: {
                // Don't set Content-Type for FormData, let browser set it with boundary
                ...config?.headers
            },
            body: formData
        };
        // Remove Content-Type header for file uploads
        if (uploadConfig.headers && 'Content-Type' in uploadConfig.headers) {
            delete uploadConfig.headers['Content-Type'];
        }
        return this.request(endpoint, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HttpMethod"].POST, uploadConfig);
    }
}
const httpClient = new HttpClient();
;
}}),
"[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "BaseService": (()=>BaseService),
    "ServiceUtils": (()=>ServiceUtils)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$httpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/httpClient.ts [app-ssr] (ecmascript)");
;
class BaseService {
    client = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$httpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["httpClient"];
    /**
   * Handle API response and extract data
   */ async handleResponse(apiCall) {
        try {
            const response = await apiCall;
            if (response.success && response.data !== undefined) {
                return response.data;
            }
            throw new Error(response.message || 'API call failed');
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    /**
   * Handle paginated API response
   */ async handlePaginatedResponse(apiCall) {
        try {
            const response = await apiCall;
            if (response.success && response.data !== undefined) {
                return response.data;
            }
            throw new Error(response.message || 'API call failed');
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    /**
   * Handle API response without data extraction (for operations like delete)
   */ async handleVoidResponse(apiCall) {
        try {
            const response = await apiCall;
            return response.success;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }
    /**
   * Log API calls in development
   */ logApiCall(method, endpoint, data) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.log(`🌐 API ${method}:`, endpoint, data ? {
                data
            } : '');
        }
    }
}
class ServiceUtils {
    /**
   * Format date for API calls
   */ static formatDate(date) {
        return date.toISOString();
    }
    /**
   * Parse API date string to Date object
   */ static parseDate(dateString) {
        return new Date(dateString);
    }
    /**
   * Validate email format
   */ static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    /**
   * Format currency
   */ static formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
    /**
   * Debounce function for search inputs
   */ static debounce(func, wait) {
        let timeout;
        return (...args)=>{
            clearTimeout(timeout);
            timeout = setTimeout(()=>func(...args), wait);
        };
    }
    /**
   * Clean undefined values from objects (useful for API params)
   */ static cleanObject(obj) {
        const cleaned = {};
        Object.entries(obj).forEach(([key, value])=>{
            if (value !== undefined && value !== null && value !== '') {
                cleaned[key] = value;
            }
        });
        return cleaned;
    }
}
}}),
"[project]/src/services/types/entities.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Collection Types
__turbopack_context__.s({
    "InquiryType": (()=>InquiryType)
});
var InquiryType = /*#__PURE__*/ function(InquiryType) {
    InquiryType[InquiryType["ProductInquiry"] = 1] = "ProductInquiry";
    InquiryType[InquiryType["RequestDesignConsultation"] = 2] = "RequestDesignConsultation";
    InquiryType[InquiryType["CustomOrders"] = 3] = "CustomOrders";
    InquiryType[InquiryType["TradePartnerships"] = 4] = "TradePartnerships";
    InquiryType[InquiryType["InstallationSupport"] = 5] = "InstallationSupport";
    InquiryType[InquiryType["ShippingAndLeadTimes"] = 6] = "ShippingAndLeadTimes";
    InquiryType[InquiryType["RequestCatalogPriceList"] = 7] = "RequestCatalogPriceList";
    InquiryType[InquiryType["MediaPressInquiry"] = 8] = "MediaPressInquiry";
    InquiryType[InquiryType["GeneralQuestions"] = 9] = "GeneralQuestions";
    return InquiryType;
}({});
}}),
"[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionGetService": (()=>CollectionGetService),
    "collectionGetService": (()=>collectionGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all collections
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base));
    }
    /**
   * Get collection by ID
   */ async getById(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id)));
    }
    /**
   * Get collections by level
   */ async getByLevel(level) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ByLevel(level));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ByLevel(level)));
    }
    /**
   * Get children of a collection
   */ async getChildren(parentId) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Children(parentId));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Children(parentId)));
    }
    /**
   * Get collection hierarchy
   */ async getHierarchy() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Hierarchy);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Hierarchy));
    }
    /**
   * Get published collections
   */ async getPublished() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Published);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Published));
    }
    /**
   * Search collections by name
   */ async search(name) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Search, {
            name
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Search, {
            name
        }));
    }
    /**
   * Get collections with advanced filtering and pagination
   */ async getFiltered(filters) {
        const cleanFilters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].cleanObject(filters);
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Filter, cleanFilters);
        return this.handlePaginatedResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Filter, cleanFilters));
    }
    /**
   * Get collections with default pagination
   */ async getPaginated(pageNumber = 1, pageSize = 10, sortBy = 'createdAt', sortDirection = 'desc') {
        const filters = {
            pageNumber,
            pageSize,
            sortBy,
            sortDirection
        };
        return this.getFiltered(filters);
    }
    /**
   * Get root collections (level 1)
   */ async getRootCollections() {
        return this.getByLevel(1);
    }
    /**
   * Get collections by tag
   */ async getByTag(tag) {
        const filters = {
            tag,
            pageSize: 100 // Get all matching collections
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get collections created by specific user
   */ async getByCreatedBy(createdBy) {
        const filters = {
            createdBy,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get collections created within date range
   */ async getByDateRange(startDate, endDate) {
        const filters = {
            createdAfter: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(startDate),
            createdBefore: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(endDate),
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
}
const collectionGetService = new CollectionGetService();
}}),
"[project]/src/services/api/collections/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionPostService": (()=>CollectionPostService),
    "collectionPostService": (()=>collectionPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a new collection
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base, data);
        // Validate required fields
        this.validateCreateRequest(data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.Base, data));
    }
    /**
   * Create a root collection (Level 1)
   */ async createRootCollection(name, description, tags = [], published = false, createdBy) {
        const data = {
            name,
            description,
            level: 1,
            parentCollectionId: undefined,
            childCollectionIds: undefined,
            tags,
            published,
            createdBy,
            images: []
        };
        return this.create(data);
    }
    /**
   * Create a sub-collection (Level 2 or 3)
   */ async createSubCollection(name, description, level, parentCollectionId, tags = [], published = false, createdBy) {
        const data = {
            name,
            description,
            level,
            parentCollectionId,
            childCollectionIds: undefined,
            tags,
            published,
            createdBy,
            images: []
        };
        return this.create(data);
    }
    /**
   * Create multiple collections in batch
   */ async createBatch(collections) {
        this.logApiCall('POST', 'Batch Collections', {
            count: collections.length
        });
        const promises = collections.map((collection)=>this.create(collection));
        return Promise.all(promises);
    }
    /**
   * Refresh parent-child relationships for all collections (maintenance operation)
   */ async refreshAllRelationships() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.RefreshRelationships);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.RefreshRelationships);
            if (response.success && response.data !== undefined) {
                return {
                    success: true,
                    updatedCount: response.data,
                    message: response.message || `Updated ${response.data} collection relationships`
                };
            } else {
                return {
                    success: false,
                    updatedCount: 0,
                    message: response.message || 'Failed to refresh relationships'
                };
            }
        } catch (error) {
            console.error('Error refreshing collection relationships:', error);
            return {
                success: false,
                updatedCount: 0,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Validate create collection request
   */ validateCreateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Collection name is required');
        }
        if (data.name.length > 200) {
            throw new Error('Collection name must be 200 characters or less');
        }
        if (!data.level || data.level < 1 || data.level > 3) {
            throw new Error('Collection level must be 1, 2, or 3');
        }
        if (data.level === 1 && data.parentCollectionId) {
            throw new Error('Root collections (level 1) cannot have a parent');
        }
        if (data.level > 1 && !data.parentCollectionId) {
            throw new Error('Sub-collections (level 2-3) must have a parent');
        }
        if (!data.createdBy || data.createdBy.trim().length === 0) {
            throw new Error('CreatedBy is required');
        }
        if (data.createdBy.length > 100) {
            throw new Error('CreatedBy must be 100 characters or less');
        }
        if (data.description && data.description.length > 1000) {
            throw new Error('Description must be 1000 characters or less');
        }
    }
}
const collectionPostService = new CollectionPostService();
}}),
"[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionUpdateService": (()=>CollectionUpdateService),
    "collectionUpdateService": (()=>collectionUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update an existing collection
   */ async update(id, data) {
        this.logApiCall('PUT', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id), data);
        // Validate required fields
        this.validateUpdateRequest(data);
        return this.handleResponse(this.client.put(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id), data));
    }
    /**
   * Update collection name and description
   */ async updateBasicInfo(id, name, description, updatedBy) {
        // First get the current collection to preserve other fields
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name,
            description,
            level: currentCollection.data.level,
            parentCollectionId: currentCollection.data.parentCollectionId,
            childCollectionIds: currentCollection.data.childCollectionIds,
            tags: currentCollection.data.tags,
            published: currentCollection.data.published,
            updatedBy,
            images: []
        };
        return this.update(id, data);
    }
    /**
   * Update collection tags
   */ async updateTags(id, tags, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name: currentCollection.data.name,
            description: currentCollection.data.description,
            level: currentCollection.data.level,
            parentCollectionId: currentCollection.data.parentCollectionId,
            childCollectionIds: currentCollection.data.childCollectionIds,
            tags,
            published: currentCollection.data.published,
            updatedBy,
            images: []
        };
        return this.update(id, data);
    }
    /**
   * Publish or unpublish a collection
   */ async updatePublishStatus(id, published, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name: currentCollection.data.name,
            description: currentCollection.data.description,
            level: currentCollection.data.level,
            parentCollectionId: currentCollection.data.parentCollectionId,
            childCollectionIds: currentCollection.data.childCollectionIds,
            tags: currentCollection.data.tags,
            published,
            updatedBy,
            images: []
        };
        return this.update(id, data);
    }
    /**
   * Move collection to different parent (change hierarchy)
   */ async moveToParent(id, newParentId, newLevel, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const data = {
            name: currentCollection.data.name,
            description: currentCollection.data.description,
            level: newLevel,
            parentCollectionId: newParentId,
            childCollectionIds: currentCollection.data.childCollectionIds,
            tags: currentCollection.data.tags,
            published: currentCollection.data.published,
            updatedBy,
            images: []
        };
        return this.update(id, data);
    }
    /**
   * Add tags to existing collection
   */ async addTags(id, newTags, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const existingTags = currentCollection.data.tags || [];
        const uniqueTags = [
            ...new Set([
                ...existingTags,
                ...newTags
            ])
        ];
        return this.updateTags(id, uniqueTags, updatedBy);
    }
    /**
   * Remove tags from existing collection
   */ async removeTags(id, tagsToRemove, updatedBy) {
        const currentCollection = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        if (!currentCollection.success || !currentCollection.data) {
            throw new Error('Collection not found');
        }
        const existingTags = currentCollection.data.tags || [];
        const filteredTags = existingTags.filter((tag)=>!tagsToRemove.includes(tag));
        return this.updateTags(id, filteredTags, updatedBy);
    }
    /**
   * Validate update collection request
   */ validateUpdateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Collection name is required');
        }
        if (data.name.length > 200) {
            throw new Error('Collection name must be 200 characters or less');
        }
        if (!data.level || data.level < 1 || data.level > 3) {
            throw new Error('Collection level must be 1, 2, or 3');
        }
        if (data.level === 1 && data.parentCollectionId) {
            throw new Error('Root collections (level 1) cannot have a parent');
        }
        if (data.level > 1 && !data.parentCollectionId) {
            throw new Error('Sub-collections (level 2-3) must have a parent');
        }
        if (!data.updatedBy || data.updatedBy.trim().length === 0) {
            throw new Error('UpdatedBy is required');
        }
        if (data.updatedBy.length > 100) {
            throw new Error('UpdatedBy must be 100 characters or less');
        }
        if (data.description && data.description.length > 1000) {
            throw new Error('Description must be 1000 characters or less');
        }
    }
}
const collectionUpdateService = new CollectionUpdateService();
}}),
"[project]/src/services/api/collections/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CollectionDeleteService": (()=>CollectionDeleteService),
    "collectionDeleteService": (()=>collectionDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CollectionDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Delete a collection by ID
   */ async delete(id) {
        this.logApiCall('DELETE', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id));
        return this.handleVoidResponse(this.client.delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Collections.ById(id)));
    }
    /**
   * Delete multiple collections
   */ async deleteBatch(ids) {
        this.logApiCall('DELETE', 'Batch Collections', {
            count: ids.length
        });
        const promises = ids.map((id)=>this.delete(id));
        return Promise.all(promises);
    }
    /**
   * Soft delete - unpublish collection instead of deleting
   */ async unpublish(id, updatedBy) {
        this.logApiCall('PATCH', `Unpublish Collection ${id}`);
        try {
            // Import here to avoid circular dependency
            const { collectionUpdateService } = await __turbopack_context__.r("[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await collectionUpdateService.updatePublishStatus(id, false, updatedBy);
            return true;
        } catch (error) {
            console.error('Failed to unpublish collection:', error);
            return false;
        }
    }
    /**
   * Check if collection can be safely deleted
   */ async canDelete(id) {
        try {
            // Import here to avoid circular dependency
            const { collectionGetService } = await __turbopack_context__.r("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            // Check if collection has children
            const children = await collectionGetService.getChildren(id);
            const hasChildren = children.length > 0;
            // Get collection with products to check if it has products
            const collection = await collectionGetService.getById(id);
            const hasProducts = collection.products && collection.products.length > 0;
            const canDelete = !hasChildren && !hasProducts;
            let reason;
            if (!canDelete) {
                if (hasChildren && hasProducts) {
                    reason = 'Collection has both child collections and products';
                } else if (hasChildren) {
                    reason = 'Collection has child collections';
                } else if (hasProducts) {
                    reason = 'Collection has products';
                }
            }
            return {
                canDelete,
                reason,
                hasChildren,
                hasProducts
            };
        } catch (error) {
            console.error('Error checking if collection can be deleted:', error);
            return {
                canDelete: false,
                reason: 'Error checking collection dependencies'
            };
        }
    }
    /**
   * Safe delete - checks dependencies before deleting
   */ async safeDelete(id) {
        try {
            const deleteCheck = await this.canDelete(id);
            if (!deleteCheck.canDelete) {
                return {
                    success: false,
                    message: deleteCheck.reason || 'Collection cannot be deleted'
                };
            }
            const deleted = await this.delete(id);
            if (deleted) {
                return {
                    success: true,
                    message: 'Collection deleted successfully'
                };
            } else {
                return {
                    success: false,
                    message: 'Failed to delete collection'
                };
            }
        } catch (error) {
            console.error('Error during safe delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Force delete with cascade (delete children and move products)
   * Note: This should be used with extreme caution
   */ async forceDelete(id, moveProductsToCollectionId) {
        try {
            const { collectionGetService } = await __turbopack_context__.r("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const deletedCollections = [];
            let movedProducts = 0;
            // Get collection details
            const collection = await collectionGetService.getById(id);
            // Move products to another collection if specified
            if (moveProductsToCollectionId && collection.products.length > 0) {
                // This would require product service - placeholder for now
                movedProducts = collection.products.length;
                console.warn('Product moving not implemented - would move', movedProducts, 'products');
            }
            // Delete children recursively
            const children = await collectionGetService.getChildren(id);
            for (const child of children){
                const childResult = await this.forceDelete(child.id, moveProductsToCollectionId);
                if (childResult.success && childResult.deletedCollections) {
                    deletedCollections.push(...childResult.deletedCollections);
                }
            }
            // Delete the collection itself
            const deleted = await this.delete(id);
            if (deleted) {
                deletedCollections.push(id);
            }
            return {
                success: deleted,
                message: deleted ? `Successfully deleted collection and ${deletedCollections.length - 1} child collections` : 'Failed to delete collection',
                deletedCollections,
                movedProducts
            };
        } catch (error) {
            console.error('Error during force delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
}
const collectionDeleteService = new CollectionDeleteService();
}}),
"[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Collection Services
__turbopack_context__.s({
    "CollectionService": (()=>CollectionService),
    "collectionService": (()=>collectionService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
class CollectionService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionPostService"];
    update = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionUpdateService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionDeleteService"];
}
const collectionService = new CollectionService();
}}),
"[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/products/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductGetService": (()=>ProductGetService),
    "productGetService": (()=>productGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all products
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Base));
    }
    /**
   * Get product by ID
   */ async getById(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id)));
    }
    /**
   * Get products by collection ID
   */ async getByCollection(collectionId) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ByCollection(collectionId));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ByCollection(collectionId)));
    }
    /**
   * Get products in stock
   */ async getInStock() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.InStock);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.InStock));
    }
    /**
   * Get featured products
   */ async getFeatured(count = 10) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Featured, {
            count
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Featured, {
            count
        }));
    }
    /**
   * Get latest products
   */ async getLatest(count = 10) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Latest, {
            count
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Latest, {
            count
        }));
    }
    /**
   * Search products by name
   */ async search(name) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Search, {
            name
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Search, {
            name
        }));
    }
    /**
   * Get products by price range
   */ async getByPriceRange(minPrice, maxPrice) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.PriceRange, {
            minPrice,
            maxPrice
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.PriceRange, {
            minPrice,
            maxPrice
        }));
    }
    /**
   * Get products with advanced filtering and pagination
   */ async getFiltered(filters) {
        const cleanFilters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].cleanObject(filters);
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Filter, cleanFilters);
        return this.handlePaginatedResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Filter, cleanFilters));
    }
    /**
   * Get products with default pagination
   */ async getPaginated(pageNumber = 1, pageSize = 10, sortBy = 'createdAt', sortDirection = 'desc') {
        const filters = {
            pageNumber,
            pageSize,
            sortBy,
            sortDirection
        };
        return this.getFiltered(filters);
    }
    /**
   * Get products by tag
   */ async getByTag(tag) {
        const filters = {
            tag,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get low stock products
   */ async getLowStock(threshold = 10) {
        const filters = {
            minStock: 1,
            maxStock: threshold,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get out of stock products
   */ async getOutOfStock() {
        const filters = {
            inStock: false,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get products in price range with pagination
   */ async getByPriceRangePaginated(minPrice, maxPrice, pageNumber = 1, pageSize = 10) {
        const filters = {
            minPrice,
            maxPrice,
            pageNumber,
            pageSize,
            sortBy: 'price',
            sortDirection: 'asc'
        };
        return this.getFiltered(filters);
    }
    /**
   * Get products created within date range
   */ async getByDateRange(startDate, endDate) {
        const filters = {
            createdAfter: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(startDate),
            createdBefore: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(endDate),
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get product recommendations based on collection
   */ async getRecommendations(productId, count = 5) {
        try {
            const product = await this.getById(productId);
            const relatedProducts = await this.getByCollection(product.collectionId);
            // Filter out the current product and return limited results
            return relatedProducts.filter((p)=>p.id !== productId).slice(0, count);
        } catch (error) {
            console.error('Error getting product recommendations:', error);
            return [];
        }
    }
}
const productGetService = new ProductGetService();
}}),
"[project]/src/services/api/products/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductPostService": (()=>ProductPostService),
    "productPostService": (()=>productPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a new product
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Base, data);
        // Validate required fields
        this.validateCreateRequest(data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.Base, data));
    }
    /**
   * Create a simple product with minimal data
   */ async createSimple(name, description, price, stock, collectionId, images = [], tags = []) {
        const data = {
            name,
            description,
            price,
            stock,
            collectionId,
            images,
            tags
        };
        return this.create(data);
    }
    /**
   * Create multiple products in batch
   */ async createBatch(products) {
        this.logApiCall('POST', 'Batch Products', {
            count: products.length
        });
        const promises = products.map((product)=>this.create(product));
        return Promise.all(promises);
    }
    /**
   * Create product with image upload
   */ async createWithImages(productData, imageFiles) {
        try {
            // First upload images (this would need an image upload service)
            const imageUrls = await this.uploadImages(imageFiles);
            // Then create product with image URLs
            const data = {
                ...productData,
                images: imageUrls
            };
            return this.create(data);
        } catch (error) {
            console.error('Error creating product with images:', error);
            throw error;
        }
    }
    /**
   * Duplicate an existing product
   */ async duplicate(originalProductId, newName, modifications = {}) {
        try {
            // Get the original product
            const originalResponse = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(originalProductId));
            if (!originalResponse.success || !originalResponse.data) {
                throw new Error('Original product not found');
            }
            const original = originalResponse.data;
            // Create new product data based on original
            const data = {
                name: newName,
                description: original.description,
                price: original.price,
                stock: 0,
                collectionId: original.collectionId,
                images: [
                    ...original.images
                ],
                tags: [
                    ...original.tags
                ],
                ...modifications // Apply any modifications
            };
            return this.create(data);
        } catch (error) {
            console.error('Error duplicating product:', error);
            throw error;
        }
    }
    /**
   * Create product variant (similar product with different attributes)
   */ async createVariant(baseProductId, variantName, priceAdjustment = 0, modifications = {}) {
        try {
            const baseResponse = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(baseProductId));
            if (!baseResponse.success || !baseResponse.data) {
                throw new Error('Base product not found');
            }
            const base = baseResponse.data;
            const data = {
                name: `${base.name} - ${variantName}`,
                description: base.description,
                price: base.price + priceAdjustment,
                stock: 0,
                collectionId: base.collectionId,
                images: [
                    ...base.images
                ],
                tags: [
                    ...base.tags,
                    'variant'
                ],
                ...modifications
            };
            return this.create(data);
        } catch (error) {
            console.error('Error creating product variant:', error);
            throw error;
        }
    }
    /**
   * Upload product images (placeholder - would need actual image upload service)
   */ async uploadImages(files) {
        // This is a placeholder implementation
        // In a real application, you would upload to a cloud storage service
        const imageUrls = [];
        for (const file of files){
            // Simulate upload process
            const formData = new FormData();
            formData.append('image', file);
            try {
                // This would be your actual image upload endpoint
                // const response = await this.client.upload('/upload/image', formData);
                // imageUrls.push(response.data.url);
                // For now, just create a placeholder URL
                imageUrls.push(`/images/products/${Date.now()}-${file.name}`);
            } catch (error) {
                console.error('Error uploading image:', error);
                throw new Error(`Failed to upload image: ${file.name}`);
            }
        }
        return imageUrls;
    }
    /**
   * Validate create product request
   */ validateCreateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Product name is required');
        }
        if (data.name.length > 200) {
            throw new Error('Product name must be 200 characters or less');
        }
        if (data.price <= 0) {
            throw new Error('Product price must be greater than 0');
        }
        if (data.stock < 0) {
            throw new Error('Product stock cannot be negative');
        }
        if (!data.collectionId) {
            throw new Error('Collection ID is required');
        }
        if (data.description && data.description.length > 1000) {
            throw new Error('Description must be 1000 characters or less');
        }
        // Validate image URLs
        if (data.images && data.images.length > 0) {
            for (const image of data.images){
                if (!this.isValidImageUrl(image)) {
                    throw new Error(`Invalid image URL: ${image}`);
                }
            }
        }
    }
    /**
   * Validate image URL format
   */ isValidImageUrl(url) {
        try {
            new URL(url);
            return true;
        } catch  {
            // If not a valid URL, check if it's a relative path
            return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
        }
    }
}
const productPostService = new ProductPostService();
}}),
"[project]/src/services/api/products/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductUpdateService": (()=>ProductUpdateService),
    "productUpdateService": (()=>productUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update an existing product
   */ async update(id, data) {
        this.logApiCall('PUT', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id), data);
        // Validate required fields
        this.validateUpdateRequest(data);
        return this.handleResponse(this.client.put(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id), data));
    }
    /**
   * Update product stock
   */ async updateStock(id, newStock) {
        this.logApiCall('PATCH', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.UpdateStock(id), {
            stock: newStock
        });
        if (newStock < 0) {
            throw new Error('Stock cannot be negative');
        }
        return this.handleVoidResponse(this.client.patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.UpdateStock(id), newStock));
    }
    /**
   * Update product price
   */ async updatePrice(id, newPrice) {
        if (newPrice <= 0) {
            throw new Error('Price must be greater than 0');
        }
        const currentProduct = await this.getCurrentProduct(id);
        const data = {
            ...this.mapProductToUpdateRequest(currentProduct),
            price: newPrice
        };
        return this.update(id, data);
    }
    /**
   * Update product basic information
   */ async updateBasicInfo(id, name, description) {
        const currentProduct = await this.getCurrentProduct(id);
        const data = {
            ...this.mapProductToUpdateRequest(currentProduct),
            name,
            description
        };
        return this.update(id, data);
    }
    /**
   * Update product images
   */ async updateImages(id, images) {
        const currentProduct = await this.getCurrentProduct(id);
        const data = {
            ...this.mapProductToUpdateRequest(currentProduct),
            images
        };
        return this.update(id, data);
    }
    /**
   * Add images to product
   */ async addImages(id, newImages) {
        const currentProduct = await this.getCurrentProduct(id);
        const existingImages = currentProduct.images || [];
        const allImages = [
            ...existingImages,
            ...newImages
        ];
        return this.updateImages(id, allImages);
    }
    /**
   * Remove images from product
   */ async removeImages(id, imagesToRemove) {
        const currentProduct = await this.getCurrentProduct(id);
        const existingImages = currentProduct.images || [];
        const filteredImages = existingImages.filter((img)=>!imagesToRemove.includes(img));
        return this.updateImages(id, filteredImages);
    }
    /**
   * Update product tags
   */ async updateTags(id, tags) {
        const currentProduct = await this.getCurrentProduct(id);
        const data = {
            ...this.mapProductToUpdateRequest(currentProduct),
            tags
        };
        return this.update(id, data);
    }
    /**
   * Add tags to product
   */ async addTags(id, newTags) {
        const currentProduct = await this.getCurrentProduct(id);
        const existingTags = currentProduct.tags || [];
        const uniqueTags = [
            ...new Set([
                ...existingTags,
                ...newTags
            ])
        ];
        return this.updateTags(id, uniqueTags);
    }
    /**
   * Remove tags from product
   */ async removeTags(id, tagsToRemove) {
        const currentProduct = await this.getCurrentProduct(id);
        const existingTags = currentProduct.tags || [];
        const filteredTags = existingTags.filter((tag)=>!tagsToRemove.includes(tag));
        return this.updateTags(id, filteredTags);
    }
    /**
   * Move product to different collection
   */ async moveToCollection(id, newCollectionId) {
        const currentProduct = await this.getCurrentProduct(id);
        const data = {
            ...this.mapProductToUpdateRequest(currentProduct),
            collectionId: newCollectionId
        };
        return this.update(id, data);
    }
    /**
   * Adjust stock (add or subtract)
   */ async adjustStock(id, adjustment) {
        const currentProduct = await this.getCurrentProduct(id);
        const newStock = currentProduct.stock + adjustment;
        if (newStock < 0) {
            throw new Error('Stock adjustment would result in negative stock');
        }
        return this.updateStock(id, newStock);
    }
    /**
   * Increase stock
   */ async increaseStock(id, amount) {
        if (amount <= 0) {
            throw new Error('Amount must be positive');
        }
        return this.adjustStock(id, amount);
    }
    /**
   * Decrease stock
   */ async decreaseStock(id, amount) {
        if (amount <= 0) {
            throw new Error('Amount must be positive');
        }
        return this.adjustStock(id, -amount);
    }
    /**
   * Apply discount to product price
   */ async applyDiscount(id, discountPercentage) {
        if (discountPercentage < 0 || discountPercentage > 100) {
            throw new Error('Discount percentage must be between 0 and 100');
        }
        const currentProduct = await this.getCurrentProduct(id);
        const discountedPrice = currentProduct.price * (1 - discountPercentage / 100);
        return this.updatePrice(id, Math.round(discountedPrice * 100) / 100);
    }
    /**
   * Get current product data
   */ async getCurrentProduct(id) {
        const response = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id));
        if (!response.success || !response.data) {
            throw new Error('Product not found');
        }
        return response.data;
    }
    /**
   * Map Product to UpdateProductRequest
   */ mapProductToUpdateRequest(product) {
        return {
            name: product.name,
            description: product.description,
            price: product.price,
            stock: product.stock,
            collectionId: product.collectionId,
            images: product.images,
            tags: product.tags
        };
    }
    /**
   * Validate update product request
   */ validateUpdateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Product name is required');
        }
        if (data.name.length > 200) {
            throw new Error('Product name must be 200 characters or less');
        }
        if (data.price <= 0) {
            throw new Error('Product price must be greater than 0');
        }
        if (data.stock < 0) {
            throw new Error('Product stock cannot be negative');
        }
        if (!data.collectionId) {
            throw new Error('Collection ID is required');
        }
        if (data.description && data.description.length > 1000) {
            throw new Error('Description must be 1000 characters or less');
        }
    }
}
const productUpdateService = new ProductUpdateService();
}}),
"[project]/src/services/api/products/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ __turbopack_context__.s({
    "ProductDeleteService": (()=>ProductDeleteService),
    "productDeleteService": (()=>productDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Delete a product by ID
   */ async delete(id) {
        this.logApiCall('DELETE', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id));
        return this.handleVoidResponse(this.client.delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Products.ById(id)));
    }
    /**
   * Delete multiple products
   */ async deleteBatch(ids) {
        this.logApiCall('DELETE', 'Batch Products', {
            count: ids.length
        });
        const promises = ids.map((id)=>this.delete(id));
        return Promise.all(promises);
    }
    /**
   * Soft delete - set stock to 0 instead of deleting
   */ async softDelete(id) {
        this.logApiCall('PATCH', `Soft Delete Product ${id}`);
        try {
            // Import here to avoid circular dependency
            const { productUpdateService } = await __turbopack_context__.r("[project]/src/services/api/products/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await productUpdateService.updateStock(id, 0);
            return true;
        } catch (error) {
            console.error('Failed to soft delete product:', error);
            return false;
        }
    }
    /**
   * Check if product can be safely deleted
   */ async canDelete(id) {
        try {
            // This would require checking if product is in any orders
            // For now, we'll assume it can be deleted
            // In a real implementation, you'd check:
            // 1. If product is in any pending orders
            // 2. If product is in any active shopping carts
            // 3. If product has any dependencies
            return {
                canDelete: true,
                hasOrders: false,
                isInActiveOrders: false
            };
        } catch (error) {
            console.error('Error checking if product can be deleted:', error);
            return {
                canDelete: false,
                reason: 'Error checking product dependencies'
            };
        }
    }
    /**
   * Safe delete - checks dependencies before deleting
   */ async safeDelete(id) {
        try {
            const deleteCheck = await this.canDelete(id);
            if (!deleteCheck.canDelete) {
                return {
                    success: false,
                    message: deleteCheck.reason || 'Product cannot be deleted'
                };
            }
            const deleted = await this.delete(id);
            if (deleted) {
                return {
                    success: true,
                    message: 'Product deleted successfully'
                };
            } else {
                return {
                    success: false,
                    message: 'Failed to delete product'
                };
            }
        } catch (error) {
            console.error('Error during safe delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Archive product (soft delete with archive flag)
   */ async archive(id) {
        try {
            // Set stock to 0 and add archive tag
            const { productUpdateService } = await __turbopack_context__.r("[project]/src/services/api/products/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await productUpdateService.updateStock(id, 0);
            await productUpdateService.addTags(id, [
                'archived'
            ]);
            return {
                success: true,
                message: 'Product archived successfully'
            };
        } catch (error) {
            console.error('Error archiving product:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to archive product'
            };
        }
    }
    /**
   * Restore archived product
   */ async restore(id, newStock = 1) {
        try {
            const { productUpdateService } = await __turbopack_context__.r("[project]/src/services/api/products/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await productUpdateService.updateStock(id, newStock);
            await productUpdateService.removeTags(id, [
                'archived'
            ]);
            return {
                success: true,
                message: 'Product restored successfully'
            };
        } catch (error) {
            console.error('Error restoring product:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to restore product'
            };
        }
    }
    /**
   * Delete all products in a collection
   */ async deleteByCollection(collectionId) {
        try {
            // Get all products in the collection
            const { productGetService } = await __turbopack_context__.r("[project]/src/services/api/products/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const products = await productGetService.getByCollection(collectionId);
            if (products.length === 0) {
                return {
                    success: true,
                    message: 'No products found in collection',
                    deletedCount: 0
                };
            }
            // Delete all products
            const productIds = products.map((p)=>p.id);
            const results = await this.deleteBatch(productIds);
            const deletedCount = results.filter((result)=>result).length;
            return {
                success: deletedCount === products.length,
                message: `Deleted ${deletedCount} of ${products.length} products`,
                deletedCount
            };
        } catch (error) {
            console.error('Error deleting products by collection:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to delete products',
                deletedCount: 0
            };
        }
    }
    /**
   * Delete out of stock products
   */ async deleteOutOfStock() {
        try {
            const { productGetService } = await __turbopack_context__.r("[project]/src/services/api/products/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const outOfStockProducts = await productGetService.getOutOfStock();
            if (outOfStockProducts.length === 0) {
                return {
                    success: true,
                    message: 'No out of stock products found',
                    deletedCount: 0
                };
            }
            const productIds = outOfStockProducts.map((p)=>p.id);
            const results = await this.deleteBatch(productIds);
            const deletedCount = results.filter((result)=>result).length;
            return {
                success: deletedCount === outOfStockProducts.length,
                message: `Deleted ${deletedCount} out of stock products`,
                deletedCount
            };
        } catch (error) {
            console.error('Error deleting out of stock products:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to delete out of stock products',
                deletedCount: 0
            };
        }
    }
}
const productDeleteService = new ProductDeleteService();
}}),
"[project]/src/services/api/products/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Product Services
__turbopack_context__.s({
    "ProductService": (()=>ProductService),
    "productService": (()=>productService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
class ProductService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["productGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["productPostService"];
    update = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["productUpdateService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["productDeleteService"];
}
const productService = new ProductService();
}}),
"[project]/src/services/api/products/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/products/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/products/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/productSpecifications/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "ProductSpecificationsGetService": (()=>ProductSpecificationsGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductSpecificationsGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all product specifications
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base));
    }
    /**
   * Get product specifications by ID
   */ async getById(id) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/${id}`);
        return this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/${id}`));
    }
    /**
   * Get product specifications by product ID
   */ async getByProductId(productId) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/product/${productId}`);
        try {
            return await this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/product/${productId}`));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
}
}}),
"[project]/src/services/api/productSpecifications/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductSpecificationsPostService": (()=>ProductSpecificationsPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductSpecificationsPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create new product specifications
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base, data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base, data));
    }
}
}}),
"[project]/src/services/api/productSpecifications/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductSpecificationsUpdateService": (()=>ProductSpecificationsUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductSpecificationsUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update product specifications
   */ async update(id, data) {
        this.logApiCall('PUT', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/${id}`, data);
        return this.handleResponse(this.client.put(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/${id}`, data));
    }
    /**
   * Delete product specifications
   */ async delete(id) {
        this.logApiCall('DELETE', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/${id}`);
        return this.handleResponse(this.client.delete(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductSpecifications.Base}/${id}`));
    }
}
}}),
"[project]/src/services/api/productSpecifications/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductSpecificationsService": (()=>ProductSpecificationsService),
    "productSpecificationsService": (()=>productSpecificationsService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productSpecifications/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productSpecifications/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productSpecifications/update.ts [app-ssr] (ecmascript)");
;
;
;
class ProductSpecificationsService {
    get = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProductSpecificationsGetService"]();
    post = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProductSpecificationsPostService"]();
    update = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProductSpecificationsUpdateService"]();
}
const productSpecificationsService = new ProductSpecificationsService();
}}),
"[project]/src/services/api/productDetails/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "ProductDetailsGetService": (()=>ProductDetailsGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductDetailsGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all product details
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base));
    }
    /**
   * Get product details by ID
   */ async getById(id) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/${id}`);
        return this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/${id}`));
    }
    /**
   * Get product details by product ID
   */ async getByProductId(productId) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/product/${productId}`);
        try {
            return await this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/product/${productId}`));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
}
}}),
"[project]/src/services/api/productDetails/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductDetailsPostService": (()=>ProductDetailsPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductDetailsPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create new product details
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base, data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base, data));
    }
}
}}),
"[project]/src/services/api/productDetails/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductDetailsUpdateService": (()=>ProductDetailsUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class ProductDetailsUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update product details
   */ async update(id, data) {
        this.logApiCall('PUT', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/${id}`, data);
        return this.handleResponse(this.client.put(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/${id}`, data));
    }
    /**
   * Delete product details
   */ async delete(id) {
        this.logApiCall('DELETE', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/${id}`);
        return this.handleResponse(this.client.delete(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].ProductDetails.Base}/${id}`));
    }
}
}}),
"[project]/src/services/api/productDetails/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProductDetailsService": (()=>ProductDetailsService),
    "productDetailsService": (()=>productDetailsService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productDetails/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productDetails/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productDetails/update.ts [app-ssr] (ecmascript)");
;
;
;
class ProductDetailsService {
    get = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProductDetailsGetService"]();
    post = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProductDetailsPostService"]();
    update = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProductDetailsUpdateService"]();
}
const productDetailsService = new ProductDetailsService();
}}),
"[project]/src/services/api/downloadableContent/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "DownloadableContentGetService": (()=>DownloadableContentGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class DownloadableContentGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all downloadable content
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base));
    }
    /**
   * Get downloadable content by ID
   */ async getById(id) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/${id}`);
        return this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/${id}`));
    }
    /**
   * Get downloadable content by product ID
   */ async getByProductId(productId) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/product/${productId}`);
        try {
            return await this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/product/${productId}`));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
}
}}),
"[project]/src/services/api/downloadableContent/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DownloadableContentPostService": (()=>DownloadableContentPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class DownloadableContentPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create new downloadable content
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base, data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base, data));
    }
}
}}),
"[project]/src/services/api/downloadableContent/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DownloadableContentUpdateService": (()=>DownloadableContentUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class DownloadableContentUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update downloadable content
   */ async update(id, data) {
        this.logApiCall('PUT', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/${id}`, data);
        return this.handleResponse(this.client.put(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/${id}`, data));
    }
    /**
   * Delete downloadable content
   */ async delete(id) {
        this.logApiCall('DELETE', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/${id}`);
        return this.handleResponse(this.client.delete(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].DownloadableContent.Base}/${id}`));
    }
}
}}),
"[project]/src/services/api/downloadableContent/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DownloadableContentService": (()=>DownloadableContentService),
    "downloadableContentService": (()=>downloadableContentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/downloadableContent/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/downloadableContent/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/downloadableContent/update.ts [app-ssr] (ecmascript)");
;
;
;
class DownloadableContentService {
    get = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DownloadableContentGetService"]();
    post = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DownloadableContentPostService"]();
    update = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DownloadableContentUpdateService"]();
}
const downloadableContentService = new DownloadableContentService();
}}),
"[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OrderGetService": (()=>OrderGetService),
    "orderGetService": (()=>orderGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class OrderGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all orders (summary)
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Base));
    }
    /**
   * Get order by ID
   */ async getById(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ById(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ById(id)));
    }
    /**
   * Get order with full details
   */ async getDetails(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Details(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Details(id)));
    }
    /**
   * Get orders by user ID
   */ async getByUser(userId) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ByUser(userId));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ByUser(userId)));
    }
    /**
   * Get orders by email
   */ async getByEmail(email) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ByEmail(email));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ByEmail(email)));
    }
    /**
   * Get orders by status
   */ async getByStatus(statusId) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ByStatus(statusId));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ByStatus(statusId)));
    }
    /**
   * Get pending orders
   */ async getPending() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Pending);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Pending));
    }
    /**
   * Get recent orders
   */ async getRecent(count = 10) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Recent, {
            count
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Recent, {
            count
        }));
    }
    /**
   * Get total revenue
   */ async getTotalRevenue() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Revenue.Total);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Revenue.Total));
    }
    /**
   * Get revenue by date range
   */ async getRevenueByDateRange(startDate, endDate) {
        const params = {
            startDate: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(startDate),
            endDate: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(endDate)
        };
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Revenue.Range, params);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Revenue.Range, params));
    }
    /**
   * Get orders with advanced filtering and pagination
   */ async getFiltered(filters) {
        const cleanFilters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].cleanObject(filters);
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Filter, cleanFilters);
        return this.handlePaginatedResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Filter, cleanFilters));
    }
    /**
   * Get orders with default pagination
   */ async getPaginated(pageNumber = 1, pageSize = 10, sortBy = 'createdAt', sortDirection = 'desc') {
        const filters = {
            pageNumber,
            pageSize,
            sortBy,
            sortDirection
        };
        return this.getFiltered(filters);
    }
    /**
   * Get orders by amount range
   */ async getByAmountRange(minAmount, maxAmount) {
        const filters = {
            minAmount,
            maxAmount,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get orders by payment method
   */ async getByPaymentMethod(paymentMethod) {
        const filters = {
            paymentMethod,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get orders by country
   */ async getByCountry(country) {
        const filters = {
            country,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get orders by city
   */ async getByCity(city) {
        const filters = {
            city,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get orders created within date range
   */ async getByDateRange(startDate, endDate) {
        const filters = {
            createdAfter: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(startDate),
            createdBefore: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(endDate),
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get orders by multiple statuses
   */ async getByStatuses(statusIds) {
        const promises = statusIds.map((statusId)=>this.getByStatus(statusId));
        const results = await Promise.all(promises);
        // Flatten and remove duplicates
        const allOrders = results.flat();
        const uniqueOrders = allOrders.filter((order, index, self)=>index === self.findIndex((o)=>o.id === order.id));
        return uniqueOrders;
    }
    /**
   * Get user's order history with pagination
   */ async getUserOrderHistory(userId, pageNumber = 1, pageSize = 10) {
        const filters = {
            userId,
            pageNumber,
            pageSize,
            sortBy: 'createdAt',
            sortDirection: 'desc'
        };
        return this.getFiltered(filters);
    }
    /**
   * Get guest orders by email with pagination
   */ async getGuestOrderHistory(email, pageNumber = 1, pageSize = 10) {
        const filters = {
            email,
            pageNumber,
            pageSize,
            sortBy: 'createdAt',
            sortDirection: 'desc'
        };
        return this.getFiltered(filters);
    }
    /**
   * Get order statistics
   */ async getStatistics() {
        try {
            const [totalRevenue, pendingOrders, allOrders] = await Promise.all([
                this.getTotalRevenue(),
                this.getPending(),
                this.getAll()
            ]);
            const totalOrders = allOrders.length;
            const completedOrders = allOrders.filter((order)=>order.statusName.toLowerCase().includes('delivered') || order.statusName.toLowerCase().includes('completed')).length;
            const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
            return {
                totalOrders,
                totalRevenue,
                averageOrderValue: Math.round(averageOrderValue * 100) / 100,
                pendingOrders: pendingOrders.length,
                completedOrders
            };
        } catch (error) {
            console.error('Error getting order statistics:', error);
            throw error;
        }
    }
}
const orderGetService = new OrderGetService();
}}),
"[project]/src/services/api/orders/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ __turbopack_context__.s({
    "OrderPostService": (()=>OrderPostService),
    "orderPostService": (()=>orderPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class OrderPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a new order
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Base, data);
        // Validate required fields
        this.validateCreateRequest(data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Base, data));
    }
    /**
   * Create a customer order (with user ID)
   */ async createCustomerOrder(userId, email, orderItems, shippingInfo = {}, paymentMethod) {
        const data = {
            userId,
            email,
            phoneNumber: shippingInfo.phoneNumber,
            country: shippingInfo.country,
            city: shippingInfo.city,
            zipCode: shippingInfo.zipCode,
            paymentMethod,
            orderItems
        };
        return this.create(data);
    }
    /**
   * Create a guest order (without user ID)
   */ async createGuestOrder(email, orderItems, shippingInfo = {}, paymentMethod) {
        const data = {
            userId: undefined,
            email,
            phoneNumber: shippingInfo.phoneNumber,
            country: shippingInfo.country,
            city: shippingInfo.city,
            zipCode: shippingInfo.zipCode,
            paymentMethod,
            orderItems
        };
        return this.create(data);
    }
    /**
   * Create order from shopping cart
   */ async createFromCart(cartItems, customerInfo, paymentMethod) {
        const orderItems = cartItems.map((item)=>({
                productId: item.productId,
                quantity: item.quantity
            }));
        const data = {
            userId: customerInfo.userId,
            email: customerInfo.email,
            phoneNumber: customerInfo.phoneNumber,
            country: customerInfo.country,
            city: customerInfo.city,
            zipCode: customerInfo.zipCode,
            paymentMethod,
            orderItems
        };
        return this.create(data);
    }
    /**
   * Create single item order (quick order)
   */ async createQuickOrder(productId, quantity, customerInfo, paymentMethod) {
        const orderItems = [
            {
                productId,
                quantity
            }
        ];
        return this.createFromCart([
            {
                productId,
                quantity
            }
        ], customerInfo, paymentMethod);
    }
    /**
   * Create bulk order (multiple products with quantities)
   */ async createBulkOrder(products, customerInfo, paymentMethod) {
        return this.createFromCart(products, customerInfo, paymentMethod);
    }
    /**
   * Create repeat order (duplicate previous order)
   */ async createRepeatOrder(originalOrderId, customerInfo, paymentMethod) {
        try {
            // Get the original order details
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const originalOrder = await orderGetService.getDetails(originalOrderId);
            // Create new order items from original order
            const orderItems = originalOrder.orderItems.map((item)=>({
                    productId: item.productId,
                    quantity: item.quantity
                }));
            const data = {
                userId: customerInfo.userId,
                email: customerInfo.email,
                phoneNumber: customerInfo.phoneNumber,
                country: customerInfo.country,
                city: customerInfo.city,
                zipCode: customerInfo.zipCode,
                paymentMethod,
                orderItems
            };
            return this.create(data);
        } catch (error) {
            console.error('Error creating repeat order:', error);
            throw new Error('Failed to create repeat order');
        }
    }
    /**
   * Create order with validation and stock check
   */ async createWithValidation(data) {
        try {
            // Validate stock availability (this would require product service)
            const stockValidation = await this.validateStock(data.orderItems);
            if (!stockValidation.valid) {
                return {
                    success: false,
                    errors: stockValidation.errors
                };
            }
            const order = await this.create(data);
            return {
                success: true,
                order
            };
        } catch (error) {
            console.error('Error creating order with validation:', error);
            return {
                success: false,
                errors: [
                    error instanceof Error ? error.message : 'Unknown error occurred'
                ]
            };
        }
    }
    /**
   * Validate stock availability for order items
   */ async validateStock(orderItems) {
        try {
            // This would require product service to check stock
            // For now, we'll assume stock is valid
            // In a real implementation, you'd check each product's stock
            const errors = [];
            for (const item of orderItems){
                if (item.quantity <= 0) {
                    errors.push(`Invalid quantity for product ${item.productId}`);
                }
            }
            return {
                valid: errors.length === 0,
                errors: errors.length > 0 ? errors : undefined
            };
        } catch (error) {
            console.error('Error validating stock:', error);
            return {
                valid: false,
                errors: [
                    'Error validating stock availability'
                ]
            };
        }
    }
    /**
   * Validate create order request
   */ validateCreateRequest(data) {
        if (!data.email || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].isValidEmail(data.email)) {
            throw new Error('Valid email is required');
        }
        if (!data.orderItems || data.orderItems.length === 0) {
            throw new Error('At least one order item is required');
        }
        // Validate order items
        for (const item of data.orderItems){
            if (!item.productId || item.productId <= 0) {
                throw new Error('Valid product ID is required for all order items');
            }
            if (!item.quantity || item.quantity <= 0) {
                throw new Error('Quantity must be greater than 0 for all order items');
            }
        }
        // Validate phone number format if provided
        if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {
            throw new Error('Invalid phone number format');
        }
        // Validate zip code format if provided
        if (data.zipCode && data.zipCode.length > 20) {
            throw new Error('Zip code must be 20 characters or less');
        }
    }
    /**
   * Validate phone number format
   */ isValidPhoneNumber(phone) {
        // Basic phone number validation (can be enhanced)
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
}
const orderPostService = new OrderPostService();
}}),
"[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OrderUpdateService": (()=>OrderUpdateService),
    "orderUpdateService": (()=>orderUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class OrderUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update order status
   */ async updateStatus(id, statusId) {
        this.logApiCall('PATCH', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.UpdateStatus(id), {
            statusId
        });
        const data = {
            statusId
        };
        return this.handleVoidResponse(this.client.patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.UpdateStatus(id), data));
    }
    /**
   * Cancel an order
   */ async cancel(id) {
        this.logApiCall('PATCH', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Cancel(id));
        return this.handleVoidResponse(this.client.patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.Cancel(id)));
    }
    /**
   * Mark order as confirmed
   */ async confirm(id) {
        return this.updateStatus(id, 2); // Status ID 2 = Confirmed
    }
    /**
   * Mark order as processing
   */ async markAsProcessing(id) {
        return this.updateStatus(id, 3); // Status ID 3 = Processing
    }
    /**
   * Mark order as shipped
   */ async markAsShipped(id) {
        return this.updateStatus(id, 4); // Status ID 4 = Shipped
    }
    /**
   * Mark order as delivered
   */ async markAsDelivered(id) {
        return this.updateStatus(id, 5); // Status ID 5 = Delivered
    }
    /**
   * Mark order as returned
   */ async markAsReturned(id) {
        return this.updateStatus(id, 7); // Status ID 7 = Returned
    }
    /**
   * Mark order as refunded
   */ async markAsRefunded(id) {
        return this.updateStatus(id, 8); // Status ID 8 = Refunded
    }
    /**
   * Update payment status to completed
   */ async markPaymentCompleted(id) {
        return this.updateStatus(id, 10); // Status ID 10 = Payment Completed
    }
    /**
   * Update payment status to failed
   */ async markPaymentFailed(id) {
        return this.updateStatus(id, 11); // Status ID 11 = Payment Failed
    }
    /**
   * Update payment status to refunded
   */ async markPaymentRefunded(id) {
        return this.updateStatus(id, 12); // Status ID 12 = Payment Refunded
    }
    /**
   * Bulk update order statuses
   */ async bulkUpdateStatus(orderIds, statusId) {
        this.logApiCall('PATCH', 'Bulk Update Order Status', {
            orderIds,
            statusId,
            count: orderIds.length
        });
        const results = await Promise.allSettled(orderIds.map((id)=>this.updateStatus(id, statusId)));
        const successful = results.filter((result)=>result.status === 'fulfilled' && result.value === true).length;
        const errors = results.filter((result)=>result.status === 'rejected').map((result, index)=>`Order ${orderIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`);
        return {
            success: successful === orderIds.length,
            updatedCount: successful,
            errors
        };
    }
    /**
   * Process order through workflow (pending -> confirmed -> processing)
   */ async processOrder(id) {
        try {
            // Get current order status
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const order = await orderGetService.getById(id);
            switch(order.statusId){
                case 1:
                    await this.confirm(id);
                    return {
                        success: true,
                        currentStatus: 'Confirmed',
                        message: 'Order confirmed successfully'
                    };
                case 2:
                    await this.markAsProcessing(id);
                    return {
                        success: true,
                        currentStatus: 'Processing',
                        message: 'Order moved to processing'
                    };
                case 3:
                    await this.markAsShipped(id);
                    return {
                        success: true,
                        currentStatus: 'Shipped',
                        message: 'Order marked as shipped'
                    };
                case 4:
                    await this.markAsDelivered(id);
                    return {
                        success: true,
                        currentStatus: 'Delivered',
                        message: 'Order marked as delivered'
                    };
                default:
                    return {
                        success: false,
                        currentStatus: order.status.statusName,
                        message: 'Order cannot be processed further'
                    };
            }
        } catch (error) {
            console.error('Error processing order:', error);
            return {
                success: false,
                currentStatus: 'Unknown',
                message: error instanceof Error ? error.message : 'Failed to process order'
            };
        }
    }
    /**
   * Reverse order status (for corrections)
   */ async reverseStatus(id) {
        try {
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const order = await orderGetService.getById(id);
            let newStatusId;
            let statusName;
            switch(order.statusId){
                case 5:
                    newStatusId = 4;
                    statusName = 'Shipped';
                    break;
                case 4:
                    newStatusId = 3;
                    statusName = 'Processing';
                    break;
                case 3:
                    newStatusId = 2;
                    statusName = 'Confirmed';
                    break;
                case 2:
                    newStatusId = 1;
                    statusName = 'Pending';
                    break;
                default:
                    return {
                        success: false,
                        previousStatus: order.status.statusName,
                        message: 'Order status cannot be reversed'
                    };
            }
            await this.updateStatus(id, newStatusId);
            return {
                success: true,
                previousStatus: statusName,
                message: `Order status reversed to ${statusName}`
            };
        } catch (error) {
            console.error('Error reversing order status:', error);
            return {
                success: false,
                previousStatus: 'Unknown',
                message: error instanceof Error ? error.message : 'Failed to reverse order status'
            };
        }
    }
    /**
   * Check if status update is valid
   */ async canUpdateStatus(id, newStatusId) {
        try {
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const order = await orderGetService.getById(id);
            // Define valid status transitions
            const validTransitions = {
                1: [
                    2,
                    6
                ],
                2: [
                    3,
                    6
                ],
                3: [
                    4,
                    6
                ],
                4: [
                    5,
                    7
                ],
                5: [
                    7
                ],
                6: [],
                7: [
                    8
                ],
                8: [] // Refunded (final)
            };
            const allowedStatuses = validTransitions[order.statusId] || [];
            const canUpdate = allowedStatuses.includes(newStatusId);
            return {
                canUpdate,
                currentStatus: order.status.statusName,
                reason: canUpdate ? undefined : 'Invalid status transition'
            };
        } catch (error) {
            console.error('Error checking status update validity:', error);
            return {
                canUpdate: false,
                reason: 'Error checking order status'
            };
        }
    }
    /**
   * Safe status update with validation
   */ async safeUpdateStatus(id, statusId) {
        try {
            const validation = await this.canUpdateStatus(id, statusId);
            if (!validation.canUpdate) {
                return {
                    success: false,
                    message: validation.reason || 'Status update not allowed'
                };
            }
            const updated = await this.updateStatus(id, statusId);
            return {
                success: updated,
                message: updated ? 'Order status updated successfully' : 'Failed to update order status'
            };
        } catch (error) {
            console.error('Error in safe status update:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
}
const orderUpdateService = new OrderUpdateService();
}}),
"[project]/src/services/api/orders/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OrderDeleteService": (()=>OrderDeleteService),
    "orderDeleteService": (()=>orderDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class OrderDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Delete an order by ID
   */ async delete(id) {
        this.logApiCall('DELETE', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ById(id));
        return this.handleVoidResponse(this.client.delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Orders.ById(id)));
    }
    /**
   * Delete multiple orders
   */ async deleteBatch(ids) {
        this.logApiCall('DELETE', 'Batch Orders', {
            count: ids.length
        });
        const promises = ids.map((id)=>this.delete(id));
        return Promise.all(promises);
    }
    /**
   * Check if order can be safely deleted
   */ async canDelete(id) {
        try {
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const order = await orderGetService.getById(id);
            // Orders can typically only be deleted if they are:
            // - Pending (status 1)
            // - Cancelled (status 6)
            // - In some cases, failed payment orders
            const deletableStatuses = [
                1,
                6,
                11
            ]; // Pending, Cancelled, Payment Failed
            const canDelete = deletableStatuses.includes(order.statusId);
            let reason;
            if (!canDelete) {
                switch(order.statusId){
                    case 2:
                    case 3:
                    case 4:
                        reason = 'Cannot delete confirmed or processing orders';
                        break;
                    case 5:
                        reason = 'Cannot delete delivered orders';
                        break;
                    case 7:
                    case 8:
                        reason = 'Cannot delete returned or refunded orders';
                        break;
                    default:
                        reason = 'Order cannot be deleted in current status';
                }
            }
            return {
                canDelete,
                reason,
                orderStatus: order.status.statusName
            };
        } catch (error) {
            console.error('Error checking if order can be deleted:', error);
            return {
                canDelete: false,
                reason: 'Error checking order status'
            };
        }
    }
    /**
   * Safe delete - checks if order can be deleted first
   */ async safeDelete(id) {
        try {
            const deleteCheck = await this.canDelete(id);
            if (!deleteCheck.canDelete) {
                return {
                    success: false,
                    message: deleteCheck.reason || 'Order cannot be deleted'
                };
            }
            const deleted = await this.delete(id);
            if (deleted) {
                return {
                    success: true,
                    message: 'Order deleted successfully'
                };
            } else {
                return {
                    success: false,
                    message: 'Failed to delete order'
                };
            }
        } catch (error) {
            console.error('Error during safe delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Cancel order instead of deleting (safer option)
   */ async cancelInsteadOfDelete(id) {
        try {
            const { orderUpdateService } = await __turbopack_context__.r("[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const cancelled = await orderUpdateService.cancel(id);
            return {
                success: cancelled,
                message: cancelled ? 'Order cancelled successfully' : 'Failed to cancel order'
            };
        } catch (error) {
            console.error('Error cancelling order:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to cancel order'
            };
        }
    }
    /**
   * Delete orders by status (bulk operation)
   */ async deleteByStatus(statusId) {
        try {
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const orders = await orderGetService.getByStatus(statusId);
            if (orders.length === 0) {
                return {
                    success: true,
                    message: 'No orders found with specified status',
                    deletedCount: 0,
                    totalCount: 0
                };
            }
            // Check if any orders can be deleted
            const deletableOrders = [];
            for (const order of orders){
                const canDelete = await this.canDelete(order.id);
                if (canDelete.canDelete) {
                    deletableOrders.push(order.id);
                }
            }
            if (deletableOrders.length === 0) {
                return {
                    success: false,
                    message: 'No orders can be deleted in current status',
                    deletedCount: 0,
                    totalCount: orders.length
                };
            }
            const results = await this.deleteBatch(deletableOrders);
            const deletedCount = results.filter((result)=>result).length;
            return {
                success: deletedCount === deletableOrders.length,
                message: `Deleted ${deletedCount} of ${deletableOrders.length} eligible orders`,
                deletedCount,
                totalCount: orders.length
            };
        } catch (error) {
            console.error('Error deleting orders by status:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to delete orders',
                deletedCount: 0,
                totalCount: 0
            };
        }
    }
    /**
   * Delete old cancelled orders (cleanup operation)
   */ async deleteOldCancelledOrders(olderThanDays = 30) {
        try {
            const { orderGetService } = await __turbopack_context__.r("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
            // Get cancelled orders
            const cancelledOrders = await orderGetService.getByStatus(6); // Status 6 = Cancelled
            // Filter orders older than cutoff date
            const oldOrders = cancelledOrders.filter((order)=>new Date(order.createdAt) < cutoffDate);
            if (oldOrders.length === 0) {
                return {
                    success: true,
                    message: `No cancelled orders older than ${olderThanDays} days found`,
                    deletedCount: 0
                };
            }
            const orderIds = oldOrders.map((order)=>order.id);
            const results = await this.deleteBatch(orderIds);
            const deletedCount = results.filter((result)=>result).length;
            return {
                success: deletedCount === oldOrders.length,
                message: `Deleted ${deletedCount} old cancelled orders`,
                deletedCount
            };
        } catch (error) {
            console.error('Error deleting old cancelled orders:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to delete old orders',
                deletedCount: 0
            };
        }
    }
    /**
   * Archive order instead of deleting (move to archive status)
   */ async archive(id) {
        try {
            // This would require adding an "Archived" status to the system
            // For now, we'll use the existing status system
            const { orderUpdateService } = await __turbopack_context__.r("[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            // Check if order can be archived (similar rules to deletion)
            const canDelete = await this.canDelete(id);
            if (!canDelete.canDelete) {
                return {
                    success: false,
                    message: canDelete.reason || 'Order cannot be archived'
                };
            }
            // In a real system, you might have a specific "Archived" status
            // For now, we'll just mark it as cancelled
            const archived = await orderUpdateService.cancel(id);
            return {
                success: archived,
                message: archived ? 'Order archived successfully' : 'Failed to archive order'
            };
        } catch (error) {
            console.error('Error archiving order:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to archive order'
            };
        }
    }
}
const orderDeleteService = new OrderDeleteService();
}}),
"[project]/src/services/api/orders/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Order Services
__turbopack_context__.s({
    "OrderService": (()=>OrderService),
    "orderService": (()=>orderService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
class OrderService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderPostService"];
    update = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderUpdateService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["orderDeleteService"];
}
const orderService = new OrderService();
}}),
"[project]/src/services/api/orders/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/orders/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/orders/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/users/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ __turbopack_context__.s({
    "UserGetService": (()=>UserGetService),
    "userGetService": (()=>userGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
;
class UserGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all users
   */ async getAll() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Base);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Base));
    }
    /**
   * Get user by ID
   */ async getById(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id)));
    }
    /**
   * Get user by email
   */ async getByEmail(email) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ByEmail(email));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ByEmail(email)));
    }
    /**
   * Get users by role
   */ async getByRole(role) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ByRole(role));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ByRole(role)));
    }
    /**
   * Get active users
   */ async getActive() {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Active);
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Active));
    }
    /**
   * Get recent users
   */ async getRecent(count = 10) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Recent, {
            count
        });
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Recent, {
            count
        }));
    }
    /**
   * Get user with orders
   */ async getWithOrders(id) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.WithOrders(id));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.WithOrders(id)));
    }
    /**
   * Check if email exists
   */ async emailExists(email) {
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.EmailExists(email));
        return this.handleResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.EmailExists(email)));
    }
    /**
   * Get users with advanced filtering and pagination
   */ async getFiltered(filters) {
        const cleanFilters = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].cleanObject(filters);
        this.logApiCall('GET', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Filter, cleanFilters);
        return this.handlePaginatedResponse(this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Filter, cleanFilters));
    }
    /**
   * Get users with default pagination
   */ async getPaginated(pageNumber = 1, pageSize = 10, sortBy = 'createdAt', sortDirection = 'desc') {
        const filters = {
            pageNumber,
            pageSize,
            sortBy,
            sortDirection
        };
        return this.getFiltered(filters);
    }
    /**
   * Get admin users
   */ async getAdmins() {
        return this.getByRole('admin');
    }
    /**
   * Get customer users
   */ async getCustomers() {
        return this.getByRole('customer');
    }
    /**
   * Get inactive users
   */ async getInactive() {
        const filters = {
            active: false,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get users by country
   */ async getByCountry(country) {
        const filters = {
            country,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get users by city
   */ async getByCity(city) {
        const filters = {
            city,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Search users by name
   */ async searchByName(name) {
        const filters = {
            name,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Search users by email pattern
   */ async searchByEmail(emailPattern) {
        const filters = {
            email: emailPattern,
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get users created within date range
   */ async getByDateRange(startDate, endDate) {
        const filters = {
            createdAfter: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(startDate),
            createdBefore: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].formatDate(endDate),
            pageSize: 100
        };
        const result = await this.getFiltered(filters);
        return result.data;
    }
    /**
   * Get user statistics
   */ async getStatistics() {
        try {
            const [allUsers, activeUsers, adminUsers, customerUsers] = await Promise.all([
                this.getAll(),
                this.getActive(),
                this.getAdmins(),
                this.getCustomers()
            ]);
            // Calculate recent signups (last 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const recentUsers = await this.getByDateRange(thirtyDaysAgo, new Date());
            return {
                totalUsers: allUsers.length,
                activeUsers: activeUsers.length,
                inactiveUsers: allUsers.length - activeUsers.length,
                adminUsers: adminUsers.length,
                customerUsers: customerUsers.length,
                recentSignups: recentUsers.length
            };
        } catch (error) {
            console.error('Error getting user statistics:', error);
            throw error;
        }
    }
    /**
   * Get user profile with additional data
   */ async getProfile(id) {
        try {
            const userWithOrders = await this.getWithOrders(id);
            // Calculate order statistics (this would require order data in the response)
            // For now, we'll return basic user data
            return {
                user: userWithOrders,
                orderCount: 0,
                totalSpent: 0,
                lastOrderDate: undefined // Would be from most recent order
            };
        } catch (error) {
            console.error('Error getting user profile:', error);
            throw error;
        }
    }
    /**
   * Validate user exists and is active
   */ async validateUser(id) {
        try {
            const user = await this.getById(id);
            return {
                exists: true,
                active: user.active,
                user
            };
        } catch (error) {
            return {
                exists: false,
                active: false
            };
        }
    }
    /**
   * Validate admin credentials
   */ async validateAdminCredentials(email, password) {
        try {
            // First get the user by email
            const user = await this.getByEmail(email);
            // Check if user exists and is an admin
            if (!user || user.role !== 'admin' || !user.active) {
                return null;
            }
            // For now, we'll use a simple validation approach
            // In a real application, you'd want to implement proper JWT authentication
            // Since the backend doesn't have a login endpoint, we'll validate against known admin
            const isValidAdmin = email === '<EMAIL>' && password === '132Trent@!';
            return isValidAdmin ? user : null;
        } catch (error) {
            console.error('Error validating admin credentials:', error);
            return null;
        }
    }
}
const userGetService = new UserGetService();
}}),
"[project]/src/services/api/users/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UserPostService": (()=>UserPostService),
    "userPostService": (()=>userPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class UserPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a new user
   */ async create(data) {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Base, {
            ...data,
            password: '[HIDDEN]'
        });
        // Validate required fields
        this.validateCreateRequest(data);
        return this.handleResponse(this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Base, data));
    }
    /**
   * Create a customer user
   */ async createCustomer(email, password, name, phoneNumber, address) {
        const data = {
            role: 'customer',
            email,
            password,
            name,
            phoneNumber,
            country: address?.country,
            city: address?.city,
            zipCode: address?.zipCode,
            active: true
        };
        return this.create(data);
    }
    /**
   * Create an admin user
   */ async createAdmin(email, password, name, phoneNumber) {
        const data = {
            role: 'admin',
            email,
            password,
            name,
            phoneNumber,
            active: true
        };
        return this.create(data);
    }
    /**
   * Register a new customer (public registration)
   */ async register(email, password, name, phoneNumber) {
        // Check if email already exists
        const { userGetService } = await __turbopack_context__.r("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        const emailExists = await userGetService.emailExists(email);
        if (emailExists) {
            throw new Error('Email already exists');
        }
        return this.createCustomer(email, password, name, phoneNumber);
    }
    /**
   * Create guest user (for guest checkout)
   */ async createGuest(email) {
        const data = {
            role: 'guest',
            email,
            password: this.generateRandomPassword(),
            active: true
        };
        return this.create(data);
    }
    /**
   * Create multiple users in batch
   */ async createBatch(users) {
        this.logApiCall('POST', 'Batch Users', {
            count: users.length
        });
        const promises = users.map((user)=>this.create(user));
        return Promise.all(promises);
    }
    /**
   * Import users from CSV data
   */ async importFromCsv(csvData) {
        const results = {
            success: true,
            imported: 0,
            failed: 0,
            errors: []
        };
        for (const [index, userData] of csvData.entries()){
            try {
                const createData = {
                    role: userData.role || 'customer',
                    email: userData.email,
                    password: this.generateRandomPassword(),
                    name: userData.name,
                    phoneNumber: userData.phoneNumber,
                    country: userData.country,
                    city: userData.city,
                    zipCode: userData.zipCode,
                    active: true
                };
                await this.create(createData);
                results.imported++;
            } catch (error) {
                results.failed++;
                results.errors.push(`Row ${index + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        results.success = results.failed === 0;
        return results;
    }
    /**
   * Create user with email verification
   */ async createWithVerification(data) {
        try {
            const user = await this.create(data);
            // In a real application, you would:
            // 1. Generate a verification token
            // 2. Send verification email
            // 3. Set user as inactive until verified
            return {
                user,
                verificationRequired: true,
                verificationToken: this.generateVerificationToken()
            };
        } catch (error) {
            console.error('Error creating user with verification:', error);
            throw error;
        }
    }
    /**
   * Create user with social login data
   */ async createFromSocialLogin(provider, socialId, email, name) {
        const data = {
            role: 'customer',
            email,
            password: this.generateRandomPassword(),
            name,
            active: true
        };
        // In a real application, you would also store the social provider info
        return this.create(data);
    }
    /**
   * Generate random password for system-created users
   */ generateRandomPassword() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let password = '';
        for(let i = 0; i < 12; i++){
            password += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return password;
    }
    /**
   * Generate verification token
   */ generateVerificationToken() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    /**
   * Validate create user request
   */ validateCreateRequest(data) {
        if (!data.email || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].isValidEmail(data.email)) {
            throw new Error('Valid email is required');
        }
        if (!data.password || data.password.length < 6) {
            throw new Error('Password must be at least 6 characters long');
        }
        if (!data.role || ![
            'admin',
            'customer',
            'guest'
        ].includes(data.role)) {
            throw new Error('Valid role is required (admin, customer, or guest)');
        }
        // Validate phone number format if provided
        if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {
            throw new Error('Invalid phone number format');
        }
        // Validate name length if provided
        if (data.name && data.name.length > 100) {
            throw new Error('Name must be 100 characters or less');
        }
        // Validate address fields if provided
        if (data.country && data.country.length > 100) {
            throw new Error('Country must be 100 characters or less');
        }
        if (data.city && data.city.length > 100) {
            throw new Error('City must be 100 characters or less');
        }
        if (data.zipCode && data.zipCode.length > 20) {
            throw new Error('Zip code must be 20 characters or less');
        }
        // Password strength validation
        if (!this.isStrongPassword(data.password)) {
            throw new Error('Password must contain at least one uppercase letter, one lowercase letter, and one number');
        }
    }
    /**
   * Validate phone number format
   */ isValidPhoneNumber(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
    /**
   * Check password strength
   */ isStrongPassword(password) {
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        return hasUpperCase && hasLowerCase && hasNumbers;
    }
}
const userPostService = new UserPostService();
}}),
"[project]/src/services/api/users/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "UserUpdateService": (()=>UserUpdateService),
    "userUpdateService": (()=>userUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class UserUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update an existing user
   */ async update(id, data) {
        this.logApiCall('PUT', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id), data);
        // Validate required fields
        this.validateUpdateRequest(data);
        return this.handleResponse(this.client.put(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id), data));
    }
    /**
   * Activate a user
   */ async activate(id) {
        this.logApiCall('PATCH', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Activate(id));
        return this.handleVoidResponse(this.client.patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Activate(id)));
    }
    /**
   * Deactivate a user
   */ async deactivate(id) {
        this.logApiCall('PATCH', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Deactivate(id));
        return this.handleVoidResponse(this.client.patch(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.Deactivate(id)));
    }
    /**
   * Update user profile information
   */ async updateProfile(id, name, phoneNumber, address) {
        const currentUser = await this.getCurrentUser(id);
        const data = {
            role: currentUser.role,
            name: name ?? currentUser.name,
            phoneNumber: phoneNumber ?? currentUser.phoneNumber,
            country: address?.country ?? currentUser.country,
            city: address?.city ?? currentUser.city,
            zipCode: address?.zipCode ?? currentUser.zipCode,
            active: currentUser.active
        };
        return this.update(id, data);
    }
    /**
   * Update user contact information
   */ async updateContact(id, phoneNumber, address) {
        const currentUser = await this.getCurrentUser(id);
        const data = {
            role: currentUser.role,
            name: currentUser.name,
            phoneNumber: phoneNumber ?? currentUser.phoneNumber,
            country: address?.country ?? currentUser.country,
            city: address?.city ?? currentUser.city,
            zipCode: address?.zipCode ?? currentUser.zipCode,
            active: currentUser.active
        };
        return this.update(id, data);
    }
    /**
   * Update user role
   */ async updateRole(id, newRole) {
        if (![
            'admin',
            'customer',
            'guest'
        ].includes(newRole)) {
            throw new Error('Invalid role. Must be admin, customer, or guest');
        }
        const currentUser = await this.getCurrentUser(id);
        const data = {
            role: newRole,
            name: currentUser.name,
            phoneNumber: currentUser.phoneNumber,
            country: currentUser.country,
            city: currentUser.city,
            zipCode: currentUser.zipCode,
            active: currentUser.active
        };
        return this.update(id, data);
    }
    /**
   * Update user address
   */ async updateAddress(id, country, city, zipCode) {
        const currentUser = await this.getCurrentUser(id);
        const data = {
            role: currentUser.role,
            name: currentUser.name,
            phoneNumber: currentUser.phoneNumber,
            country: country ?? currentUser.country,
            city: city ?? currentUser.city,
            zipCode: zipCode ?? currentUser.zipCode,
            active: currentUser.active
        };
        return this.update(id, data);
    }
    /**
   * Toggle user active status
   */ async toggleActiveStatus(id) {
        const currentUser = await this.getCurrentUser(id);
        if (currentUser.active) {
            await this.deactivate(id);
        } else {
            await this.activate(id);
        }
        // Return updated user
        return this.getCurrentUser(id);
    }
    /**
   * Bulk update user roles
   */ async bulkUpdateRole(userIds, newRole) {
        this.logApiCall('PUT', 'Bulk Update User Roles', {
            userIds,
            newRole,
            count: userIds.length
        });
        const results = await Promise.allSettled(userIds.map((id)=>this.updateRole(id, newRole)));
        const successful = results.filter((result)=>result.status === 'fulfilled').length;
        const errors = results.filter((result)=>result.status === 'rejected').map((result, index)=>`User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`);
        return {
            success: successful === userIds.length,
            updatedCount: successful,
            errors
        };
    }
    /**
   * Bulk activate users
   */ async bulkActivate(userIds) {
        this.logApiCall('PATCH', 'Bulk Activate Users', {
            userIds,
            count: userIds.length
        });
        const results = await Promise.allSettled(userIds.map((id)=>this.activate(id)));
        const successful = results.filter((result)=>result.status === 'fulfilled' && result.value === true).length;
        const errors = results.filter((result)=>result.status === 'rejected').map((result, index)=>`User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`);
        return {
            success: successful === userIds.length,
            activatedCount: successful,
            errors
        };
    }
    /**
   * Bulk deactivate users
   */ async bulkDeactivate(userIds) {
        this.logApiCall('PATCH', 'Bulk Deactivate Users', {
            userIds,
            count: userIds.length
        });
        const results = await Promise.allSettled(userIds.map((id)=>this.deactivate(id)));
        const successful = results.filter((result)=>result.status === 'fulfilled' && result.value === true).length;
        const errors = results.filter((result)=>result.status === 'rejected').map((result, index)=>`User ${userIds[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`);
        return {
            success: successful === userIds.length,
            deactivatedCount: successful,
            errors
        };
    }
    /**
   * Promote user to admin
   */ async promoteToAdmin(id) {
        return this.updateRole(id, 'admin');
    }
    /**
   * Demote admin to customer
   */ async demoteToCustomer(id) {
        return this.updateRole(id, 'customer');
    }
    /**
   * Update user preferences (placeholder for future implementation)
   */ async updatePreferences(id, preferences) {
        // This would require extending the user model to include preferences
        // For now, we'll just return the current user
        console.log('User preferences update not implemented:', preferences);
        return this.getCurrentUser(id);
    }
    /**
   * Get current user data
   */ async getCurrentUser(id) {
        const response = await this.client.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id));
        if (!response.success || !response.data) {
            throw new Error('User not found');
        }
        return response.data;
    }
    /**
   * Validate update user request
   */ validateUpdateRequest(data) {
        if (!data.role || ![
            'admin',
            'customer',
            'guest'
        ].includes(data.role)) {
            throw new Error('Valid role is required (admin, customer, or guest)');
        }
        // Validate phone number format if provided
        if (data.phoneNumber && !this.isValidPhoneNumber(data.phoneNumber)) {
            throw new Error('Invalid phone number format');
        }
        // Validate name length if provided
        if (data.name && data.name.length > 100) {
            throw new Error('Name must be 100 characters or less');
        }
        // Validate address fields if provided
        if (data.country && data.country.length > 100) {
            throw new Error('Country must be 100 characters or less');
        }
        if (data.city && data.city.length > 100) {
            throw new Error('City must be 100 characters or less');
        }
        if (data.zipCode && data.zipCode.length > 20) {
            throw new Error('Zip code must be 20 characters or less');
        }
    }
    /**
   * Validate phone number format
   */ isValidPhoneNumber(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
}
const userUpdateService = new UserUpdateService();
}}),
"[project]/src/services/api/users/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UserDeleteService": (()=>UserDeleteService),
    "userDeleteService": (()=>userDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class UserDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Delete a user by ID
   */ async delete(id) {
        this.logApiCall('DELETE', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id));
        return this.handleVoidResponse(this.client.delete(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Users.ById(id)));
    }
    /**
   * Delete multiple users
   */ async deleteBatch(ids) {
        this.logApiCall('DELETE', 'Batch Users', {
            count: ids.length
        });
        const promises = ids.map((id)=>this.delete(id));
        return Promise.all(promises);
    }
    /**
   * Soft delete - deactivate user instead of deleting
   */ async softDelete(id) {
        this.logApiCall('PATCH', `Soft Delete User ${id}`);
        try {
            const { userUpdateService } = await __turbopack_context__.r("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await userUpdateService.deactivate(id);
            return true;
        } catch (error) {
            console.error('Failed to soft delete user:', error);
            return false;
        }
    }
    /**
   * Check if user can be safely deleted
   */ async canDelete(id) {
        try {
            const { userGetService } = await __turbopack_context__.r("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const user = await userGetService.getById(id);
            // Check if user is admin
            const isAdmin = user.role === 'admin';
            // Check if user has orders (this would require order service integration)
            // For now, we'll assume users can be deleted
            const hasOrders = false; // Would check order history
            // Admins typically shouldn't be deleted
            const canDelete = !isAdmin && !hasOrders;
            let reason;
            if (!canDelete) {
                if (isAdmin) {
                    reason = 'Cannot delete admin users';
                } else if (hasOrders) {
                    reason = 'Cannot delete users with order history';
                }
            }
            return {
                canDelete,
                reason,
                hasOrders,
                isAdmin
            };
        } catch (error) {
            console.error('Error checking if user can be deleted:', error);
            return {
                canDelete: false,
                reason: 'Error checking user dependencies'
            };
        }
    }
    /**
   * Safe delete - checks dependencies before deleting
   */ async safeDelete(id) {
        try {
            const deleteCheck = await this.canDelete(id);
            if (!deleteCheck.canDelete) {
                return {
                    success: false,
                    message: deleteCheck.reason || 'User cannot be deleted'
                };
            }
            const deleted = await this.delete(id);
            if (deleted) {
                return {
                    success: true,
                    message: 'User deleted successfully'
                };
            } else {
                return {
                    success: false,
                    message: 'Failed to delete user'
                };
            }
        } catch (error) {
            console.error('Error during safe delete:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    /**
   * Archive user (soft delete with archive flag)
   */ async archive(id) {
        try {
            // Deactivate the user
            const { userUpdateService } = await __turbopack_context__.r("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await userUpdateService.deactivate(id);
            return {
                success: true,
                message: 'User archived successfully'
            };
        } catch (error) {
            console.error('Error archiving user:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to archive user'
            };
        }
    }
    /**
   * Restore archived user
   */ async restore(id) {
        try {
            const { userUpdateService } = await __turbopack_context__.r("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            await userUpdateService.activate(id);
            return {
                success: true,
                message: 'User restored successfully'
            };
        } catch (error) {
            console.error('Error restoring user:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to restore user'
            };
        }
    }
    /**
   * Delete inactive users (cleanup operation)
   */ async deleteInactiveUsers(inactiveDays = 365) {
        try {
            const { userGetService } = await __turbopack_context__.r("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - inactiveDays);
            // Get inactive users
            const inactiveUsers = await userGetService.getInactive();
            // Filter users inactive for specified period
            const oldInactiveUsers = inactiveUsers.filter((user)=>new Date(user.createdAt) < cutoffDate);
            if (oldInactiveUsers.length === 0) {
                return {
                    success: true,
                    message: `No inactive users older than ${inactiveDays} days found`,
                    deletedCount: 0
                };
            }
            // Check which users can be safely deleted
            const deletableUsers = [];
            for (const user of oldInactiveUsers){
                const canDelete = await this.canDelete(user.id);
                if (canDelete.canDelete) {
                    deletableUsers.push(user.id);
                }
            }
            if (deletableUsers.length === 0) {
                return {
                    success: false,
                    message: 'No inactive users can be safely deleted',
                    deletedCount: 0
                };
            }
            const results = await this.deleteBatch(deletableUsers);
            const deletedCount = results.filter((result)=>result).length;
            return {
                success: deletedCount === deletableUsers.length,
                message: `Deleted ${deletedCount} inactive users`,
                deletedCount
            };
        } catch (error) {
            console.error('Error deleting inactive users:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to delete inactive users',
                deletedCount: 0
            };
        }
    }
    /**
   * Delete guest users (cleanup operation)
   */ async deleteGuestUsers() {
        try {
            const { userGetService } = await __turbopack_context__.r("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const guestUsers = await userGetService.getByRole('guest');
            if (guestUsers.length === 0) {
                return {
                    success: true,
                    message: 'No guest users found',
                    deletedCount: 0
                };
            }
            const userIds = guestUsers.map((user)=>user.id);
            const results = await this.deleteBatch(userIds);
            const deletedCount = results.filter((result)=>result).length;
            return {
                success: deletedCount === guestUsers.length,
                message: `Deleted ${deletedCount} guest users`,
                deletedCount
            };
        } catch (error) {
            console.error('Error deleting guest users:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to delete guest users',
                deletedCount: 0
            };
        }
    }
    /**
   * Anonymize user data (GDPR compliance)
   */ async anonymize(id) {
        try {
            const { userUpdateService } = await __turbopack_context__.r("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            // Replace personal data with anonymized values
            const anonymizedData = {
                role: 'customer',
                name: 'Anonymized User',
                phoneNumber: undefined,
                country: undefined,
                city: undefined,
                zipCode: undefined,
                active: false
            };
            await userUpdateService.update(id, anonymizedData);
            return {
                success: true,
                message: 'User data anonymized successfully'
            };
        } catch (error) {
            console.error('Error anonymizing user:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to anonymize user'
            };
        }
    }
    /**
   * Bulk soft delete (deactivate multiple users)
   */ async bulkSoftDelete(ids) {
        this.logApiCall('PATCH', 'Bulk Soft Delete Users', {
            count: ids.length
        });
        const results = await Promise.allSettled(ids.map((id)=>this.softDelete(id)));
        const successful = results.filter((result)=>result.status === 'fulfilled' && result.value === true).length;
        const errors = results.filter((result)=>result.status === 'rejected').map((result, index)=>`User ${ids[index]}: ${result.status === 'rejected' ? result.reason : 'Unknown error'}`);
        return {
            success: successful === ids.length,
            deactivatedCount: successful,
            errors
        };
    }
}
const userDeleteService = new UserDeleteService();
}}),
"[project]/src/services/api/users/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// User Services
__turbopack_context__.s({
    "UserService": (()=>UserService),
    "userService": (()=>userService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
class UserService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userPostService"];
    update = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userUpdateService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userDeleteService"];
}
const userService = new UserService();
}}),
"[project]/src/services/api/users/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/users/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/cart/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "CartGetService": (()=>CartGetService),
    "cartGetService": (()=>cartGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CartGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get cart by user ID
   */ async getByUserId(userId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.ByUserId(userId);
        this.logApiCall('GET', endpoint);
        try {
            return this.handleResponse(this.client.get(endpoint));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Get cart by session ID
   */ async getBySessionId(sessionId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.BySessionId(sessionId);
        this.logApiCall('GET', endpoint);
        try {
            return this.handleResponse(this.client.get(endpoint));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Get cart summary by user ID
   */ async getSummaryByUserId(userId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.SummaryByUserId(userId);
        this.logApiCall('GET', endpoint);
        try {
            return this.handleResponse(this.client.get(endpoint));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Get cart summary by session ID
   */ async getSummaryBySessionId(sessionId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.SummaryBySessionId(sessionId);
        this.logApiCall('GET', endpoint);
        try {
            return this.handleResponse(this.client.get(endpoint));
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            throw error;
        }
    }
    /**
   * Get or create cart
   */ async getOrCreate(userId, sessionId) {
        const params = {};
        if (userId) params.userId = userId;
        if (sessionId) params.sessionId = sessionId;
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.GetOrCreate;
        this.logApiCall('POST', endpoint, params);
        return this.handleResponse(this.client.post(endpoint, null, {
            params
        }));
    }
}
const cartGetService = new CartGetService();
}}),
"[project]/src/services/api/cart/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CartPostService": (()=>CartPostService),
    "cartPostService": (()=>cartPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CartPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Add item to cart
   */ async addToCart(request) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.Add;
        this.logApiCall('POST', endpoint);
        return this.handleResponse(this.client.post(endpoint, request));
    }
}
const cartPostService = new CartPostService();
}}),
"[project]/src/services/api/cart/update.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CartUpdateService": (()=>CartUpdateService),
    "cartUpdateService": (()=>cartUpdateService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CartUpdateService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Update cart item quantity
   */ async updateCartItem(cartId, productId, request) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.UpdateItem(cartId, productId);
        this.logApiCall('PUT', endpoint);
        return this.handleResponse(this.client.put(endpoint, request));
    }
}
const cartUpdateService = new CartUpdateService();
}}),
"[project]/src/services/api/cart/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CartDeleteService": (()=>CartDeleteService),
    "cartDeleteService": (()=>cartDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class CartDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Remove item from cart
   */ async removeFromCart(cartId, productId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.RemoveItem(cartId, productId);
        this.logApiCall('DELETE', endpoint);
        return this.handleResponse(this.client.delete(endpoint));
    }
    /**
   * Remove cart item by ID
   */ async removeCartItem(cartItemId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.RemoveCartItem(cartItemId);
        this.logApiCall('DELETE', endpoint);
        return this.handleResponse(this.client.delete(endpoint));
    }
    /**
   * Clear entire cart
   */ async clearCart(cartId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.Clear(cartId);
        this.logApiCall('DELETE', endpoint);
        return this.handleResponse(this.client.delete(endpoint));
    }
    /**
   * Clear cart by user ID
   */ async clearCartByUserId(userId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.ClearByUserId(userId);
        this.logApiCall('DELETE', endpoint);
        return this.handleResponse(this.client.delete(endpoint));
    }
    /**
   * Clear cart by session ID
   */ async clearCartBySessionId(sessionId) {
        const endpoint = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Cart.ClearBySessionId(sessionId);
        this.logApiCall('DELETE', endpoint);
        return this.handleResponse(this.client.delete(endpoint));
    }
}
const cartDeleteService = new CartDeleteService();
}}),
"[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Cart Services
__turbopack_context__.s({
    "CartService": (()=>CartService),
    "cartService": (()=>cartService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
class CartService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cartGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cartPostService"];
    update = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cartUpdateService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cartDeleteService"];
}
const cartService = new CartService();
}}),
"[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$update$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/update.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/cart/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/payments/types.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-empty-object-type */ /* eslint-disable @typescript-eslint/no-explicit-any */ // Payment-related TypeScript types for frontend
__turbopack_context__.s({});
;
}}),
"[project]/src/services/api/payments/stripe.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "StripePaymentService": (()=>StripePaymentService),
    "default": (()=>__TURBOPACK__default__export__),
    "stripePaymentService": (()=>stripePaymentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class StripePaymentService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a Stripe payment intent with specific payment method
   */ async createPaymentIntent(data) {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/stripe/create-intent`, data);
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/stripe/create-intent`, data));
    }
    /**
   * Create a legacy Stripe payment intent (backward compatibility)
   */ async createLegacyIntent(amount, currency = 'usd') {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/create-intent`, {
            amount,
            currency
        });
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/create-intent`, {
            amount,
            currency
        }));
    }
    /**
   * Confirm a Stripe payment
   */ async confirmPayment(data) {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/stripe/confirm`, data);
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/stripe/confirm`, data));
    }
    /**
   * Create Affirm payment intent
   */ async createAffirmIntent(amount, currency = 'usd', metadata) {
        const data = {
            amount,
            currency,
            paymentMethodType: 'affirm',
            confirmationMethod: false,
            metadata
        };
        return this.createPaymentIntent(data);
    }
    /**
   * Create Apple Pay payment intent
   */ async createApplePayIntent(amount, currency = 'usd', metadata) {
        const data = {
            amount,
            currency,
            paymentMethodType: 'apple_pay',
            confirmationMethod: false,
            metadata
        };
        return this.createPaymentIntent(data);
    }
    /**
   * Create Apple Pay session
   */ async createApplePaySession(domainName) {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/apple-pay/session`, domainName);
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/apple-pay/session`, domainName));
    }
    /**
   * Validate Apple Pay merchant
   */ async validateApplePayMerchant(validationURL, domainName) {
        // This would typically be handled by your backend
        // For now, we'll create a session through our API
        return this.createApplePaySession(domainName);
    }
    /**
   * Process Apple Pay payment
   */ async processApplePayPayment(paymentData, amount, currency = 'usd') {
        // Create payment intent for Apple Pay
        const intentResponse = await this.createApplePayIntent(amount, currency, {
            apple_pay_payment: paymentData
        });
        if (!intentResponse.success || !intentResponse.paymentIntentId) {
            throw new Error(intentResponse.message || 'Failed to create Apple Pay payment intent');
        }
        // Confirm the payment
        return this.confirmPayment({
            paymentIntentId: intentResponse.paymentIntentId,
            paymentMethod: 'apple_pay',
            orderId: 0 // This should be set by the calling code
        });
    }
    /**
   * Process Affirm payment
   */ async processAffirmPayment(amount, currency = 'usd', orderId, metadata) {
        return this.createAffirmIntent(amount, currency, {
            ...metadata,
            order_id: orderId
        });
    }
    /**
   * Handle Stripe webhook events (if needed on frontend)
   */ async handleWebhookEvent(event) {
        // Log the webhook event for debugging
        console.log('Stripe webhook event received:', event);
        // Handle different event types
        switch(event.type){
            case 'payment_intent.succeeded':
                console.log('Payment succeeded:', event.data.object);
                break;
            case 'payment_intent.payment_failed':
                console.log('Payment failed:', event.data.object);
                break;
            default:
                console.log('Unhandled event type:', event.type);
        }
    }
    /**
   * Get payment method capabilities
   */ getPaymentMethodCapabilities() {
        return {
            card: true,
            affirm: true,
            applePay: this.isApplePayAvailable()
        };
    }
    /**
   * Check if Apple Pay is available
   */ isApplePayAvailable() {
        if ("TURBOPACK compile-time truthy", 1) return false;
        "TURBOPACK unreachable";
    }
    /**
   * Format amount for Stripe (convert to cents)
   */ formatAmountForStripe(amount) {
        return Math.round(amount * 100);
    }
    /**
   * Format amount from Stripe (convert from cents)
   */ formatAmountFromStripe(amount) {
        return amount / 100;
    }
}
const stripePaymentService = new StripePaymentService();
const __TURBOPACK__default__export__ = stripePaymentService;
}}),
"[project]/src/services/api/payments/paypal.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "PayPalPaymentService": (()=>PayPalPaymentService),
    "default": (()=>__TURBOPACK__default__export__),
    "paypalPaymentService": (()=>paypalPaymentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class PayPalPaymentService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Create a PayPal order
   */ async createOrder(data) {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/paypal/create-order`, data);
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/paypal/create-order`, data));
    }
    /**
   * Capture a PayPal order
   */ async captureOrder(orderId) {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/paypal/capture/${orderId}`, {});
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/paypal/capture/${orderId}`, {}));
    }
    /**
   * Get PayPal order details
   */ async getOrder(orderId) {
        this.logApiCall('GET', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/paypal/order/${orderId}`, {});
        return this.handleResponse(this.client.get(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/paypal/order/${orderId}`));
    }
    /**
   * Create PayPal order for checkout
   */ async createCheckoutOrder(amount, currency = 'USD', description, returnUrl, cancelUrl) {
        const orderData = {
            amount,
            currency,
            intent: 'CAPTURE',
            returnUrl,
            cancelUrl,
            purchaseUnit: {
                description: description || 'Cast Stone Purchase',
                amount: {
                    currencyCode: currency,
                    value: amount.toFixed(2)
                }
            }
        };
        return this.createOrder(orderData);
    }
    /**
   * Generate PayPal buttons configuration
   */ generateButtonsConfig(amount, currency = 'USD', onSuccess, onError, onCancel, description) {
        return {
            createOrder: async (data, actions)=>{
                try {
                    const orderResponse = await this.createCheckoutOrder(amount, currency, description);
                    if (!orderResponse.success || !orderResponse.orderId) {
                        throw new Error(orderResponse.message || 'Failed to create PayPal order');
                    }
                    return orderResponse.orderId;
                } catch (error) {
                    console.error('Error creating PayPal order:', error);
                    throw error;
                }
            },
            onApprove: async (data, actions)=>{
                try {
                    const captureResponse = await this.captureOrder(data.orderID);
                    if (captureResponse.success) {
                        console.log('PayPal payment captured successfully:', captureResponse);
                        onSuccess?.(captureResponse);
                    } else {
                        throw new Error(captureResponse.message || 'Failed to capture PayPal payment');
                    }
                } catch (error) {
                    console.error('Error capturing PayPal payment:', error);
                    onError?.(error);
                }
            },
            onError: (err)=>{
                console.error('PayPal error:', err);
                onError?.(err);
            },
            onCancel: (data)=>{
                console.log('PayPal payment cancelled:', data);
                onCancel?.(data);
            },
            style: {
                layout: 'vertical',
                color: 'gold',
                shape: 'rect',
                label: 'paypal',
                tagline: false,
                height: 40
            }
        };
    }
    /**
   * Load PayPal SDK script
   */ async loadPayPalSDK(clientId, currency = 'USD') {
        return new Promise((resolve, reject)=>{
            // Check if PayPal SDK is already loaded
            if (window.paypal) {
                resolve();
                return;
            }
            // Create script element
            const script = document.createElement('script');
            script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=${currency}&intent=capture`;
            script.async = true;
            script.onload = ()=>{
                if (window.paypal) {
                    resolve();
                } else {
                    reject(new Error('PayPal SDK failed to load'));
                }
            };
            script.onerror = ()=>{
                reject(new Error('Failed to load PayPal SDK script'));
            };
            document.head.appendChild(script);
        });
    }
    /**
   * Render PayPal buttons
   */ async renderPayPalButtons(containerId, config) {
        if (!window.paypal) {
            throw new Error('PayPal SDK not loaded');
        }
        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`Container with ID '${containerId}' not found`);
        }
        // Clear existing content
        container.innerHTML = '';
        // Render PayPal buttons
        window.paypal.Buttons(config).render(`#${containerId}`);
    }
    /**
   * Validate PayPal order amount
   */ validateOrderAmount(amount, currency = 'USD') {
        if (amount <= 0) {
            return false;
        }
        // PayPal minimum amounts by currency
        const minimumAmounts = {
            'USD': 0.01,
            'EUR': 0.01,
            'GBP': 0.01,
            'CAD': 0.01,
            'AUD': 0.01,
            'JPY': 1
        };
        const minimum = minimumAmounts[currency] || 0.01;
        return amount >= minimum;
    }
    /**
   * Format amount for PayPal
   */ formatAmountForPayPal(amount) {
        return amount.toFixed(2);
    }
    /**
   * Get PayPal supported currencies
   */ getSupportedCurrencies() {
        return [
            'USD',
            'EUR',
            'GBP',
            'CAD',
            'AUD',
            'JPY',
            'CHF',
            'NOK',
            'SEK',
            'DKK',
            'PLN',
            'CZK',
            'HUF',
            'ILS',
            'MXN',
            'BRL',
            'MYR',
            'PHP',
            'THB',
            'TWD',
            'NZD',
            'HKD',
            'SGD',
            'RUB'
        ];
    }
    /**
   * Check if currency is supported
   */ isCurrencySupported(currency) {
        return this.getSupportedCurrencies().includes(currency.toUpperCase());
    }
}
const paypalPaymentService = new PayPalPaymentService();
const __TURBOPACK__default__export__ = paypalPaymentService;
}}),
"[project]/src/services/api/payments/unified.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ __turbopack_context__.s({
    "UnifiedPaymentService": (()=>UnifiedPaymentService),
    "default": (()=>__TURBOPACK__default__export__),
    "unifiedPaymentService": (()=>unifiedPaymentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$stripe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/stripe.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$paypal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/paypal.ts [app-ssr] (ecmascript)");
;
;
;
;
class UnifiedPaymentService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    stripe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$stripe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stripePaymentService"];
    paypal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$paypal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["paypalPaymentService"];
    /**
   * Confirm payment and send email notification
   */ async confirmPaymentAndNotify(data) {
        this.logApiCall('POST', `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/confirm-and-notify`, data);
        return this.handleResponse(this.client.post(`${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Payments}/confirm-and-notify`, data));
    }
    /**
   * Process payment based on method type
   */ async processPayment(paymentMethod, amount, currency = 'USD', orderId, additionalData) {
        switch(paymentMethod){
            case 'stripe':
                return this.stripe.createPaymentIntent({
                    amount: this.stripe.formatAmountForStripe(amount),
                    currency: currency.toLowerCase(),
                    paymentMethodType: 'card',
                    confirmationMethod: false,
                    metadata: {
                        order_id: orderId,
                        ...additionalData
                    }
                });
            case 'affirm':
                return this.stripe.createAffirmIntent(this.stripe.formatAmountForStripe(amount), currency.toLowerCase(), {
                    order_id: orderId,
                    ...additionalData
                });
            case 'apple_pay':
                return this.stripe.createApplePayIntent(this.stripe.formatAmountForStripe(amount), currency.toLowerCase(), {
                    order_id: orderId,
                    ...additionalData
                });
            case 'paypal':
                return this.paypal.createCheckoutOrder(amount, currency.toUpperCase(), additionalData?.description || 'Cast Stone Purchase', additionalData?.returnUrl, additionalData?.cancelUrl);
            default:
                throw new Error(`Unsupported payment method: ${paymentMethod}`);
        }
    }
    /**
   * Complete payment process with email notification
   */ async completePayment(paymentMethod, paymentIntentId, orderId) {
        const confirmationData = {
            paymentIntentId,
            paymentMethod,
            orderId
        };
        return this.confirmPaymentAndNotify(confirmationData);
    }
    /**
   * Get available payment methods
   */ getAvailablePaymentMethods() {
        const stripeCapabilities = this.stripe.getPaymentMethodCapabilities();
        return [
            {
                method: 'stripe',
                name: 'Credit Card',
                description: 'Visa, Mastercard, American Express',
                available: stripeCapabilities.card,
                icon: '💳'
            },
            {
                method: 'paypal',
                name: 'PayPal',
                description: 'Pay securely with your PayPal account',
                available: true,
                icon: '🅿️'
            },
            {
                method: 'affirm',
                name: 'Affirm',
                description: 'Buy now, pay later with Affirm',
                available: stripeCapabilities.affirm,
                icon: '📅'
            },
            {
                method: 'apple_pay',
                name: 'Apple Pay',
                description: 'Pay with Touch ID or Face ID',
                available: stripeCapabilities.applePay,
                icon: '🍎'
            }
        ];
    }
    /**
   * Validate payment amount for all methods
   */ validatePaymentAmount(amount, currency = 'USD') {
        const errors = [];
        if (amount <= 0) {
            errors.push('Amount must be greater than zero');
        }
        // Check PayPal minimum
        if (!this.paypal.validateOrderAmount(amount, currency)) {
            errors.push(`Amount is below minimum for ${currency}`);
        }
        // Check Stripe minimum (usually $0.50 USD)
        const stripeMinimum = currency.toLowerCase() === 'usd' ? 0.50 : 0.50;
        if (amount < stripeMinimum) {
            errors.push(`Amount is below Stripe minimum of ${stripeMinimum} ${currency}`);
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    /**
   * Format currency for display
   */ formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency.toUpperCase()
        }).format(amount);
    }
    /**
   * Get payment method configuration
   */ getPaymentMethodConfig(method) {
        switch(method){
            case 'stripe':
            case 'affirm':
            case 'apple_pay':
                return {
                    publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
                    appearance: {
                        theme: 'stripe',
                        variables: {
                            colorPrimary: '#1e3a8a',
                            colorBackground: '#ffffff',
                            colorText: '#1f2937',
                            colorDanger: '#dc2626',
                            fontFamily: 'system-ui, sans-serif',
                            spacingUnit: '4px',
                            borderRadius: '6px'
                        }
                    }
                };
            case 'paypal':
                return {
                    clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID,
                    currency: 'USD',
                    intent: 'capture',
                    style: {
                        layout: 'vertical',
                        color: 'gold',
                        shape: 'rect',
                        label: 'paypal',
                        tagline: false,
                        height: 40
                    }
                };
            default:
                return {};
        }
    }
    /**
   * Handle payment errors
   */ handlePaymentError(error, paymentMethod) {
        console.error(`Payment error (${paymentMethod}):`, error);
        // Common error patterns
        if (error.message?.includes('insufficient_funds')) {
            return {
                userMessage: 'Your payment method has insufficient funds. Please try a different payment method.',
                technicalMessage: error.message,
                shouldRetry: true
            };
        }
        if (error.message?.includes('card_declined')) {
            return {
                userMessage: 'Your card was declined. Please check your card details or try a different payment method.',
                technicalMessage: error.message,
                shouldRetry: true
            };
        }
        if (error.message?.includes('network')) {
            return {
                userMessage: 'Network error. Please check your connection and try again.',
                technicalMessage: error.message,
                shouldRetry: true
            };
        }
        // Default error handling
        return {
            userMessage: 'An error occurred while processing your payment. Please try again.',
            technicalMessage: error.message || 'Unknown payment error',
            shouldRetry: true
        };
    }
    /**
   * Get Stripe service instance
   */ get stripeService() {
        return this.stripe;
    }
    /**
   * Get PayPal service instance
   */ get paypalService() {
        return this.paypal;
    }
}
const unifiedPaymentService = new UnifiedPaymentService();
const __TURBOPACK__default__export__ = unifiedPaymentService;
}}),
"[project]/src/services/api/payments/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ // Payment Services Export
__turbopack_context__.s({
    "PaymentService": (()=>PaymentService),
    "default": (()=>__TURBOPACK__default__export__),
    "paymentService": (()=>paymentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$stripe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/stripe.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$paypal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/paypal.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$unified$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/unified.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
class PaymentService {
    stripe = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$stripe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["stripePaymentService"];
    paypal = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$paypal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["paypalPaymentService"];
    unified = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$unified$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["unifiedPaymentService"];
    // Convenience methods that delegate to unified service
    async processPayment(paymentMethod, amount, currency = 'USD', orderId, additionalData) {
        return this.unified.processPayment(paymentMethod, amount, currency, orderId, additionalData);
    }
    async completePayment(paymentMethod, paymentIntentId, orderId) {
        return this.unified.completePayment(paymentMethod, paymentIntentId, orderId);
    }
    getAvailablePaymentMethods() {
        return this.unified.getAvailablePaymentMethods();
    }
    validatePaymentAmount(amount, currency = 'USD') {
        return this.unified.validatePaymentAmount(amount, currency);
    }
    formatCurrency(amount, currency = 'USD') {
        return this.unified.formatCurrency(amount, currency);
    }
    getPaymentMethodConfig(method) {
        return this.unified.getPaymentMethodConfig(method);
    }
    handlePaymentError(error, paymentMethod) {
        return this.unified.handlePaymentError(error, paymentMethod);
    }
}
const paymentService = new PaymentService();
const __TURBOPACK__default__export__ = paymentService;
}}),
"[project]/src/services/api/payments/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$stripe$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/stripe.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$paypal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/paypal.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$unified$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/payments/unified.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/payments/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/seed/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SeedService": (()=>SeedService),
    "seedService": (()=>seedService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
;
;
class SeedService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Seed all data (statuses, admin user, sample collections and products)
   */ async seedAll() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.All);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.All);
            return {
                success: response.success,
                message: response.message || 'All data seeded successfully'
            };
        } catch (error) {
            console.error('Error seeding all data:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to seed data'
            };
        }
    }
    /**
   * Seed status data
   */ async seedStatuses() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.Statuses);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.Statuses);
            return {
                success: response.success,
                message: response.message || 'Status data seeded successfully'
            };
        } catch (error) {
            console.error('Error seeding statuses:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to seed statuses'
            };
        }
    }
    /**
   * Seed admin user
   */ async seedAdminUser() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.AdminUser);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.AdminUser);
            return {
                success: response.success,
                message: response.message || 'Admin user seeded successfully'
            };
        } catch (error) {
            console.error('Error seeding admin user:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to seed admin user'
            };
        }
    }
    /**
   * Seed sample collections
   */ async seedCollections() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.Collections);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.Collections);
            return {
                success: response.success,
                message: response.message || 'Sample collections seeded successfully'
            };
        } catch (error) {
            console.error('Error seeding collections:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to seed collections'
            };
        }
    }
    /**
   * Seed sample products
   */ async seedProducts() {
        this.logApiCall('POST', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.Products);
        try {
            const response = await this.client.post(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiEndpoints"].Seed.Products);
            return {
                success: response.success,
                message: response.message || 'Sample products seeded successfully'
            };
        } catch (error) {
            console.error('Error seeding products:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to seed products'
            };
        }
    }
    /**
   * Initialize database with all required data
   */ async initializeDatabase() {
        const steps = [];
        try {
            // Step 1: Seed statuses
            const statusResult = await this.seedStatuses();
            steps.push({
                step: 'Seed Statuses',
                success: statusResult.success,
                message: statusResult.message
            });
            // Step 2: Seed admin user
            const adminResult = await this.seedAdminUser();
            steps.push({
                step: 'Seed Admin User',
                success: adminResult.success,
                message: adminResult.message
            });
            // Step 3: Seed collections
            const collectionsResult = await this.seedCollections();
            steps.push({
                step: 'Seed Collections',
                success: collectionsResult.success,
                message: collectionsResult.message
            });
            // Step 4: Seed products
            const productsResult = await this.seedProducts();
            steps.push({
                step: 'Seed Products',
                success: productsResult.success,
                message: productsResult.message
            });
            const allSuccessful = steps.every((step)=>step.success);
            return {
                success: allSuccessful,
                message: allSuccessful ? 'Database initialized successfully' : 'Database initialization completed with some errors',
                steps
            };
        } catch (error) {
            console.error('Error initializing database:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to initialize database',
                steps
            };
        }
    }
    /**
   * Check if database needs seeding
   */ async checkSeedingStatus() {
        try {
            // This would require additional endpoints to check data existence
            // For now, we'll return a placeholder response
            return {
                needsSeeding: false,
                missingData: []
            };
        } catch (error) {
            console.error('Error checking seeding status:', error);
            return {
                needsSeeding: true,
                missingData: [
                    'Unable to check status'
                ]
            };
        }
    }
    /**
   * Reset and reseed database (development only)
   */ async resetAndReseed() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            // This would require additional endpoints to clear data
            // For now, we'll just seed all data
            return await this.seedAll();
        } catch (error) {
            console.error('Error resetting and reseeding:', error);
            return {
                success: false,
                message: error instanceof Error ? error.message : 'Failed to reset and reseed'
            };
        }
    }
}
const seedService = new SeedService();
}}),
"[project]/src/services/api/contactForm/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ __turbopack_context__.s({
    "ContactFormGetService": (()=>ContactFormGetService),
    "contactFormGetService": (()=>contactFormGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
class ContactFormGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all contact form submissions (Admin only)
   */ async getAll() {
        this.logApiCall('GET', '/contactform');
        return this.handleResponse(this.client.get('/contactform'));
    }
    /**
   * Get contact form submission by ID
   */ async getById(id) {
        this.logApiCall('GET', `/contactform/${id}`);
        if (!id || id <= 0) {
            throw new Error('Valid submission ID is required');
        }
        return this.handleResponse(this.client.get(`/contactform/${id}`));
    }
    /**
   * Get recent contact form submissions
   */ async getRecent(count = 10) {
        this.logApiCall('GET', `/contactform/recent?count=${count}`);
        if (count <= 0 || count > 100) {
            throw new Error('Count must be between 1 and 100');
        }
        return this.handleResponse(this.client.get(`/contactform/recent?count=${count}`));
    }
    /**
   * Get contact form submissions by inquiry type
   */ async getByInquiryType(inquiryType) {
        this.logApiCall('GET', `/contactform/inquiry/${inquiryType}`);
        return this.handleResponse(this.client.get(`/contactform/inquiry/${inquiryType}`));
    }
    /**
   * Get contact form submissions by date range
   */ async getByDateRange(startDate, endDate) {
        const start = startDate.toISOString();
        const end = endDate.toISOString();
        this.logApiCall('GET', `/contactform/date-range?startDate=${start}&endDate=${end}`);
        if (startDate > endDate) {
            throw new Error('Start date must be before end date');
        }
        return this.handleResponse(this.client.get(`/contactform/date-range?startDate=${start}&endDate=${end}`));
    }
    /**
   * Get contact form submissions with pagination
   */ async getPaginated(page = 1, pageSize = 20) {
        this.logApiCall('GET', `/contactform?page=${page}&pageSize=${pageSize}`);
        if (page <= 0) {
            throw new Error('Page must be greater than 0');
        }
        if (pageSize <= 0 || pageSize > 100) {
            throw new Error('Page size must be between 1 and 100');
        }
        // For now, we'll simulate pagination by getting all and slicing
        // In a real implementation, the backend would handle pagination
        const allSubmissions = await this.getAll();
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const submissions = allSubmissions.slice(startIndex, endIndex);
        return {
            submissions,
            totalCount: allSubmissions.length,
            currentPage: page,
            totalPages: Math.ceil(allSubmissions.length / pageSize)
        };
    }
    /**
   * Search contact form submissions
   */ async search(query) {
        this.logApiCall('GET', `/contactform/search?q=${encodeURIComponent(query)}`);
        if (!query || query.trim().length === 0) {
            throw new Error('Search query is required');
        }
        // For now, we'll simulate search by filtering all submissions
        // In a real implementation, the backend would handle search
        const allSubmissions = await this.getAll();
        const lowerQuery = query.toLowerCase();
        return allSubmissions.filter((submission)=>submission.name.toLowerCase().includes(lowerQuery) || submission.email.toLowerCase().includes(lowerQuery) || submission.company?.toLowerCase().includes(lowerQuery) || submission.message.toLowerCase().includes(lowerQuery) || submission.inquiryDisplayName.toLowerCase().includes(lowerQuery));
    }
}
const contactFormGetService = new ContactFormGetService();
}}),
"[project]/src/services/api/contactForm/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-unused-vars */ __turbopack_context__.s({
    "ContactFormPostService": (()=>ContactFormPostService),
    "contactFormPostService": (()=>contactFormPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
class ContactFormPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Submit a new contact form
   */ async create(data) {
        this.logApiCall('POST', '/contactform', data);
        // Validate required fields
        this.validateCreateRequest(data);
        return this.handleResponse(this.client.post('/contactform', data));
    }
    /**
   * Submit contact form with additional validation
   */ async submit(formData) {
        const data = {
            name: formData.name.trim(),
            email: formData.email.trim().toLowerCase(),
            phoneNumber: formData.phoneNumber.trim(),
            company: formData.company?.trim() || undefined,
            state: formData.state.trim(),
            inquiry: formData.inquiry,
            message: formData.message.trim()
        };
        return this.create(data);
    }
    /**
   * Validate create contact form request
   */ validateCreateRequest(data) {
        if (!data.name || data.name.trim().length === 0) {
            throw new Error('Name is required');
        }
        if (data.name.length > 100) {
            throw new Error('Name must be 100 characters or less');
        }
        if (!data.email || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceUtils"].isValidEmail(data.email)) {
            throw new Error('Valid email is required');
        }
        if (!data.phoneNumber || data.phoneNumber.trim().length === 0) {
            throw new Error('Phone number is required');
        }
        if (!this.isValidPhoneNumber(data.phoneNumber)) {
            throw new Error('Please enter a valid phone number');
        }
        if (data.company && data.company.length > 200) {
            throw new Error('Company name must be 200 characters or less');
        }
        if (!data.state || data.state.trim().length === 0) {
            throw new Error('State is required');
        }
        if (data.state.length > 100) {
            throw new Error('State must be 100 characters or less');
        }
        if (!data.inquiry || ![
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9
        ].includes(data.inquiry)) {
            throw new Error('Please select a valid inquiry type');
        }
        if (!data.message || data.message.trim().length === 0) {
            throw new Error('Message is required');
        }
        if (data.message.length < 10) {
            throw new Error('Message must be at least 10 characters long');
        }
        if (data.message.length > 2000) {
            throw new Error('Message must be 2000 characters or less');
        }
    }
    /**
   * Validate phone number format
   */ isValidPhoneNumber(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
}
const contactFormPostService = new ContactFormPostService();
}}),
"[project]/src/services/api/contactForm/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Contact Form API Services
__turbopack_context__.s({
    "contactFormService": (()=>contactFormService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/post.ts [app-ssr] (ecmascript)");
;
;
const contactFormService = {
    get: ()=>__turbopack_context__.r("[project]/src/services/api/contactForm/get.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.contactFormGetService),
    post: ()=>__turbopack_context__.r("[project]/src/services/api/contactForm/post.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.contactFormPostService)
};
}}),
"[project]/src/services/api/contactForm/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/wholesaleBuyers/get.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WholesaleBuyerGetService": (()=>WholesaleBuyerGetService),
    "wholesaleBuyerGetService": (()=>wholesaleBuyerGetService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
class WholesaleBuyerGetService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Get all wholesale buyer applications
   */ async getAll() {
        return this.client.get('/wholesalebuyers');
    }
    /**
   * Get wholesale buyer application by ID
   */ async getById(id) {
        return this.client.get(`/wholesalebuyers/${id}`);
    }
    /**
   * Get wholesale buyer application by email
   */ async getByEmail(email) {
        return this.client.get(`/wholesalebuyers/email/${encodeURIComponent(email)}`);
    }
    /**
   * Get wholesale buyer applications by status
   */ async getByStatus(status) {
        return this.client.get(`/wholesalebuyers/status/${status}`);
    }
    /**
   * Get pending wholesale buyer applications
   */ async getPending() {
        return this.client.get('/wholesalebuyers/pending');
    }
    /**
   * Get approved wholesale buyers
   */ async getApproved() {
        return this.client.get('/wholesalebuyers/approved');
    }
    /**
   * Get recent wholesale buyer applications
   */ async getRecent(count = 10) {
        return this.client.get(`/wholesalebuyers/recent?count=${count}`);
    }
    /**
   * Check if user is an approved wholesale buyer
   */ async checkApproval(email) {
        return this.client.get(`/wholesalebuyers/check-approval/${encodeURIComponent(email)}`);
    }
}
const wholesaleBuyerGetService = new WholesaleBuyerGetService();
}}),
"[project]/src/services/api/wholesaleBuyers/post.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WholesaleBuyerPostService": (()=>WholesaleBuyerPostService),
    "wholesaleBuyerPostService": (()=>wholesaleBuyerPostService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
class WholesaleBuyerPostService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Submit a new wholesale buyer application
   */ async submitApplication(request) {
        return this.client.post('/wholesalebuyers/apply', request);
    }
    /**
   * Approve a wholesale buyer application
   */ async approveApplication(id, request) {
        return this.client.put(`/wholesalebuyers/${id}/approve`, request);
    }
    /**
   * Reject a wholesale buyer application
   */ async rejectApplication(id, request) {
        return this.client.put(`/wholesalebuyers/${id}/reject`, request);
    }
}
const wholesaleBuyerPostService = new WholesaleBuyerPostService();
}}),
"[project]/src/services/api/wholesaleBuyers/delete.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WholesaleBuyerDeleteService": (()=>WholesaleBuyerDeleteService),
    "wholesaleBuyerDeleteService": (()=>wholesaleBuyerDeleteService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
class WholesaleBuyerDeleteService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Delete a wholesale buyer application
   */ async deleteApplication(id) {
        return this.client.delete(`/wholesalebuyers/${id}`);
    }
}
const wholesaleBuyerDeleteService = new WholesaleBuyerDeleteService();
}}),
"[project]/src/services/api/wholesaleBuyers/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Wholesale Buyer Services
__turbopack_context__.s({
    "WholesaleBuyerService": (()=>WholesaleBuyerService),
    "wholesaleBuyerService": (()=>wholesaleBuyerService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/delete.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
class WholesaleBuyerService {
    get = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wholesaleBuyerGetService"];
    post = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wholesaleBuyerPostService"];
    delete = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["wholesaleBuyerDeleteService"];
}
const wholesaleBuyerService = new WholesaleBuyerService();
}}),
"[project]/src/services/api/wholesaleBuyers/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$post$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/post.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$delete$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/delete.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/services/api/auth/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthService": (()=>AuthService),
    "authService": (()=>authService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
;
class AuthService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseService"] {
    /**
   * Validate user credentials for login
   */ async login(request) {
        return this.client.post('/auth/login', request);
    }
    /**
   * Check if user is an approved wholesale buyer
   */ async checkWholesaleStatus(email) {
        return this.client.get(`/auth/check-wholesale-status/${encodeURIComponent(email)}`);
    }
    /**
   * Get user information by email
   */ async getUserByEmail(email) {
        return this.client.get(`/auth/user/${encodeURIComponent(email)}`);
    }
}
const authService = new AuthService();
}}),
"[project]/src/services/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Configuration and Base Services
__turbopack_context__.s({
    "ApiService": (()=>ApiService),
    "apiService": (()=>apiService),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$httpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/httpClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
// Type Definitions
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$types$2f$entities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/types/entities.ts [app-ssr] (ecmascript)");
// Individual Service Exports
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/products/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productSpecifications/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productDetails/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/downloadableContent/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/orders/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/users/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/payments/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$seed$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/seed/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/auth/index.ts [app-ssr] (ecmascript)");
// Combined API Service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/products/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/orders/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/users/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/payments/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
class ApiService {
    collections = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["collectionService"];
    products = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["productService"];
    productSpecifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["productSpecificationsService"];
    productDetails = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["productDetailsService"];
    downloadableContent = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["downloadableContentService"];
    orders = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["orderService"];
    users = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["userService"];
    cart = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"];
    payments = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["paymentService"];
    seed = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$seed$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["seedService"];
    contactForm = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["contactFormService"];
    wholesaleBuyers = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wholesaleBuyerService"];
    auth = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"];
}
const apiService = new ApiService();
const __TURBOPACK__default__export__ = apiService;
;
;
;
}}),
"[project]/src/services/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$apiConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/apiConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$httpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/httpClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$config$2f$baseService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/config/baseService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$types$2f$entities$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/types/entities.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$products$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/products/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productSpecifications$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productSpecifications/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$productDetails$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/productDetails/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$downloadableContent$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/downloadableContent/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$orders$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/orders/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/users/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$payments$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/payments/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$seed$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/seed/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$contactForm$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/contactForm/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/auth/index.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/contexts/CartContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ /* eslint-disable react-hooks/exhaustive-deps */ __turbopack_context__.s({
    "CartProvider": (()=>CartProvider),
    "useCart": (()=>useCart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/cart/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
// Initial State
const initialState = {
    cart: null,
    isLoading: false,
    error: null,
    sessionId: ''
};
// Cart Reducer
function cartReducer(state, action) {
    switch(action.type){
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload,
                isLoading: false
            };
        case 'SET_CART':
            return {
                ...state,
                cart: action.payload,
                isLoading: false,
                error: null
            };
        case 'SET_SESSION_ID':
            return {
                ...state,
                sessionId: action.payload
            };
        case 'ADD_ITEM':
            if (!state.cart) return state;
            const existingItemIndex = state.cart.cartItems.findIndex((item)=>item.productId === action.payload.productId);
            if (existingItemIndex >= 0) {
                const updatedItems = [
                    ...state.cart.cartItems
                ];
                updatedItems[existingItemIndex] = {
                    ...updatedItems[existingItemIndex],
                    quantity: updatedItems[existingItemIndex].quantity + action.payload.quantity
                };
                return {
                    ...state,
                    cart: {
                        ...state.cart,
                        cartItems: updatedItems
                    }
                };
            } else {
                return {
                    ...state,
                    cart: {
                        ...state.cart,
                        cartItems: [
                            ...state.cart.cartItems,
                            action.payload
                        ]
                    }
                };
            }
        case 'UPDATE_ITEM':
            if (!state.cart) return state;
            const updatedItems = state.cart.cartItems.map((item)=>item.productId === action.payload.productId ? {
                    ...item,
                    quantity: action.payload.quantity
                } : item);
            return {
                ...state,
                cart: {
                    ...state.cart,
                    cartItems: updatedItems
                }
            };
        case 'REMOVE_ITEM':
            if (!state.cart) return state;
            const filteredItems = state.cart.cartItems.filter((item)=>item.productId !== action.payload);
            return {
                ...state,
                cart: {
                    ...state.cart,
                    cartItems: filteredItems
                }
            };
        case 'CLEAR_CART':
            return {
                ...state,
                cart: state.cart ? {
                    ...state.cart,
                    cartItems: [],
                    totalItems: 0,
                    totalAmount: 0
                } : null
            };
        default:
            return state;
    }
}
// Create Context
const CartContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Generate session ID
function generateSessionId() {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}
function CartProvider({ children }) {
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(cartReducer, initialState);
    // Initialize session ID on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeSession = ()=>{
            let sessionId = localStorage.getItem('cart_session_id');
            if (!sessionId) {
                sessionId = generateSessionId();
                localStorage.setItem('cart_session_id', sessionId);
            }
            dispatch({
                type: 'SET_SESSION_ID',
                payload: sessionId
            });
        };
        initializeSession();
    }, []);
    // Load cart on session ID change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (state.sessionId && state.sessionId.length > 0) {
            loadCart();
        }
    }, [
        state.sessionId
    ]);
    const loadCart = async (userId)=>{
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            let cart = null;
            if (userId) {
                cart = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"].get.getByUserId(userId);
            } else if (state.sessionId && state.sessionId.length > 0) {
                // Use getOrCreate to avoid 404 errors for new sessions
                cart = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"].get.getOrCreate(undefined, state.sessionId);
            }
            dispatch({
                type: 'SET_CART',
                payload: cart
            });
        } catch (error) {
            console.error('Error loading cart:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: 'Failed to load cart'
            });
        } finally{
            dispatch({
                type: 'SET_LOADING',
                payload: false
            });
        }
    };
    const addToCart = async (productId, quantity, userId)=>{
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            // Ensure we have either userId or sessionId
            if (!userId && (!state.sessionId || state.sessionId.length === 0)) {
                throw new Error('No user ID or session ID available for cart operation');
            }
            const request = {
                productId,
                quantity,
                userId,
                sessionId: userId ? undefined : state.sessionId
            };
            const updatedCart = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"].post.addToCart(request);
            dispatch({
                type: 'SET_CART',
                payload: updatedCart
            });
        } catch (error) {
            console.error('Error adding to cart:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: error.message || 'Failed to add item to cart'
            });
        } finally{
            dispatch({
                type: 'SET_LOADING',
                payload: false
            });
        }
    };
    const updateCartItem = async (productId, quantity)=>{
        if (!state.cart) return;
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            const request = {
                quantity
            };
            const updatedCart = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"].update.updateCartItem(state.cart.id, productId, request);
            dispatch({
                type: 'SET_CART',
                payload: updatedCart
            });
        } catch (error) {
            console.error('Error updating cart item:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: error.message || 'Failed to update cart item'
            });
        }
    };
    const removeFromCart = async (productId)=>{
        if (!state.cart) return;
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"].delete.removeFromCart(state.cart.id, productId);
            dispatch({
                type: 'REMOVE_ITEM',
                payload: productId
            });
            dispatch({
                type: 'SET_LOADING',
                payload: false
            });
        } catch (error) {
            console.error('Error removing from cart:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: error.message || 'Failed to remove item from cart'
            });
        }
    };
    const clearCart = async ()=>{
        if (!state.cart) return;
        try {
            dispatch({
                type: 'SET_LOADING',
                payload: true
            });
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$cart$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cartService"].delete.clearCart(state.cart.id);
            dispatch({
                type: 'CLEAR_CART'
            });
            dispatch({
                type: 'SET_LOADING',
                payload: false
            });
        } catch (error) {
            console.error('Error clearing cart:', error);
            dispatch({
                type: 'SET_ERROR',
                payload: error.message || 'Failed to clear cart'
            });
        }
    };
    const getCartSummary = ()=>{
        if (!state.cart) {
            return {
                totalItems: 0,
                totalAmount: 0
            };
        }
        return {
            totalItems: state.cart.totalItems,
            totalAmount: state.cart.totalAmount
        };
    };
    const refreshCart = async (userId)=>{
        await loadCart(userId);
    };
    const contextValue = {
        state,
        addToCart,
        updateCartItem,
        removeFromCart,
        clearCart,
        getCartSummary,
        refreshCart
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CartContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/CartContext.tsx",
        lineNumber: 268,
        columnNumber: 5
    }, this);
}
function useCart() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
}
}}),
"[project]/src/components/shared/Header/header.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "Headerroot": "header-module__zhIT0W__Headerroot",
  "active": "header-module__zhIT0W__active",
  "cartBadge": "header-module__zhIT0W__cartBadge",
  "cartBadgeAppear": "header-module__zhIT0W__cartBadgeAppear",
  "cartContainer": "header-module__zhIT0W__cartContainer",
  "cartIconWrapper": "header-module__zhIT0W__cartIconWrapper",
  "cartLink": "header-module__zhIT0W__cartLink",
  "container": "header-module__zhIT0W__container",
  "dropdown": "header-module__zhIT0W__dropdown",
  "dropdownContainer": "header-module__zhIT0W__dropdownContainer",
  "dropdownIcon": "header-module__zhIT0W__dropdownIcon",
  "dropdownItem": "header-module__zhIT0W__dropdownItem",
  "dropdownLink": "header-module__zhIT0W__dropdownLink",
  "dropdownList": "header-module__zhIT0W__dropdownList",
  "dropdownSlideIn": "header-module__zhIT0W__dropdownSlideIn",
  "header": "header-module__zhIT0W__header",
  "loadingIcon": "header-module__zhIT0W__loadingIcon",
  "logo": "header-module__zhIT0W__logo",
  "logoLink": "header-module__zhIT0W__logoLink",
  "logoSubtext": "header-module__zhIT0W__logoSubtext",
  "logoText": "header-module__zhIT0W__logoText",
  "nav": "header-module__zhIT0W__nav",
  "navButton": "header-module__zhIT0W__navButton",
  "navItem": "header-module__zhIT0W__navItem",
  "navLink": "header-module__zhIT0W__navLink",
  "navList": "header-module__zhIT0W__navList",
  "nonHomePage": "header-module__zhIT0W__nonHomePage",
  "rotated": "header-module__zhIT0W__rotated",
  "scrolled": "header-module__zhIT0W__scrolled",
  "subDropdownItem": "header-module__zhIT0W__subDropdownItem",
  "subDropdownLink": "header-module__zhIT0W__subDropdownLink",
  "subDropdownList": "header-module__zhIT0W__subDropdownList",
  "subSubDropdownItem": "header-module__zhIT0W__subSubDropdownItem",
  "subSubDropdownLink": "header-module__zhIT0W__subSubDropdownLink",
  "subSubDropdownList": "header-module__zhIT0W__subSubDropdownList",
});
}}),
"[project]/src/components/shared/Header/Header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable react-hooks/rules-of-hooks */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CartContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/CartContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/api/collections/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/collections/get.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/shared/Header/header.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
const Header = ({ title = "Cast Stone" })=>{
    const { getCartSummary } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CartContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCart"])();
    const [collections, setCollections] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [activeDropdown, setActiveDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isScrolled, setIsScrolled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const dropdownRefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({});
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    if (pathname.startsWith('/admin')) {
        return null;
    }
    // Hide header on admin dashboard routes
    // Company dropdown items
    const companyItems = [
        {
            label: 'Contact Us',
            href: '/contact'
        },
        {
            label: 'Our Story',
            href: '/our-story'
        },
        {
            label: 'Retail Locator',
            href: '/retail-locator'
        },
        {
            label: 'Wholesale Signup',
            href: '/wholesale-signup'
        }
    ];
    // Discover dropdown items
    const discoverItems = [
        {
            label: 'Catalog',
            href: '/catalog'
        },
        {
            label: 'Finishes',
            href: '/finishes'
        },
        {
            label: 'Videos',
            href: '/videos'
        },
        {
            label: 'Technical Info',
            href: '/technical-info'
        },
        {
            label: 'FAQ',
            href: '/faq'
        }
    ];
    // Handle scroll effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleScroll = ()=>{
            setIsScrolled(window.scrollY > 50);
        };
        window.addEventListener('scroll', handleScroll);
        return ()=>window.removeEventListener('scroll', handleScroll);
    }, []);
    // Fetch collections on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchCollections = async ()=>{
            try {
                setIsLoading(true);
                const hierarchyData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$collections$2f$get$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectionGetService"].getHierarchy();
                setCollections(hierarchyData);
            } catch (error) {
                console.error('Failed to fetch collections:', error);
            } finally{
                setIsLoading(false);
            }
        };
        fetchCollections();
    }, []);
    // Handle dropdown toggle
    const handleDropdownToggle = (dropdownName)=>{
        setActiveDropdown(activeDropdown === dropdownName ? null : dropdownName);
    };
    // Handle click outside to close dropdown
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            const target = event.target;
            const isClickInsideDropdown = Object.values(dropdownRefs.current).some((ref)=>ref && ref.contains(target));
            if (!isClickInsideDropdown) {
                setActiveDropdown(null);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    // Convert collections to dropdown items
    const collectionsToDropdownItems = (collections)=>{
        return collections.map((collection)=>({
                label: collection.name,
                href: `/collections/${collection.id}`,
                children: collection.children.length > 0 ? collectionsToDropdownItems(collection.children) : undefined
            }));
    };
    const collectionItems = collectionsToDropdownItems(collections);
    // Check if we're on the home page
    const isHomePage = pathname === '/';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].header} ${isScrolled ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].scrolled : ''} ${!isHomePage ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].nonHomePage : ''}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].container,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logo,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/",
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logoLink,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logoText,
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 128,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logoSubtext,
                                children: "Interiors & Decorations"
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 129,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                        lineNumber: 127,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                    lineNumber: 126,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].nav,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navList,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownContainer,
                                    ref: (el)=>{
                                        dropdownRefs.current['company'] = el;
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navButton} ${activeDropdown === 'company' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: ()=>handleDropdownToggle('company'),
                                            "aria-expanded": activeDropdown === 'company',
                                            children: [
                                                "Company",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownIcon} ${activeDropdown === 'company' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].rotated : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "8",
                                                        viewBox: "0 0 12 8",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M1 1.5L6 6.5L11 1.5",
                                                            stroke: "currentColor",
                                                            strokeWidth: "1.5",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 150,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 149,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 148,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 142,
                                            columnNumber: 17
                                        }, this),
                                        activeDropdown === 'company' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownList,
                                                children: companyItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownItem,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            href: item.href,
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownLink,
                                                            children: item.label
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 159,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, index, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 158,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                lineNumber: 156,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 155,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 138,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 137,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/products",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navLink,
                                    children: "Products"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 172,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 171,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownContainer,
                                    ref: (el)=>{
                                        dropdownRefs.current['collections'] = el;
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navButton} ${activeDropdown === 'collections' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: ()=>handleDropdownToggle('collections'),
                                            "aria-expanded": activeDropdown === 'collections',
                                            disabled: isLoading,
                                            children: [
                                                "Collections",
                                                isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].loadingIcon,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "12",
                                                        viewBox: "0 0 24 24",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                                            cx: "12",
                                                            cy: "12",
                                                            r: "10",
                                                            stroke: "currentColor",
                                                            strokeWidth: "2",
                                                            strokeLinecap: "round",
                                                            strokeDasharray: "31.416",
                                                            strokeDashoffset: "31.416",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("animate", {
                                                                    attributeName: "stroke-dasharray",
                                                                    dur: "2s",
                                                                    values: "0 31.416;15.708 15.708;0 31.416",
                                                                    repeatCount: "indefinite"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                    lineNumber: 194,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("animate", {
                                                                    attributeName: "stroke-dashoffset",
                                                                    dur: "2s",
                                                                    values: "0;-15.708;-31.416",
                                                                    repeatCount: "indefinite"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                    lineNumber: 195,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 193,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 192,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 21
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownIcon} ${activeDropdown === 'collections' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].rotated : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "8",
                                                        viewBox: "0 0 12 8",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M1 1.5L6 6.5L11 1.5",
                                                            stroke: "currentColor",
                                                            strokeWidth: "1.5",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 202,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 201,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 200,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 183,
                                            columnNumber: 17
                                        }, this),
                                        activeDropdown === 'collections' && !isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownList,
                                                children: collectionItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownItem,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                href: item.href,
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownLink,
                                                                children: item.label
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                lineNumber: 212,
                                                                columnNumber: 27
                                                            }, this),
                                                            item.children && item.children.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownList,
                                                                children: item.children.map((child, childIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownItem,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                href: child.href,
                                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownLink,
                                                                                children: child.label
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                                lineNumber: 219,
                                                                                columnNumber: 35
                                                                            }, this),
                                                                            child.children && child.children.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subSubDropdownList,
                                                                                children: child.children.map((grandChild, grandChildIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subSubDropdownItem,
                                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                                            href: grandChild.href,
                                                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subSubDropdownLink,
                                                                                            children: grandChild.label
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                                            lineNumber: 226,
                                                                                            columnNumber: 43
                                                                                        }, this)
                                                                                    }, grandChildIndex, false, {
                                                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                                        lineNumber: 225,
                                                                                        columnNumber: 41
                                                                                    }, this))
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                                lineNumber: 223,
                                                                                columnNumber: 37
                                                                            }, this)
                                                                        ]
                                                                    }, childIndex, true, {
                                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                        lineNumber: 218,
                                                                        columnNumber: 33
                                                                    }, this))
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                                lineNumber: 216,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, index, true, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 211,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                lineNumber: 209,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 208,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 179,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 178,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/completed-projects",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navLink,
                                    children: "Completed Projects"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 247,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 246,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navItem,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownContainer,
                                    ref: (el)=>{
                                        dropdownRefs.current['discover'] = el;
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navButton} ${activeDropdown === 'discover' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ''}`,
                                            onClick: ()=>handleDropdownToggle('discover'),
                                            "aria-expanded": activeDropdown === 'discover',
                                            children: [
                                                "Discover",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownIcon} ${activeDropdown === 'discover' ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].rotated : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                        width: "12",
                                                        height: "8",
                                                        viewBox: "0 0 12 8",
                                                        fill: "none",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                            d: "M1 1.5L6 6.5L11 1.5",
                                                            stroke: "currentColor",
                                                            strokeWidth: "1.5",
                                                            strokeLinecap: "round",
                                                            strokeLinejoin: "round"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 266,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 265,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                    lineNumber: 264,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 258,
                                            columnNumber: 17
                                        }, this),
                                        activeDropdown === 'discover' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdown,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownList,
                                                children: discoverItems.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownItem,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            href: item.href,
                                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].dropdownLink,
                                                            children: item.label
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                            lineNumber: 275,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, index, false, {
                                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                        lineNumber: 274,
                                                        columnNumber: 25
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                                lineNumber: 272,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                                            lineNumber: 271,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 254,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/shared/Header/Header.tsx",
                                lineNumber: 253,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                        lineNumber: 135,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cartContainer,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        href: "/cart",
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cartLink,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cartIconWrapper,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    width: "24",
                                    height: "24",
                                    viewBox: "0 0 24 24",
                                    fill: "none",
                                    stroke: "currentColor",
                                    strokeWidth: "1.5",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                                        lineNumber: 293,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 292,
                                    columnNumber: 15
                                }, this),
                                getCartSummary().totalItems > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Header$2f$header$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cartBadge,
                                    children: getCartSummary().totalItems
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                                    lineNumber: 296,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Header/Header.tsx",
                            lineNumber: 291,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/shared/Header/Header.tsx",
                        lineNumber: 290,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/shared/Header/Header.tsx",
                    lineNumber: 289,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/shared/Header/Header.tsx",
            lineNumber: 124,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/shared/Header/Header.tsx",
        lineNumber: 123,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Header;
}}),
"[project]/src/components/shared/Footer/footer.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "bottom": "footer-module__94Lf8a__bottom",
  "brand": "footer-module__94Lf8a__brand",
  "brandDescription": "footer-module__94Lf8a__brandDescription",
  "brandName": "footer-module__94Lf8a__brandName",
  "contactDetails": "footer-module__94Lf8a__contactDetails",
  "contactInfo": "footer-module__94Lf8a__contactInfo",
  "contactItem": "footer-module__94Lf8a__contactItem",
  "contactLabel": "footer-module__94Lf8a__contactLabel",
  "contactTitle": "footer-module__94Lf8a__contactTitle",
  "contactValue": "footer-module__94Lf8a__contactValue",
  "container": "footer-module__94Lf8a__container",
  "content": "footer-module__94Lf8a__content",
  "copyright": "footer-module__94Lf8a__copyright",
  "footer": "footer-module__94Lf8a__footer",
  "legalLink": "footer-module__94Lf8a__legalLink",
  "legalLinks": "footer-module__94Lf8a__legalLinks",
  "link": "footer-module__94Lf8a__link",
  "linkGroup": "footer-module__94Lf8a__linkGroup",
  "linkGroupTitle": "footer-module__94Lf8a__linkGroupTitle",
  "linkList": "footer-module__94Lf8a__linkList",
  "socialLink": "footer-module__94Lf8a__socialLink",
  "socialLinks": "footer-module__94Lf8a__socialLinks",
});
}}),
"[project]/src/components/shared/Footer/Footer.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/shared/Footer/footer.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Footer = ({ companyName = "Cast Stone" })=>{
    const currentYear = new Date().getFullYear();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    if (pathname.startsWith('/admin')) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].footer,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].container,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].content,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].brand,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].brandName,
                                    children: companyName
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 27,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].brandDescription,
                                    children: "Creating timeless beauty with handcrafted cast stone elements for over 25 years."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 28,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].socialLinks,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].socialLink,
                                            "aria-label": "Facebook",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                width: "20",
                                                height: "20",
                                                viewBox: "0 0 24 24",
                                                fill: "currentColor",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                    lineNumber: 35,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 34,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 33,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].socialLink,
                                            "aria-label": "Instagram",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                width: "20",
                                                height: "20",
                                                viewBox: "0 0 24 24",
                                                fill: "currentColor",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                    lineNumber: 40,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 39,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 38,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "#",
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].socialLink,
                                            "aria-label": "LinkedIn",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                width: "20",
                                                height: "20",
                                                viewBox: "0 0 24 24",
                                                fill: "currentColor",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    d: "M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                    lineNumber: 45,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 44,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 43,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 32,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 26,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkGroupTitle,
                                    children: "Company"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 53,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkList,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/contact",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Contact Us"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 55,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 55,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/our-story",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Our Story"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 56,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 56,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/retail-locator",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Retail Locator"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 57,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 57,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/wholesale-signup",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Wholesale Sign-up"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 58,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 58,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 54,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 52,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkGroupTitle,
                                    children: "Products"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 64,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkList,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/products/architectural",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Architectural Products"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 66,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 66,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/products/designer",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Designer Products"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 67,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 67,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/products/limited-edition",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Limited Edition"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 68,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 68,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/products/sealers",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Cast Stone Sealers"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 69,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 69,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 65,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 63,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkGroupTitle,
                                    children: "Discover"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 75,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].linkList,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/catalog",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Catalog"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 77,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 77,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/collections",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Collections"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 78,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 78,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/completed-projects",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Completed Projects"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 79,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 79,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/videos",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "Videos"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 80,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 80,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/faq",
                                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].link,
                                                children: "FAQs"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                lineNumber: 81,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 81,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 74,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                    lineNumber: 24,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactInfo,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactTitle,
                            children: "Contact Info"
                        }, void 0, false, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 88,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactDetails,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactItem,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactLabel,
                                            children: "Address:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 91,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactValue,
                                            children: [
                                                "123 Artisan Way",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                                    lineNumber: 93,
                                                    columnNumber: 32
                                                }, this),
                                                "Craftsman City, CC 12345"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 92,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 90,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactItem,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactLabel,
                                            children: "Phone:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 98,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactValue,
                                            children: "(*************"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 99,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 97,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactItem,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactLabel,
                                            children: "Email:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 102,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].contactValue,
                                            children: "<EMAIL>"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                            lineNumber: 103,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 101,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 89,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].bottom,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].copyright,
                            children: [
                                "© ",
                                currentYear,
                                " ",
                                companyName,
                                ". All rights reserved."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 109,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].legalLinks,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/privacy",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].legalLink,
                                    children: "Privacy Policy"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/terms",
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$shared$2f$Footer$2f$footer$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].legalLink,
                                    children: "Terms of Service"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                            lineNumber: 112,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/shared/Footer/Footer.tsx",
                    lineNumber: 108,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/shared/Footer/Footer.tsx",
            lineNumber: 23,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/shared/Footer/Footer.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Footer;
}}),
"[project]/src/contexts/WholesaleAuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "WholesaleAuthProvider": (()=>WholesaleAuthProvider),
    "useWholesaleAuth": (()=>useWholesaleAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/auth/index.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const WholesaleAuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function WholesaleAuthProvider({ children }) {
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isApprovedWholesaleBuyer, setIsApprovedWholesaleBuyer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Check for existing session on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const checkExistingSession = async ()=>{
            try {
                const storedSession = localStorage.getItem('wholesale_session');
                if (storedSession) {
                    const sessionData = JSON.parse(storedSession);
                    // Verify the session is still valid
                    if (sessionData.email && sessionData.role === 'WholesaleBuyer') {
                        setUser(sessionData);
                        // Check if user is still approved
                        const isApproved = await checkWholesaleStatus(sessionData.email);
                        setIsApprovedWholesaleBuyer(isApproved);
                        // If user is no longer approved, clear session
                        if (!isApproved) {
                            localStorage.removeItem('wholesale_session');
                            setUser(null);
                        }
                    } else {
                        localStorage.removeItem('wholesale_session');
                    }
                }
            } catch (error) {
                console.error('Error checking existing session:', error);
                localStorage.removeItem('wholesale_session');
            } finally{
                setIsLoading(false);
            }
        };
        checkExistingSession();
    }, []);
    const login = async (email, password)=>{
        setIsLoading(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].login({
                email,
                password
            });
            if (response.success && response.data) {
                const authResult = response.data;
                if (authResult.isValid && authResult.user) {
                    setUser(authResult.user);
                    setIsApprovedWholesaleBuyer(authResult.isApprovedWholesaleBuyer);
                    // Store session in localStorage (in production, use secure httpOnly cookies)
                    localStorage.setItem('wholesale_session', JSON.stringify(authResult.user));
                    return {
                        success: true
                    };
                } else {
                    return {
                        success: false,
                        error: authResult.errorMessage || 'Invalid credentials'
                    };
                }
            }
            return {
                success: false,
                error: response.message || 'Login failed'
            };
        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'An unexpected error occurred'
            };
        } finally{
            setIsLoading(false);
        }
    };
    const logout = ()=>{
        setUser(null);
        setIsApprovedWholesaleBuyer(false);
        localStorage.removeItem('wholesale_session');
    };
    const checkWholesaleStatus = async (email)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].checkWholesaleStatus(email);
            return response.success && response.data === true;
        } catch (error) {
            console.error('Error checking wholesale status:', error);
            return false;
        }
    };
    const refreshUserData = async ()=>{
        if (!user?.email) return;
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].getUserByEmail(user.email);
            if (response.success && response.data) {
                setUser(response.data);
                localStorage.setItem('wholesale_session', JSON.stringify(response.data));
                // Check wholesale status
                const isApproved = await checkWholesaleStatus(user.email);
                setIsApprovedWholesaleBuyer(isApproved);
            }
        } catch (error) {
            console.error('Error refreshing user data:', error);
        }
    };
    const value = {
        user,
        isLoading,
        isApprovedWholesaleBuyer,
        login,
        logout,
        isAuthenticated: !!user,
        checkWholesaleStatus,
        refreshUserData
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(WholesaleAuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/WholesaleAuthContext.tsx",
        lineNumber: 142,
        columnNumber: 5
    }, this);
}
function useWholesaleAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(WholesaleAuthContext);
    if (context === undefined) {
        throw new Error('useWholesaleAuth must be used within a WholesaleAuthProvider');
    }
    return context;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__321fe6ec._.js.map