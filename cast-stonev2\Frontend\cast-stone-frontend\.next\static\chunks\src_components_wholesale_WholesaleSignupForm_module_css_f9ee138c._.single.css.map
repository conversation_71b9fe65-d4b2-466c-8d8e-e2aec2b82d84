{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/wholesale/WholesaleSignupForm.module.css"], "sourcesContent": [".formContainer {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 2rem;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n\n.progressBar {\n  position: relative;\n  margin-bottom: 3rem;\n  padding: 0 2rem;\n}\n\n.progressSteps {\n  display: flex;\n  justify-content: space-between;\n  position: relative;\n  z-index: 2;\n}\n\n.progressStep {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.stepNumber {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: #e5e7eb;\n  color: #6b7280;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  transition: all 0.3s ease;\n}\n\n.progressStep.active .stepNumber {\n  background: #3b82f6;\n  color: white;\n}\n\n.progressStep.completed .stepNumber {\n  background: #10b981;\n  color: white;\n}\n\n.stepLabel {\n  font-size: 0.875rem;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.progressStep.active .stepLabel,\n.progressStep.completed .stepLabel {\n  color: #374151;\n}\n\n.progressLine {\n  position: absolute;\n  top: 20px;\n  left: 2rem;\n  right: 2rem;\n  height: 2px;\n  background: #10b981;\n  transition: width 0.3s ease;\n  z-index: 1;\n}\n\n.progressLine::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background: #e5e7eb;\n  z-index: -1;\n}\n\n.stepContent {\n  margin-bottom: 2rem;\n}\n\n.stepContent h3 {\n  margin-bottom: 1.5rem;\n  color: #1f2937;\n  font-size: 1.5rem;\n  font-weight: 600;\n}\n\n.formGroup {\n  margin-bottom: 1.5rem;\n}\n\n.formGroup label {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #374151;\n  font-weight: 500;\n  font-size: 0.875rem;\n}\n\n.formGroup input,\n.formGroup select,\n.formGroup textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease, box-shadow 0.2s ease;\n}\n\n.formGroup input:focus,\n.formGroup select:focus,\n.formGroup textarea:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.formGroup input.error,\n.formGroup select.error,\n.formGroup textarea.error {\n  border-color: #ef4444;\n}\n\n.formGroup input.error:focus,\n.formGroup select.error:focus,\n.formGroup textarea.error:focus {\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.formRow {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n}\n\n.formRow.triple {\n  grid-template-columns: 1fr 1fr 1fr;\n}\n\n.errorText {\n  display: block;\n  color: #ef4444;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n\n.checkboxGroup {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 0.75rem;\n  margin-top: 0.5rem;\n}\n\n.checkboxLabel {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n  font-size: 0.875rem;\n  color: #374151;\n}\n\n.checkboxLabel input[type=\"checkbox\"] {\n  width: auto;\n  margin: 0;\n}\n\n.formActions {\n  display: flex;\n  justify-content: space-between;\n  gap: 1rem;\n  margin-top: 2rem;\n  padding-top: 2rem;\n  border-top: 1px solid #e5e7eb;\n}\n\n.primaryButton {\n  background: #3b82f6;\n  color: white;\n  border: none;\n  padding: 0.75rem 2rem;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  min-width: 120px;\n}\n\n.primaryButton:hover:not(:disabled) {\n  background: #2563eb;\n}\n\n.primaryButton:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n\n.secondaryButton {\n  background: white;\n  color: #374151;\n  border: 1px solid #d1d5db;\n  padding: 0.75rem 2rem;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  min-width: 120px;\n}\n\n.secondaryButton:hover:not(:disabled) {\n  background: #f9fafb;\n  border-color: #9ca3af;\n}\n\n.secondaryButton:disabled {\n  background: #f9fafb;\n  color: #9ca3af;\n  cursor: not-allowed;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .formContainer {\n    padding: 1.5rem;\n    margin: 1rem;\n  }\n  \n  .progressBar {\n    padding: 0 1rem;\n  }\n  \n  .formRow {\n    grid-template-columns: 1fr;\n  }\n  \n  .formRow.triple {\n    grid-template-columns: 1fr;\n  }\n  \n  .checkboxGroup {\n    grid-template-columns: 1fr;\n  }\n  \n  .formActions {\n    flex-direction: column;\n  }\n  \n  .stepLabel {\n    display: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .formContainer {\n    padding: 1rem;\n    margin: 0.5rem;\n  }\n  \n  .progressSteps {\n    justify-content: center;\n    gap: 2rem;\n  }\n  \n  .stepNumber {\n    width: 32px;\n    height: 32px;\n    font-size: 0.875rem;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;;;;;;;;AAWA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;AAWA;;;;;;AAQA;;;;AAMA;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;AAOA;EACE;;;;;EAKA;;;;EAIA;;;;EAYA;;;;EAIA;;;;;AAKF;EACE;;;;;EAKA;;;;;EAKA"}}]}