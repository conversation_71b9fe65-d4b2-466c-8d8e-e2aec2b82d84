(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/contexts/AdminAuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdminAuthProvider": (()=>AdminAuthProvider),
    "useAdminAuth": (()=>useAdminAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$get$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api/users/get.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AdminAuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AdminAuthProvider({ children }) {
    _s();
    const [admin, setAdmin] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Check for existing session on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AdminAuthProvider.useEffect": ()=>{
            const checkExistingSession = {
                "AdminAuthProvider.useEffect.checkExistingSession": ()=>{
                    try {
                        const storedAdmin = localStorage.getItem('admin_session');
                        if (storedAdmin) {
                            const adminData = JSON.parse(storedAdmin);
                            // Verify the session is still valid (simple check)
                            if (adminData.email && adminData.role === 'admin') {
                                setAdmin(adminData);
                            } else {
                                localStorage.removeItem('admin_session');
                            }
                        }
                    } catch (error) {
                        console.error('Error checking existing session:', error);
                        localStorage.removeItem('admin_session');
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["AdminAuthProvider.useEffect.checkExistingSession"];
            checkExistingSession();
        }
    }["AdminAuthProvider.useEffect"], []);
    const login = async (email, password)=>{
        setIsLoading(true);
        try {
            const adminUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$users$2f$get$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userGetService"].validateAdminCredentials(email, password);
            if (adminUser) {
                setAdmin(adminUser);
                // Store session in localStorage (in production, use secure httpOnly cookies)
                localStorage.setItem('admin_session', JSON.stringify(adminUser));
                return true;
            }
            return false;
        } catch (error) {
            console.error('Login error:', error);
            return false;
        } finally{
            setIsLoading(false);
        }
    };
    const logout = ()=>{
        setAdmin(null);
        localStorage.removeItem('admin_session');
    };
    const value = {
        admin,
        isLoading,
        login,
        logout,
        isAuthenticated: !!admin
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AdminAuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AdminAuthContext.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
_s(AdminAuthProvider, "6t39ZjYaGYsHEMLnBAmfmuK7Cbg=");
_c = AdminAuthProvider;
function useAdminAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AdminAuthContext);
    if (context === undefined) {
        throw new Error('useAdminAuth must be used within an AdminAuthProvider');
    }
    return context;
}
_s1(useAdminAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AdminAuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_contexts_AdminAuthContext_tsx_1ccc6287._.js.map