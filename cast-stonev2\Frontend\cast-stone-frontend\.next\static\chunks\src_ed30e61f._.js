(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/wholesale/WholesaleSignupForm.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "active": "WholesaleSignupForm-module__HafJYa__active",
  "checkboxGroup": "WholesaleSignupForm-module__HafJYa__checkboxGroup",
  "checkboxLabel": "WholesaleSignupForm-module__HafJYa__checkboxLabel",
  "completed": "WholesaleSignupForm-module__HafJYa__completed",
  "error": "WholesaleSignupForm-module__HafJYa__error",
  "errorText": "WholesaleSignupForm-module__HafJYa__errorText",
  "formActions": "WholesaleSignupForm-module__HafJYa__formActions",
  "formContainer": "WholesaleSignupForm-module__HafJYa__formContainer",
  "formGroup": "WholesaleSignupForm-module__HafJYa__formGroup",
  "formRow": "WholesaleSignupForm-module__HafJYa__formRow",
  "primaryButton": "WholesaleSignupForm-module__HafJYa__primaryButton",
  "progressBar": "WholesaleSignupForm-module__HafJYa__progressBar",
  "progressLine": "WholesaleSignupForm-module__HafJYa__progressLine",
  "progressStep": "WholesaleSignupForm-module__HafJYa__progressStep",
  "progressSteps": "WholesaleSignupForm-module__HafJYa__progressSteps",
  "secondaryButton": "WholesaleSignupForm-module__HafJYa__secondaryButton",
  "stepContent": "WholesaleSignupForm-module__HafJYa__stepContent",
  "stepLabel": "WholesaleSignupForm-module__HafJYa__stepLabel",
  "stepNumber": "WholesaleSignupForm-module__HafJYa__stepNumber",
  "triple": "WholesaleSignupForm-module__HafJYa__triple",
});
}}),
"[project]/src/components/wholesale/WholesaleSignupForm.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "WholesaleSignupForm": (()=>WholesaleSignupForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/api/wholesaleBuyers/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/wholesale/WholesaleSignupForm.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const BUSINESS_TYPES = [
    'Landscape Contractor',
    'Landscape Architect',
    'Garden Center/Nursery',
    'Hardscape Contractor',
    'Pool/Spa Contractor',
    'Interior Designer',
    'Architect',
    'General Contractor',
    'Distributor/Dealer',
    'Other'
];
const HOW_DID_YOU_HEAR_OPTIONS = [
    'Google Search',
    'Social Media',
    'Trade Show',
    'Referral from Customer',
    'Referral from Industry Professional',
    'Print Advertisement',
    'Website',
    'Other'
];
const US_STATES = [
    'Alabama',
    'Alaska',
    'Arizona',
    'Arkansas',
    'California',
    'Colorado',
    'Connecticut',
    'Delaware',
    'Florida',
    'Georgia',
    'Hawaii',
    'Idaho',
    'Illinois',
    'Indiana',
    'Iowa',
    'Kansas',
    'Kentucky',
    'Louisiana',
    'Maine',
    'Maryland',
    'Massachusetts',
    'Michigan',
    'Minnesota',
    'Mississippi',
    'Missouri',
    'Montana',
    'Nebraska',
    'Nevada',
    'New Hampshire',
    'New Jersey',
    'New Mexico',
    'New York',
    'North Carolina',
    'North Dakota',
    'Ohio',
    'Oklahoma',
    'Oregon',
    'Pennsylvania',
    'Rhode Island',
    'South Carolina',
    'South Dakota',
    'Tennessee',
    'Texas',
    'Utah',
    'Vermont',
    'Virginia',
    'Washington',
    'West Virginia',
    'Wisconsin',
    'Wyoming'
];
const WholesaleSignupForm = ({ onSuccess, onError })=>{
    _s();
    const [currentStep, setCurrentStep] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        email: '',
        firstName: '',
        lastName: '',
        phone: '',
        companyName: '',
        businessType: '',
        otherBusinessType: '',
        taxNumber: '',
        businessAddress: '',
        state: '',
        city: '',
        zipCode: '',
        country: '',
        howDidYouHear: [],
        otherHowDidYouHear: '',
        comments: '',
        password: '',
        confirmPassword: ''
    });
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const validateStep = (step)=>{
        const newErrors = {};
        if (step === 1) {
            // Personal Information
            if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
            if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
            if (!formData.email.trim()) newErrors.email = 'Email is required';
            else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
            if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
            if (!formData.password) newErrors.password = 'Password is required';
            else if (formData.password.length < 6) newErrors.password = 'Password must be at least 6 characters';
            if (!formData.confirmPassword) newErrors.confirmPassword = 'Please confirm your password';
            else if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match';
        } else if (step === 2) {
            // Business Information
            if (!formData.companyName.trim()) newErrors.companyName = 'Company name is required';
            if (!formData.businessType) newErrors.businessType = 'Business type is required';
            if (formData.businessType === 'Other' && !formData.otherBusinessType?.trim()) {
                newErrors.otherBusinessType = 'Please specify your business type';
            }
            if (!formData.businessAddress.trim()) newErrors.businessAddress = 'Business address is required';
            if (!formData.state) newErrors.state = 'State is required';
            if (!formData.city.trim()) newErrors.city = 'City is required';
            if (!formData.zipCode.trim()) newErrors.zipCode = 'ZIP code is required';
        } else if (step === 3) {
            // Additional Information
            if (formData.howDidYouHear.length === 0) {
                newErrors.howDidYouHear = 'Please select at least one option';
            }
            if (formData.howDidYouHear.includes('Other') && !formData.otherHowDidYouHear?.trim()) {
                newErrors.otherHowDidYouHear = 'Please specify how you heard about us';
            }
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const handleInputChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev)=>({
                    ...prev,
                    [field]: ''
                }));
        }
    };
    const handleCheckboxChange = (option, checked)=>{
        const newHowDidYouHear = checked ? [
            ...formData.howDidYouHear,
            option
        ] : formData.howDidYouHear.filter((item)=>item !== option);
        handleInputChange('howDidYouHear', newHowDidYouHear);
    };
    const nextStep = ()=>{
        if (validateStep(currentStep)) {
            setCurrentStep((prev)=>prev + 1);
        }
    };
    const prevStep = ()=>{
        setCurrentStep((prev)=>prev - 1);
    };
    const handleSubmit = async ()=>{
        if (!validateStep(3)) return;
        setIsSubmitting(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2f$wholesaleBuyers$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["wholesaleBuyerService"].post.submitApplication(formData);
            if (response.success) {
                onSuccess?.();
            } else {
                onError?.(response.message || 'Failed to submit application');
            }
        } catch (error) {
            onError?.(error instanceof Error ? error.message : 'An unexpected error occurred');
        } finally{
            setIsSubmitting(false);
        }
    };
    const renderStep1 = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].stepContent,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    children: "Personal Information"
                }, void 0, false, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 163,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formRow,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "firstName",
                                    children: "First Name *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 167,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "firstName",
                                    value: formData.firstName,
                                    onChange: (e)=>handleInputChange('firstName', e.target.value),
                                    className: errors.firstName ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 168,
                                    columnNumber: 11
                                }, this),
                                errors.firstName && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.firstName
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 175,
                                    columnNumber: 32
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 166,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "lastName",
                                    children: "Last Name *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 179,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "lastName",
                                    value: formData.lastName,
                                    onChange: (e)=>handleInputChange('lastName', e.target.value),
                                    className: errors.lastName ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 180,
                                    columnNumber: 11
                                }, this),
                                errors.lastName && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.lastName
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 187,
                                    columnNumber: 31
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 178,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 165,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "email",
                            children: "Email Address *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 192,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "email",
                            id: "email",
                            value: formData.email,
                            onChange: (e)=>handleInputChange('email', e.target.value),
                            className: errors.email ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 193,
                            columnNumber: 9
                        }, this),
                        errors.email && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.email
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 200,
                            columnNumber: 26
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 191,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "phone",
                            children: "Phone Number *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 204,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "tel",
                            id: "phone",
                            value: formData.phone,
                            onChange: (e)=>handleInputChange('phone', e.target.value),
                            className: errors.phone ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 205,
                            columnNumber: 9
                        }, this),
                        errors.phone && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.phone
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 212,
                            columnNumber: 26
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 203,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formRow,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "password",
                                    children: "Password *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 217,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "password",
                                    id: "password",
                                    value: formData.password,
                                    onChange: (e)=>handleInputChange('password', e.target.value),
                                    className: errors.password ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 218,
                                    columnNumber: 11
                                }, this),
                                errors.password && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.password
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 225,
                                    columnNumber: 31
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 216,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "confirmPassword",
                                    children: "Confirm Password *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 229,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "password",
                                    id: "confirmPassword",
                                    value: formData.confirmPassword,
                                    onChange: (e)=>handleInputChange('confirmPassword', e.target.value),
                                    className: errors.confirmPassword ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 230,
                                    columnNumber: 11
                                }, this),
                                errors.confirmPassword && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.confirmPassword
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 237,
                                    columnNumber: 38
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 228,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 215,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
            lineNumber: 162,
            columnNumber: 5
        }, this);
    const renderStep2 = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].stepContent,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    children: "Business Information"
                }, void 0, false, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 245,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "companyName",
                            children: "Company Name *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 248,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            id: "companyName",
                            value: formData.companyName,
                            onChange: (e)=>handleInputChange('companyName', e.target.value),
                            className: errors.companyName ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 249,
                            columnNumber: 9
                        }, this),
                        errors.companyName && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.companyName
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 256,
                            columnNumber: 32
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 247,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "businessType",
                            children: "Business Type *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 260,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                            id: "businessType",
                            value: formData.businessType,
                            onChange: (e)=>handleInputChange('businessType', e.target.value),
                            className: errors.businessType ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : '',
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: "",
                                    children: "Select Business Type"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 267,
                                    columnNumber: 11
                                }, this),
                                BUSINESS_TYPES.map((type)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: type,
                                        children: type
                                    }, type, false, {
                                        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                        lineNumber: 269,
                                        columnNumber: 13
                                    }, this))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 261,
                            columnNumber: 9
                        }, this),
                        errors.businessType && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.businessType
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 272,
                            columnNumber: 33
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 259,
                    columnNumber: 7
                }, this),
                formData.businessType === 'Other' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "otherBusinessType",
                            children: "Please specify *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 277,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            id: "otherBusinessType",
                            value: formData.otherBusinessType || '',
                            onChange: (e)=>handleInputChange('otherBusinessType', e.target.value),
                            className: errors.otherBusinessType ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 278,
                            columnNumber: 11
                        }, this),
                        errors.otherBusinessType && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.otherBusinessType
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 285,
                            columnNumber: 40
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 276,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "taxNumber",
                            children: "Tax ID Number (Optional)"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 290,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            id: "taxNumber",
                            value: formData.taxNumber || '',
                            onChange: (e)=>handleInputChange('taxNumber', e.target.value)
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 291,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 289,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "businessAddress",
                            children: "Business Address *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 300,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                            id: "businessAddress",
                            value: formData.businessAddress,
                            onChange: (e)=>handleInputChange('businessAddress', e.target.value),
                            className: errors.businessAddress ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : '',
                            rows: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 301,
                            columnNumber: 9
                        }, this),
                        errors.businessAddress && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.businessAddress
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 308,
                            columnNumber: 36
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 299,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formRow,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "state",
                                    children: "State *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 313,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    id: "state",
                                    value: formData.state,
                                    onChange: (e)=>handleInputChange('state', e.target.value),
                                    className: errors.state ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : '',
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: "",
                                            children: "Select State"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                            lineNumber: 320,
                                            columnNumber: 13
                                        }, this),
                                        US_STATES.map((state)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: state,
                                                children: state
                                            }, state, false, {
                                                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                                lineNumber: 322,
                                                columnNumber: 15
                                            }, this))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 314,
                                    columnNumber: 11
                                }, this),
                                errors.state && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.state
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 325,
                                    columnNumber: 28
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 312,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "city",
                                    children: "City *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 329,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "city",
                                    value: formData.city,
                                    onChange: (e)=>handleInputChange('city', e.target.value),
                                    className: errors.city ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 330,
                                    columnNumber: 11
                                }, this),
                                errors.city && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.city
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 337,
                                    columnNumber: 27
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 328,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "zipCode",
                                    children: "ZIP Code *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 341,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    id: "zipCode",
                                    value: formData.zipCode,
                                    onChange: (e)=>handleInputChange('zipCode', e.target.value),
                                    className: errors.zipCode ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 342,
                                    columnNumber: 11
                                }, this),
                                errors.zipCode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.zipCode
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 349,
                                    columnNumber: 30
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 340,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 311,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
            lineNumber: 244,
            columnNumber: 5
        }, this);
    const renderStep3 = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].stepContent,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    children: "Additional Information"
                }, void 0, false, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 357,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            children: "How did you hear about us? *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 360,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkboxGroup,
                            children: HOW_DID_YOU_HEAR_OPTIONS.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].checkboxLabel,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "checkbox",
                                            checked: formData.howDidYouHear.includes(option),
                                            onChange: (e)=>handleCheckboxChange(option, e.target.checked)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                            lineNumber: 364,
                                            columnNumber: 15
                                        }, this),
                                        option
                                    ]
                                }, option, true, {
                                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                    lineNumber: 363,
                                    columnNumber: 13
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 361,
                            columnNumber: 9
                        }, this),
                        errors.howDidYouHear && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.howDidYouHear
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 373,
                            columnNumber: 34
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 359,
                    columnNumber: 7
                }, this),
                formData.howDidYouHear.includes('Other') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "otherHowDidYouHear",
                            children: "Please specify *"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 378,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            id: "otherHowDidYouHear",
                            value: formData.otherHowDidYouHear || '',
                            onChange: (e)=>handleInputChange('otherHowDidYouHear', e.target.value),
                            className: errors.otherHowDidYouHear ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : ''
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 379,
                            columnNumber: 11
                        }, this),
                        errors.otherHowDidYouHear && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                            children: errors.otherHowDidYouHear
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 386,
                            columnNumber: 41
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 377,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            htmlFor: "comments",
                            children: "Additional Comments (Optional)"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 391,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                            id: "comments",
                            value: formData.comments || '',
                            onChange: (e)=>handleInputChange('comments', e.target.value),
                            rows: 4,
                            placeholder: "Tell us more about your business or any specific requirements..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                            lineNumber: 392,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                    lineNumber: 390,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
            lineNumber: 356,
            columnNumber: 5
        }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formContainer,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].progressBar,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].progressSteps,
                        children: [
                            1,
                            2,
                            3
                        ].map((step)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].progressStep} ${step <= currentStep ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].active : ''} ${step < currentStep ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].completed : ''}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].stepNumber,
                                        children: step
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                        lineNumber: 414,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].stepLabel,
                                        children: step === 1 ? 'Personal' : step === 2 ? 'Business' : 'Additional'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                        lineNumber: 415,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, step, true, {
                                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                lineNumber: 408,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                        lineNumber: 406,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].progressLine,
                        style: {
                            width: `${(currentStep - 1) / 2 * 100}%`
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                        lineNumber: 421,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                lineNumber: 405,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: (e)=>e.preventDefault(),
                children: [
                    currentStep === 1 && renderStep1(),
                    currentStep === 2 && renderStep2(),
                    currentStep === 3 && renderStep3(),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formActions,
                        children: [
                            currentStep > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: prevStep,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].secondaryButton,
                                disabled: isSubmitting,
                                children: "Previous"
                            }, void 0, false, {
                                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                lineNumber: 434,
                                columnNumber: 13
                            }, this),
                            currentStep < 3 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: nextStep,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].primaryButton,
                                children: "Next"
                            }, void 0, false, {
                                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                lineNumber: 445,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: handleSubmit,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].primaryButton,
                                disabled: isSubmitting,
                                children: isSubmitting ? 'Submitting...' : 'Submit Application'
                            }, void 0, false, {
                                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                                lineNumber: 453,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                        lineNumber: 432,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
                lineNumber: 427,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/wholesale/WholesaleSignupForm.tsx",
        lineNumber: 404,
        columnNumber: 5
    }, this);
};
_s(WholesaleSignupForm, "yzwdn4bj3tXaDdPvmNDueQzUL4A=");
_c = WholesaleSignupForm;
var _c;
__turbopack_context__.k.register(_c, "WholesaleSignupForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/wholesale/WholesaleLogin.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "error": "WholesaleLogin-module__KgOrfG__error",
  "errorText": "WholesaleLogin-module__KgOrfG__errorText",
  "formGroup": "WholesaleLogin-module__KgOrfG__formGroup",
  "linkButton": "WholesaleLogin-module__KgOrfG__linkButton",
  "loginButton": "WholesaleLogin-module__KgOrfG__loginButton",
  "loginCard": "WholesaleLogin-module__KgOrfG__loginCard",
  "loginContainer": "WholesaleLogin-module__KgOrfG__loginContainer",
  "loginFooter": "WholesaleLogin-module__KgOrfG__loginFooter",
  "loginForm": "WholesaleLogin-module__KgOrfG__loginForm",
  "loginHeader": "WholesaleLogin-module__KgOrfG__loginHeader",
});
}}),
"[project]/src/components/wholesale/WholesaleLogin.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "WholesaleLogin": (()=>WholesaleLogin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WholesaleAuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/WholesaleAuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/wholesale/WholesaleLogin.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const WholesaleLogin = ({ onSuccess, onError, onSwitchToSignup })=>{
    _s();
    const { login } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WholesaleAuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWholesaleAuth"])();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        email: '',
        password: ''
    });
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const validateForm = ()=>{
        const newErrors = {};
        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Email is invalid';
        }
        if (!formData.password) {
            newErrors.password = 'Password is required';
        }
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };
    const handleInputChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors((prev)=>({
                    ...prev,
                    [field]: ''
                }));
        }
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!validateForm()) return;
        setIsSubmitting(true);
        try {
            const result = await login(formData.email, formData.password);
            if (result.success) {
                // Create a mock AuthenticationResult for the onSuccess callback
                const authResult = {
                    isValid: true,
                    user: undefined,
                    isApprovedWholesaleBuyer: true // Will be determined by the context
                };
                onSuccess?.(authResult);
            } else {
                onError?.(result.error || 'Login failed');
            }
        } catch (error) {
            onError?.(error instanceof Error ? error.message : 'An unexpected error occurred');
        } finally{
            setIsSubmitting(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loginContainer,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loginCard,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loginHeader,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            children: "Wholesale Buyer Login"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                            lineNumber: 82,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            children: "Access your wholesale pricing and account"
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                            lineNumber: 83,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                    lineNumber: 81,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSubmit,
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loginForm,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "email",
                                    children: "Email Address"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                    lineNumber: 88,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "email",
                                    id: "email",
                                    value: formData.email,
                                    onChange: (e)=>handleInputChange('email', e.target.value),
                                    className: errors.email ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : '',
                                    placeholder: "Enter your email address",
                                    disabled: isSubmitting
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                    lineNumber: 89,
                                    columnNumber: 13
                                }, this),
                                errors.email && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.email
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                    lineNumber: 98,
                                    columnNumber: 30
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formGroup,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    htmlFor: "password",
                                    children: "Password"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "password",
                                    id: "password",
                                    value: formData.password,
                                    onChange: (e)=>handleInputChange('password', e.target.value),
                                    className: errors.password ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].error : '',
                                    placeholder: "Enter your password",
                                    disabled: isSubmitting
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                    lineNumber: 103,
                                    columnNumber: 13
                                }, this),
                                errors.password && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorText,
                                    children: errors.password
                                }, void 0, false, {
                                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                    lineNumber: 112,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                            lineNumber: 101,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "submit",
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loginButton,
                            disabled: isSubmitting,
                            children: isSubmitting ? 'Signing In...' : 'Sign In'
                        }, void 0, false, {
                            fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                            lineNumber: 115,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                    lineNumber: 86,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].loginFooter,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Don't have a wholesale account?",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: onSwitchToSignup,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].linkButton,
                                disabled: isSubmitting,
                                children: "Apply for Wholesale Access"
                            }, void 0, false, {
                                fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                                lineNumber: 127,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                        lineNumber: 125,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
                    lineNumber: 124,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
            lineNumber: 80,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/wholesale/WholesaleLogin.tsx",
        lineNumber: 79,
        columnNumber: 5
    }, this);
};
_s(WholesaleLogin, "Kr+yy9qa974JI/L0d4pYCUkINv0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WholesaleAuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWholesaleAuth"]
    ];
});
_c = WholesaleLogin;
var _c;
__turbopack_context__.k.register(_c, "WholesaleLogin");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/wholesale-signup/page.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "backToLogin": "page-module__7gVXna__backToLogin",
  "benefits": "page-module__7gVXna__benefits",
  "closeError": "page-module__7gVXna__closeError",
  "contactInfo": "page-module__7gVXna__contactInfo",
  "contentContainer": "page-module__7gVXna__contentContainer",
  "errorBanner": "page-module__7gVXna__errorBanner",
  "formHeader": "page-module__7gVXna__formHeader",
  "header": "page-module__7gVXna__header",
  "headerContent": "page-module__7gVXna__headerContent",
  "mainContent": "page-module__7gVXna__mainContent",
  "messageContainer": "page-module__7gVXna__messageContainer",
  "nextSteps": "page-module__7gVXna__nextSteps",
  "pageContainer": "page-module__7gVXna__pageContainer",
  "pendingIcon": "page-module__7gVXna__pendingIcon",
  "pendingMessage": "page-module__7gVXna__pendingMessage",
  "primaryButton": "page-module__7gVXna__primaryButton",
  "sidebar": "page-module__7gVXna__sidebar",
  "successIcon": "page-module__7gVXna__successIcon",
  "successMessage": "page-module__7gVXna__successMessage",
});
}}),
"[project]/src/app/wholesale-signup/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>WholesaleSignupPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/wholesale/WholesaleSignupForm.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/wholesale/WholesaleLogin.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WholesaleAuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/WholesaleAuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/app/wholesale-signup/page.module.css [app-client] (css module)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function WholesaleSignupPage() {
    _s();
    const [currentView, setCurrentView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('login');
    const [message, setMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const { isApprovedWholesaleBuyer, user, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WholesaleAuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWholesaleAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    // Redirect if user is already logged in and approved
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "WholesaleSignupPage.useEffect": ()=>{
            if (!isLoading && user && isApprovedWholesaleBuyer) {
                router.push('/catalog?wholesale=true');
            }
        }
    }["WholesaleSignupPage.useEffect"], [
        isLoading,
        user,
        isApprovedWholesaleBuyer,
        router
    ]);
    const handleLoginSuccess = async (result)=>{
        // Check the context state for the most up-to-date information
        if (isApprovedWholesaleBuyer && user) {
            // Redirect to catalog or home page with wholesale pricing
            router.push('/catalog?wholesale=true');
        } else if (user && !isApprovedWholesaleBuyer) {
            // Show pending approval message
            setCurrentView('pending');
            setMessage('Your wholesale application is pending approval. You will be notified once approved.');
        } else {
            // Fallback to result data
            if (result.isApprovedWholesaleBuyer) {
                router.push('/catalog?wholesale=true');
            } else {
                setCurrentView('pending');
                setMessage('Your wholesale application is pending approval. You will be notified once approved.');
            }
        }
    };
    const handleLoginError = (error)=>{
        setError(error);
    };
    const handleSignupSuccess = ()=>{
        setCurrentView('success');
        setMessage('Your wholesale application has been submitted successfully! We will review your application and notify you within 2-3 business days.');
        setError('');
    };
    const handleSignupError = (error)=>{
        setError(error);
    };
    const renderHeader = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].headerContent,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        children: "Wholesale Access"
                    }, void 0, false, {
                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                        lineNumber: 64,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Join our wholesale program to access exclusive pricing and benefits"
                    }, void 0, false, {
                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/wholesale-signup/page.tsx",
            lineNumber: 62,
            columnNumber: 5
        }, this);
    const renderBenefits = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].benefits,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                    children: "Wholesale Benefits"
                }, void 0, false, {
                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                    lineNumber: 72,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Exclusive wholesale pricing on all products"
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 74,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Priority customer support"
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 75,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Access to new products before general release"
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 76,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Dedicated account manager"
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 77,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Flexible payment terms"
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 78,
                            columnNumber: 9
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                            children: "Volume discounts available"
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 79,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                    lineNumber: 73,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/wholesale-signup/page.tsx",
            lineNumber: 71,
            columnNumber: 5
        }, this);
    const renderContent = ()=>{
        switch(currentView){
            case 'login':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].contentContainer,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mainContent,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleLogin$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WholesaleLogin"], {
                                onSuccess: handleLoginSuccess,
                                onError: handleLoginError,
                                onSwitchToSignup: ()=>{
                                    setCurrentView('signup');
                                    setError('');
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 90,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 89,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sidebar,
                            children: renderBenefits()
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 99,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                    lineNumber: 88,
                    columnNumber: 11
                }, this);
            case 'signup':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].contentContainer,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mainContent,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].formHeader,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            children: "Apply for Wholesale Access"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                            lineNumber: 110,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "Fill out the form below to apply for wholesale pricing"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                            lineNumber: 111,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>{
                                                setCurrentView('login');
                                                setError('');
                                            },
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].backToLogin,
                                            children: "← Back to Login"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                            lineNumber: 112,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                    lineNumber: 109,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$wholesale$2f$WholesaleSignupForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WholesaleSignupForm"], {
                                    onSuccess: handleSignupSuccess,
                                    onError: handleSignupError
                                }, void 0, false, {
                                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                    lineNumber: 122,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 108,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].sidebar,
                            children: renderBenefits()
                        }, void 0, false, {
                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                            lineNumber: 127,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                    lineNumber: 107,
                    columnNumber: 11
                }, this);
            case 'success':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].messageContainer,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].successMessage,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].successIcon,
                                children: "✓"
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 137,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                children: "Application Submitted!"
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 138,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: message
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 139,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].nextSteps,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        children: "What happens next?"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                        lineNumber: 141,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "We'll review your application within 2-3 business days"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                                lineNumber: 143,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "You'll receive an email notification with our decision"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                                lineNumber: 144,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: "Once approved, you can log in to access wholesale pricing"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                                lineNumber: 145,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                        lineNumber: 142,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 140,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>{
                                    setCurrentView('login');
                                    setMessage('');
                                },
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].primaryButton,
                                children: "Back to Login"
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 148,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                        lineNumber: 136,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                    lineNumber: 135,
                    columnNumber: 11
                }, this);
            case 'pending':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].messageContainer,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].pendingMessage,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].pendingIcon,
                                children: "⏳"
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 165,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                children: "Application Pending"
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 166,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: message
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 167,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].contactInfo,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        "If you have any questions, please contact us at",
                                        ' ',
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "mailto:<EMAIL>",
                                            children: "<EMAIL>"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                            lineNumber: 171,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                    lineNumber: 169,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 168,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>{
                                    setCurrentView('login');
                                    setMessage('');
                                },
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].primaryButton,
                                children: "Back to Login"
                            }, void 0, false, {
                                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                                lineNumber: 174,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                        lineNumber: 164,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/wholesale-signup/page.tsx",
                    lineNumber: 163,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].pageContainer,
        children: [
            renderHeader(),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].errorBanner,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: error
                    }, void 0, false, {
                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                        lineNumber: 198,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>setError(''),
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$wholesale$2d$signup$2f$page$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].closeError,
                        children: "×"
                    }, void 0, false, {
                        fileName: "[project]/src/app/wholesale-signup/page.tsx",
                        lineNumber: 199,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/wholesale-signup/page.tsx",
                lineNumber: 197,
                columnNumber: 9
            }, this),
            renderContent()
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/wholesale-signup/page.tsx",
        lineNumber: 193,
        columnNumber: 5
    }, this);
}
_s(WholesaleSignupPage, "evWeSHGMnwI6FvTJIdTCEKdp3Sg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WholesaleAuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWholesaleAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = WholesaleSignupPage;
var _c;
__turbopack_context__.k.register(_c, "WholesaleSignupPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_ed30e61f._.js.map